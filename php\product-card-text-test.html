<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Card 文字样式测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #212529;
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        /* 当前的 product-card 样式（有问题的版本） */
        .product-card-current {
            background: #ffffff !important;
            color: #ffffff !important; /* 这里有问题 - 白色文字在白色背景上 */
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            overflow: hidden !important;
            position: relative !important;
            margin-bottom: 20px;
        }

        .product-card-current:hover {
            color: #ffffff !important; /* 悬停时也是白色 */
            text-decoration: none !important;
            transform: none !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        /* 优化后的 product-card 样式 */
        .product-card-optimized {
            background: #ffffff !important;
            color: #212529 !important; /* 修复：深色文字 */
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            overflow: hidden !important;
            position: relative !important;
            margin-bottom: 20px;
        }

        .product-card-optimized:hover {
            color: #212529 !important; /* 悬停时保持深色 */
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        /* 商品信息区域 */
        .product-info {
            background: #ffffff !important;
            padding: 20px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: space-between !important;
            min-height: 140px !important;
            box-sizing: border-box !important;
        }

        /* 当前有问题的文字样式 */
        .product-card-current .product-info * {
            color: inherit !important; /* 继承白色，导致不可见 */
        }

        .product-card-current .product-info h3 {
            color: #ffffff !important; /* 白色标题 */
        }

        .product-card-current .product-info p {
            color: #ffffff !important; /* 白色段落 */
        }

        .product-card-current .product-info span {
            color: #ffffff !important; /* 白色span */
        }

        /* 优化后的文字样式 */
        .product-card-optimized .product-info * {
            color: inherit !important;
        }

        .product-card-optimized .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin: 0 0 8px 0 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-card-optimized .product-origin {
            color: #6c757d !important;
            font-size: 0.9rem !important;
            margin: 0 0 12px 0 !important;
            font-weight: 500 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        .product-card-optimized .product-price {
            color: #212529 !important;
            font-weight: 800 !important;
            font-size: 1.25rem !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-card-optimized .product-price-original {
            color: #8e9ba8 !important;
            text-decoration: line-through !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
        }

        /* 共同样式 */
        .product-image-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-badge {
            background-color: #007bff;
            color: #ffffff;
            font-weight: 600;
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            position: absolute;
            top: 12px;
            left: 12px;
            z-index: 2;
        }

        .product-badge.sale {
            background-color: #ffc107;
            color: #212529;
            right: 12px;
            left: auto;
        }

        .product-price-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: auto;
            padding-top: 8px;
            gap: 8px;
        }

        .product-price-container {
            display: flex;
            align-items: baseline;
            gap: 8px;
            flex: 1;
        }

        .add-to-cart {
            background-color: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .add-to-cart:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        .add-to-cart i {
            color: #ffffff;
            font-size: 14px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-item {
            max-width: 300px;
        }

        .comparison-label {
            font-weight: 600;
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .current-label {
            background-color: #f8d7da;
            color: #721c24;
        }

        .optimized-label {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Product Card 文字样式问题分析与优化</h1>

        <!-- 问题分析 -->
        <div class="test-section">
            <h2 class="section-title">问题分析</h2>
            <p><strong>当前问题：</strong></p>
            <ul>
                <li>product-card 设置了 <code>color: #ffffff</code>（白色文字）</li>
                <li>product-card 背景是 <code>background: #ffffff</code>（白色背景）</li>
                <li>白色文字在白色背景上导致文字不可见</li>
                <li>product-info 内的所有元素继承了白色文字颜色</li>
                <li>虽然有一些强制颜色设置，但存在优先级冲突</li>
            </ul>
        </div>

        <!-- 对比测试 -->
        <div class="test-section">
            <h2 class="section-title">样式对比测试</h2>
            
            <div class="comparison-grid">
                <!-- 当前有问题的样式 -->
                <div class="comparison-item">
                    <div class="comparison-label current-label">❌ 当前样式（有问题）</div>
                    <a href="#" class="product-card-current">
                        <div class="product-image-container">
                            <div class="product-badge sale">特价</div>
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop" 
                                 alt="测试商品" class="product-image">
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                            <p class="product-origin">原神</p>
                            <div class="product-price-row">
                                <div class="product-price-container">
                                    <span class="product-price">¥499.00</span>
                                    <span class="product-price-original">¥599.00</span>
                                </div>
                                <button class="add-to-cart" title="添加到购物车">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- 优化后的样式 -->
                <div class="comparison-item">
                    <div class="comparison-label optimized-label">✅ 优化后样式</div>
                    <a href="#" class="product-card-optimized">
                        <div class="product-image-container">
                            <div class="product-badge sale">特价</div>
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop" 
                                 alt="测试商品" class="product-image">
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                            <p class="product-origin">原神</p>
                            <div class="product-price-row">
                                <div class="product-price-container">
                                    <span class="product-price">¥499.00</span>
                                    <span class="product-price-original">¥599.00</span>
                                </div>
                                <button class="add-to-cart" title="添加到购物车">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- 修复方案 -->
        <div class="test-section">
            <h2 class="section-title">修复方案</h2>
            <p><strong>需要修改的CSS：</strong></p>
            <ol>
                <li>将 <code>.product-card</code> 的 <code>color</code> 从 <code>#ffffff</code> 改为 <code>#212529</code></li>
                <li>将 <code>.product-card:hover</code> 的 <code>color</code> 也改为 <code>#212529</code></li>
                <li>确保所有文字元素都有正确的颜色设置</li>
                <li>移除冲突的颜色继承规则</li>
                <li>添加更具体的选择器确保样式优先级</li>
            </ol>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== Product Card 文字样式测试 ===');
            
            // 检查当前样式的文字可见性
            const currentCard = document.querySelector('.product-card-current');
            const optimizedCard = document.querySelector('.product-card-optimized');
            
            if (currentCard) {
                const currentStyles = window.getComputedStyle(currentCard);
                console.log('当前卡片样式:', {
                    color: currentStyles.color,
                    backgroundColor: currentStyles.backgroundColor
                });
                
                const currentTitle = currentCard.querySelector('.product-name');
                if (currentTitle) {
                    const titleStyles = window.getComputedStyle(currentTitle);
                    console.log('当前标题样式:', {
                        color: titleStyles.color,
                        visibility: titleStyles.visibility,
                        opacity: titleStyles.opacity
                    });
                }
            }
            
            if (optimizedCard) {
                const optimizedStyles = window.getComputedStyle(optimizedCard);
                console.log('优化后卡片样式:', {
                    color: optimizedStyles.color,
                    backgroundColor: optimizedStyles.backgroundColor
                });
                
                const optimizedTitle = optimizedCard.querySelector('.product-name');
                if (optimizedTitle) {
                    const titleStyles = window.getComputedStyle(optimizedTitle);
                    console.log('优化后标题样式:', {
                        color: titleStyles.color,
                        visibility: titleStyles.visibility,
                        opacity: titleStyles.opacity
                    });
                }
            }
        });
    </script>
</body>
</html>
