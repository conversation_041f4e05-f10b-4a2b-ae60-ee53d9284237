/**
 * COSPlay购物网站 - 主要样式文件
 * 包含基础样式、布局、导航栏、按钮等核心样式
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 颜色主题 */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* 中性色 */
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* 字体 */
    --font-family-base: 'Nunito', 'Microsoft YaHei', sans-serif;
    --font-family-heading: 'Poppins', 'Microsoft YaHei', sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 14px;
    --font-size-lg: 18px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* 边框 */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-width: 1px;
    --border-color: #dee2e6;

    /* 阴影 */
    --box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --box-shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.12);
    --box-shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.15);

    /* 过渡动画 */
    --transition-fast: 0.15s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* 布局 */
    --container-max-width: 1200px;
    --navbar-height: 80px;
    --navbar-padding: 16px 0;
    --sidebar-width: 280px;
}

/* ===== 基础重置样式 ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: var(--font-size-base);
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-base);
    font-weight: var(--font-weight-normal);
    color: var(--gray-800);
    background-color: var(--gray-100);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 链接样式 ===== */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== 标题样式 ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-semibold);
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

/* ===== 段落和文本 ===== */
p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.text-muted {
    color: var(--gray-600);
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

/* ===== 容器和布局 ===== */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-md));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-md);
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 12px 24px;
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: var(--border-width) solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    user-select: none;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover:not(:disabled) {
    background-color: #545b62;
    border-color: #545b62;
    color: var(--white);
    transform: translateY(-2px);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--white);
}

.btn-success:hover:not(:disabled) {
    background-color: #218838;
    border-color: #218838;
    color: var(--white);
    transform: translateY(-2px);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
    border-color: #c82333;
    color: var(--white);
    transform: translateY(-2px);
}

.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* 按钮尺寸 */
.btn-sm {
    padding: 8px 16px;
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: 16px 32px;
    font-size: var(--font-size-lg);
}

/* ===== 表单样式 ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
}

.form-control {
    display: block;
    width: 100%;
    padding: 12px 16px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--gray-800);
    background-color: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control::placeholder {
    color: var(--gray-500);
}

/* ===== 导航栏样式 ===== */
.navbar {
    background-color: var(--white);
    border-bottom: var(--border-width) solid var(--border-color);
    box-shadow: var(--box-shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: var(--navbar-height);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: var(--navbar-padding);
    gap: var(--spacing-lg);
}

.logo {
    font-family: var(--font-family-heading);
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.logo span {
    color: var(--danger-color);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-link-icon {
    font-size: 1.1rem;
}

.nav-link-text {
    font-weight: var(--font-weight-medium);
}

.nav-link-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: var(--danger-color);
    color: var(--white);
    font-size: 10px;
    font-weight: var(--font-weight-bold);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* ===== 搜索框样式 ===== */
.search-container {
    position: relative;
    flex: 1;
    max-width: 500px;
}

.search-container form {
    display: flex;
    position: relative;
}

.search-input {
    flex: 1;
    padding: 12px 50px 12px 16px;
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

/* ===== 移动端菜单 ===== */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.menu-toggle:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

/* ===== 无障碍跳转链接 ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius);
    z-index: 1001;
    transition: var(--transition);
}

.skip-link:focus {
    top: 6px;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .navbar-container {
        gap: var(--spacing-md);
    }

    .search-container {
        max-width: 300px;
    }

    .nav-links {
        gap: var(--spacing-md);
    }

    .nav-link-text {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .search-container {
        max-width: 200px;
    }

    .search-input {
        padding: 10px 40px 10px 12px;
        font-size: var(--font-size-sm);
    }

    .nav-link {
        padding: var(--spacing-xs);
    }
}