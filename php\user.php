<?php
/**
 * COSPlay购物网站 - 用户中心页面
 * 显示用户信息、订单历史和账户管理
 */

// 启动输出缓冲
ob_start();

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查用户是否已登录
if (!isset($_SESSION['user'])) {
    ob_end_clean();
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// 获取当前用户信息
$current_user = $_SESSION['user'];

// 页面信息设置
$page_title = '用户中心 - COSPlay购物网站';
$page_description = '管理您的账户信息、查看订单历史和个人设置';

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.user-name {
    color: #212529 !important;
}

.user-email {
    color: #6c757d !important;
}

.user-level span {
    color: #007bff !important;
}

.section-title {
    color: #212529 !important;
}

.stat-label {
    color: #6c757d !important;
}

.stat-number {
    color: #212529 !important;
}

.product-name {
    color: #212529 !important;
}

.product-origin {
    color: #6c757d !important;
}

.current-price {
    color: #007bff !important;
}

.original-price {
    color: #6c757d !important;
}

.activity-title {
    color: #212529 !important;
}

.activity-desc {
    color: #495057 !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.action-btn, .quick-add-btn, .activity-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.nav-tab {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

.nav-tab.active {
    color: #007bff !important;
    background-color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保卡片背景和文字可见 */
.user-profile-card, .overview-stat-card, .recommendation-card, .activity-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.quick-stats, .quick-actions, .user-nav-tabs {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* 确保表单文字可见 */
.form-input {
    color: #495057 !important;
    background-color: #ffffff !important;
}

.form-label {
    color: #212529 !important;
}

/* 确保链接文字可见 */
.view-all-link, .action-link {
    color: #007bff !important;
}

.view-all-link:hover, .action-link:hover {
    color: #0056b3 !important;
}
</style>

<div class="user-center-page">
    <div class="container">
        <div class="user-layout">
            <!-- 主要内容区域 -->
            <div class="user-content">
                <!-- 用户头部信息栏 -->
                <div class="user-header">
                    <!-- 用户资料卡片 -->
                    <div class="user-profile-card">
                        <div class="profile-background">
                            <div class="profile-pattern"></div>
                        </div>
                        <div class="profile-content">
                            <div class="user-avatar">
                                <?php if ($current_user['avatar']): ?>
                                    <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>" alt="用户头像">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="avatar-badge <?php echo $current_user['role']; ?>">
                                    <?php
                                    $role_icons = [
                                        'admin' => 'fas fa-crown',
                                        'vip' => 'fas fa-star',
                                        'user' => 'fas fa-user'
                                    ];
                                    echo '<i class="' . ($role_icons[$current_user['role']] ?? 'fas fa-user') . '"></i>';
                                    ?>
                                </div>
                                <div class="avatar-status online"></div>
                            </div>

                            <div class="user-info">
                                <h3 class="user-name"><?php echo htmlspecialchars($current_user['nickname'] ?? $current_user['username'] ?? '用户'); ?></h3>
                                <div class="user-level">
                                    <?php
                                    $role_names = [
                                        'admin' => '管理员',
                                        'vip' => 'VIP会员',
                                        'user' => '普通会员'
                                    ];
                                    $role_name = $role_names[$current_user['role']] ?? '普通会员';
                                    $role_icon = $role_icons[$current_user['role']] ?? 'fas fa-user';
                                    ?>
                                    <i class="<?php echo $role_icon; ?>"></i>
                                    <span><?php echo $role_name; ?></span>
                                </div>
                                <p class="user-email"><?php echo htmlspecialchars($current_user['email']); ?></p>

                                <!-- 会员等级进度条 -->
                                <div class="member-progress">
                                    <div class="progress-info">
                                        <span class="progress-label">会员等级</span>
                                        <span class="progress-value">LV.3</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 65%"></div>
                                    </div>
                                    <p class="progress-text">再消费 ¥350 升级到 LV.4</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速统计 -->
                    <div class="quick-stats">
                        <div class="stat-item" data-target="orders">
                            <div class="stat-icon orders">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number" data-count="12">12</span>
                                <span class="stat-label">订单</span>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+2</span>
                            </div>
                        </div>
                        <div class="stat-item" data-target="wishlist">
                            <div class="stat-icon wishlist">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number" data-count="8">8</span>
                                <span class="stat-label">心愿</span>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+1</span>
                            </div>
                        </div>
                        <div class="stat-item" data-target="coupons">
                            <div class="stat-icon coupons">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-number" data-count="3">3</span>
                                <span class="stat-label">优惠券</span>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-down"></i>
                                <span>-1</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <h4 class="quick-actions-title">快捷操作</h4>
                        <div class="action-buttons">
                            <button class="action-btn" data-action="reorder">
                                <i class="fas fa-redo"></i>
                                <span>再次购买</span>
                            </button>
                            <button class="action-btn" data-action="track">
                                <i class="fas fa-truck"></i>
                                <span>物流查询</span>
                            </button>
                            <button class="action-btn" data-action="service">
                                <i class="fas fa-headset"></i>
                                <span>客服咨询</span>
                            </button>
                            <button class="action-btn" data-action="feedback">
                                <i class="fas fa-comment-alt"></i>
                                <span>意见反馈</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 导航标签页 -->
                <div class="user-nav-tabs">
                    <div class="nav-tabs-container">
                        <div class="nav-tabs-scroll">
                            <a href="#overview" class="nav-tab active" data-section="overview">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>概览</span>
                            </a>
                            <a href="#orders" class="nav-tab" data-section="orders">
                                <i class="fas fa-shopping-bag"></i>
                                <span>我的订单</span>
                                <span class="tab-badge">2</span>
                            </a>
                            <a href="#wishlist" class="nav-tab" data-section="wishlist">
                                <i class="fas fa-heart"></i>
                                <span>心愿单</span>
                            </a>
                            <a href="#addresses" class="nav-tab" data-section="addresses">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>收货地址</span>
                            </a>
                            <a href="#coupons" class="nav-tab" data-section="coupons">
                                <i class="fas fa-ticket-alt"></i>
                                <span>优惠券</span>
                                <span class="tab-badge">3</span>
                            </a>
                            <a href="#profile" class="nav-tab" data-section="profile">
                                <i class="fas fa-user-edit"></i>
                                <span>个人资料</span>
                            </a>
                            <a href="#security" class="nav-tab" data-section="security">
                                <i class="fas fa-shield-alt"></i>
                                <span>账户安全</span>
                            </a>
                        </div>
                        <div class="nav-tabs-indicator"></div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="user-main-content">
                <!-- 概览页面 -->
                <div class="content-section active" id="overview">
                    <div class="welcome-message">
                        <div class="welcome-content">
                            <div class="welcome-text">
                                <h2>欢迎回来，<?php echo htmlspecialchars($current_user['nickname'] ?? $current_user['username'] ?? '用户'); ?>！</h2>
                                <p>
                                    您是我们尊贵的<?php echo $role_names[$current_user['role']] ?? '会员'; ?>，
                                    <?php if (isset($_SESSION['login_time'])): ?>
                                        本次登录时间：<?php echo date('Y-m-d H:i', $_SESSION['login_time']); ?>
                                    <?php endif; ?>
                                </p>
                                <div class="welcome-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-calendar-check"></i>
                                        注册时间：2023-06-15
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-clock"></i>
                                        在线时长：2小时30分
                                    </span>
                                </div>
                            </div>
                            <div class="welcome-weather">
                                <div class="weather-info">
                                    <i class="fas fa-sun"></i>
                                    <span class="weather-temp">22°C</span>
                                    <span class="weather-desc">晴朗</span>
                                </div>
                                <p class="weather-tip">今天天气不错，适合外出购物！</p>
                            </div>
                        </div>
                    </div>

                    <!-- 今日推荐 -->
                    <div class="daily-recommendations">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-star"></i>
                                今日为您推荐
                            </h3>
                            <button class="refresh-btn" title="刷新推荐">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="recommendation-cards">
                            <div class="recommendation-card">
                                <div class="card-image">
                                    <img src="images/product-1.jpg" alt="推荐商品">
                                    <div class="discount-badge">-20%</div>
                                </div>
                                <div class="card-content">
                                    <h4 class="product-name">刻晴 - 星霜华裳</h4>
                                    <p class="product-origin">原神</p>
                                    <div class="price-info">
                                        <span class="current-price">¥399</span>
                                        <span class="original-price">¥499</span>
                                    </div>
                                    <button class="quick-add-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        快速加购
                                    </button>
                                </div>
                            </div>
                            <div class="recommendation-card">
                                <div class="card-image">
                                    <img src="images/product-2.jpg" alt="推荐商品">
                                    <div class="hot-badge">HOT</div>
                                </div>
                                <div class="card-content">
                                    <h4 class="product-name">宝可梦 - 皮卡丘</h4>
                                    <p class="product-origin">宝可梦</p>
                                    <div class="price-info">
                                        <span class="current-price">¥299</span>
                                    </div>
                                    <button class="quick-add-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        快速加购
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section-header">
                        <h2 class="section-title">账户概览</h2>
                        <div class="overview-actions">
                            <button class="action-link" data-action="export-data">
                                <i class="fas fa-download"></i>
                                导出数据
                            </button>
                            <button class="action-link" data-action="print-summary">
                                <i class="fas fa-print"></i>
                                打印报告
                            </button>
                        </div>
                    </div>

                    <!-- 数据概览卡片 -->
                    <div class="overview-dashboard">
                        <div class="dashboard-row">
                            <div class="overview-stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon orders">
                                        <i class="fas fa-shopping-bag"></i>
                                    </div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+15%</span>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" data-count="12">12</div>
                                    <div class="stat-label">总订单</div>
                                    <div class="stat-detail">本月新增 2 个订单</div>
                                </div>
                                <div class="stat-chart">
                                    <div class="mini-chart">
                                        <div class="chart-bar" style="height: 60%"></div>
                                        <div class="chart-bar" style="height: 80%"></div>
                                        <div class="chart-bar" style="height: 45%"></div>
                                        <div class="chart-bar" style="height: 90%"></div>
                                        <div class="chart-bar" style="height: 75%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="overview-stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon spending">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+8%</span>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">¥2,580</div>
                                    <div class="stat-label">累计消费</div>
                                    <div class="stat-detail">距离下一等级还需 ¥420</div>
                                </div>
                                <div class="stat-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 86%"></div>
                                    </div>
                                    <div class="progress-text">86% 完成</div>
                                </div>
                            </div>

                            <div class="overview-stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon wishlist">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>0%</span>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" data-count="8">8</div>
                                    <div class="stat-label">心愿单</div>
                                    <div class="stat-detail">3 个商品有折扣</div>
                                </div>
                                <div class="stat-actions">
                                    <button class="quick-action-btn" data-action="view-wishlist">
                                        <i class="fas fa-eye"></i>
                                        查看
                                    </button>
                                </div>
                            </div>

                            <div class="overview-stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon coupons">
                                        <i class="fas fa-ticket-alt"></i>
                                    </div>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-1</span>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" data-count="3">3</div>
                                    <div class="stat-label">可用优惠券</div>
                                    <div class="stat-detail">1 张即将过期</div>
                                </div>
                                <div class="stat-actions">
                                    <button class="quick-action-btn urgent" data-action="use-coupon">
                                        <i class="fas fa-clock"></i>
                                        立即使用
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 活动中心 -->
                    <div class="activity-center">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-fire"></i>
                                活动中心
                            </h3>
                            <a href="#" class="view-all-link">查看全部</a>
                        </div>
                        <div class="activity-cards">
                            <div class="activity-card featured">
                                <div class="activity-badge">限时</div>
                                <div class="activity-content">
                                    <h4 class="activity-title">新年大促销</h4>
                                    <p class="activity-desc">全场商品8折起，满500减100</p>
                                    <div class="activity-time">
                                        <i class="fas fa-clock"></i>
                                        还剩 2天 14小时
                                    </div>
                                </div>
                                <button class="activity-btn">立即参与</button>
                            </div>
                            <div class="activity-card">
                                <div class="activity-content">
                                    <h4 class="activity-title">会员专享</h4>
                                    <p class="activity-desc">VIP会员额外9折优惠</p>
                                </div>
                                <button class="activity-btn">了解详情</button>
                            </div>
                            <div class="activity-card">
                                <div class="activity-content">
                                    <h4 class="activity-title">邀请有礼</h4>
                                    <p class="activity-desc">邀请好友注册送优惠券</p>
                                </div>
                                <button class="activity-btn">立即邀请</button>
                            </div>
                        </div>
                    </div>

                    <!-- 最近订单 -->
                    <div class="recent-orders">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-history"></i>
                                最近订单
                                <span class="order-count-badge">3</span>
                            </h3>
                            <div class="recent-order-controls">
                                <div class="order-filter-tabs">
                                    <button class="filter-tab active" data-filter="all">
                                        <i class="fas fa-list"></i>
                                        全部
                                    </button>
                                    <button class="filter-tab" data-filter="pending">
                                        <i class="fas fa-clock"></i>
                                        待处理
                                        <span class="tab-count">1</span>
                                    </button>
                                    <button class="filter-tab" data-filter="shipped">
                                        <i class="fas fa-truck"></i>
                                        配送中
                                        <span class="tab-count">1</span>
                                    </button>
                                </div>
                                <a href="#orders" class="view-all-link" data-section="orders">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看全部
                                </a>
                            </div>
                        </div>

                        <!-- 订单统计概览 -->
                        <div class="recent-order-stats">
                            <div class="order-stat-item">
                                <div class="stat-icon pending">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number">1</span>
                                    <span class="stat-label">待付款</span>
                                </div>
                                <div class="stat-action">
                                    <button class="quick-pay-btn" data-action="quick-pay">
                                        <i class="fas fa-credit-card"></i>
                                        立即付款
                                    </button>
                                </div>
                            </div>

                            <div class="order-stat-item">
                                <div class="stat-icon shipped">
                                    <i class="fas fa-shipping-fast"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number">1</span>
                                    <span class="stat-label">配送中</span>
                                </div>
                                <div class="stat-action">
                                    <button class="track-btn" data-action="track-all">
                                        <i class="fas fa-map-marker-alt"></i>
                                        查看物流
                                    </button>
                                </div>
                            </div>

                            <div class="order-stat-item">
                                <div class="stat-icon delivered">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number">1</span>
                                    <span class="stat-label">已完成</span>
                                </div>
                                <div class="stat-action">
                                    <button class="review-btn" data-action="review-all">
                                        <i class="fas fa-star"></i>
                                        去评价
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="recent-order-list">
                            <div class="recent-order-item" data-order-id="COS2024011501" data-status="delivered">
                                <div class="order-image-container">
                                    <img src="images/product-1.jpg" alt="商品图片" class="order-image">
                                    <div class="order-quantity">×1</div>
                                    <div class="order-category-tag">原神</div>
                                </div>
                                <div class="order-info">
                                    <div class="order-header-info">
                                        <h4 class="order-product">刻晴 - 星霜华裳限定版</h4>
                                        <div class="order-rating">
                                            <div class="stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                            <span class="rating-score">4.9</span>
                                        </div>
                                    </div>
                                    <p class="order-details">
                                        <span class="detail-item">
                                            <i class="fas fa-ruler"></i>
                                            尺码: M
                                        </span>
                                        <span class="detail-item">
                                            <i class="fas fa-palette"></i>
                                            颜色: 紫色
                                        </span>
                                        <span class="detail-item">
                                            <i class="fas fa-tag"></i>
                                            限定版
                                        </span>
                                    </p>
                                    <div class="order-meta">
                                        <span class="order-number">
                                            <i class="fas fa-receipt"></i>
                                            COS2024011501
                                        </span>
                                        <span class="order-date">
                                            <i class="fas fa-calendar"></i>
                                            2024-01-15 14:30
                                        </span>
                                        <span class="delivery-time">
                                            <i class="fas fa-clock"></i>
                                            3天前送达
                                        </span>
                                    </div>
                                </div>
                                <div class="order-status">
                                    <span class="status-badge delivered">
                                        <i class="fas fa-check-circle"></i>
                                        已送达
                                    </span>
                                    <div class="delivery-info">
                                        <div class="delivery-detail">
                                            <i class="fas fa-truck"></i>
                                            <span>已签收 - 本人签收</span>
                                        </div>
                                        <div class="delivery-location">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>北京市朝阳区</span>
                                        </div>
                                    </div>
                                    <div class="order-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-text">订单已完成</span>
                                    </div>
                                </div>
                                <div class="order-price">
                                    <div class="price-main">¥499</div>
                                    <div class="price-original">¥599</div>
                                    <div class="price-detail">含运费 ¥15</div>
                                    <div class="price-save">省¥100</div>
                                </div>
                                <div class="order-actions">
                                    <button class="action-btn primary" data-action="review" data-order-id="COS2024011501">
                                        <i class="fas fa-star"></i>
                                        <span>评价商品</span>
                                    </button>
                                    <button class="action-btn secondary" data-action="reorder" data-product-id="201">
                                        <i class="fas fa-redo"></i>
                                        <span>再次购买</span>
                                    </button>
                                    <div class="action-dropdown">
                                        <button class="action-btn text dropdown-toggle" data-toggle="dropdown">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <span>更多</span>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a href="#" class="dropdown-item" data-action="details">
                                                <i class="fas fa-eye"></i>
                                                查看详情
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="invoice">
                                                <i class="fas fa-file-invoice"></i>
                                                查看发票
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="share">
                                                <i class="fas fa-share-alt"></i>
                                                分享商品
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="service">
                                                <i class="fas fa-headset"></i>
                                                联系客服
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="recent-order-item" data-order-id="COS2024011202" data-status="shipped">
                                <div class="order-image-container">
                                    <img src="images/product-2.jpg" alt="商品图片" class="order-image">
                                    <div class="order-quantity">×2</div>
                                    <div class="order-category-tag">宝可梦</div>
                                    <div class="order-hot-badge">热销</div>
                                </div>
                                <div class="order-info">
                                    <div class="order-header-info">
                                        <h4 class="order-product">宝可梦 - 皮卡丘经典套装</h4>
                                        <div class="order-rating">
                                            <div class="stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="far fa-star"></i>
                                            </div>
                                            <span class="rating-score">4.2</span>
                                        </div>
                                    </div>
                                    <p class="order-details">
                                        <span class="detail-item">
                                            <i class="fas fa-ruler"></i>
                                            尺码: S, M
                                        </span>
                                        <span class="detail-item">
                                            <i class="fas fa-palette"></i>
                                            颜色: 黄色
                                        </span>
                                        <span class="detail-item">
                                            <i class="fas fa-fire"></i>
                                            热销商品
                                        </span>
                                    </p>
                                    <div class="order-meta">
                                        <span class="order-number">
                                            <i class="fas fa-receipt"></i>
                                            COS2024011202
                                        </span>
                                        <span class="order-date">
                                            <i class="fas fa-calendar"></i>
                                            2024-01-12 10:15
                                        </span>
                                        <span class="delivery-time">
                                            <i class="fas fa-clock"></i>
                                            预计明日送达
                                        </span>
                                    </div>
                                </div>
                                <div class="order-status">
                                    <span class="status-badge shipped">
                                        <i class="fas fa-shipping-fast"></i>
                                        运输中
                                    </span>
                                    <div class="delivery-info">
                                        <div class="delivery-detail">
                                            <i class="fas fa-truck"></i>
                                            <span>快递单号: SF1234567890</span>
                                        </div>
                                        <div class="delivery-location">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>预计明日 18:00 前送达</span>
                                        </div>
                                        <div class="delivery-current">
                                            <i class="fas fa-map-pin"></i>
                                            <span>包裹已到达北京分拣中心</span>
                                        </div>
                                    </div>
                                    <div class="order-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 75%"></div>
                                        </div>
                                        <span class="progress-text">配送中 - 75%</span>
                                    </div>
                                </div>
                                <div class="order-price">
                                    <div class="price-main">¥598</div>
                                    <div class="price-original">¥698</div>
                                    <div class="price-detail">免运费</div>
                                    <div class="price-save">省¥100</div>
                                </div>
                                <div class="order-actions">
                                    <button class="action-btn primary" data-action="track" data-order-id="COS2024011202">
                                        <i class="fas fa-truck"></i>
                                        <span>查看物流</span>
                                    </button>
                                    <button class="action-btn secondary" data-action="contact-courier">
                                        <i class="fas fa-phone"></i>
                                        <span>联系快递员</span>
                                    </button>
                                    <div class="action-dropdown">
                                        <button class="action-btn text dropdown-toggle" data-toggle="dropdown">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <span>更多</span>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a href="#" class="dropdown-item" data-action="details">
                                                <i class="fas fa-eye"></i>
                                                查看详情
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="modify-address">
                                                <i class="fas fa-edit"></i>
                                                修改地址
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="delivery-time">
                                                <i class="fas fa-clock"></i>
                                                预约配送
                                            </a>
                                            <a href="#" class="dropdown-item" data-action="service">
                                                <i class="fas fa-headset"></i>
                                                联系客服
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="recent-order-item">
                                <div class="order-image">
                                    <img src="images/product-3.jpg" alt="商品图片">
                                    <div class="order-quantity">×1</div>
                                </div>
                                <div class="order-info">
                                    <h4 class="order-product">初音未来 - 演唱会服装</h4>
                                    <p class="order-details">尺码: M | 颜色: 青色</p>
                                    <div class="order-meta">
                                        <span class="order-number">COS2024010801</span>
                                        <span class="order-date">2024-01-08 16:45</span>
                                    </div>
                                </div>
                                <div class="order-status">
                                    <span class="status-badge pending">
                                        <i class="fas fa-clock"></i>
                                        待付款
                                    </span>
                                    <div class="delivery-info">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        24小时内付款
                                    </div>
                                </div>
                                <div class="order-price">
                                    <div class="price-main">¥799</div>
                                    <div class="price-detail">含运费 ¥20</div>
                                </div>
                                <div class="order-actions">
                                    <button class="action-btn primary urgent" data-action="pay">
                                        <i class="fas fa-credit-card"></i>
                                        立即付款
                                    </button>
                                    <button class="action-btn text" data-action="cancel">
                                        <i class="fas fa-times"></i>
                                        取消订单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 我的订单 -->
                <div class="content-section" id="orders">
                    <div class="section-header">
                        <h2 class="section-title">我的订单</h2>
                        <div class="order-controls">
                            <div class="search-box">
                                <input type="text" placeholder="搜索订单号或商品名称" class="search-input">
                                <button class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="date-filter">
                                <select class="date-select">
                                    <option value="all">全部时间</option>
                                    <option value="week">最近一周</option>
                                    <option value="month">最近一月</option>
                                    <option value="quarter">最近三月</option>
                                    <option value="year">最近一年</option>
                                </select>
                            </div>
                            <button class="export-orders-btn">
                                <i class="fas fa-download"></i>
                                导出订单
                            </button>
                        </div>
                    </div>

                    <div class="order-filters">
                        <button class="filter-btn active" data-status="all">
                            <span class="filter-text">全部</span>
                            <span class="filter-count">12</span>
                        </button>
                        <button class="filter-btn" data-status="pending">
                            <span class="filter-text">待付款</span>
                            <span class="filter-count">2</span>
                        </button>
                        <button class="filter-btn" data-status="processing">
                            <span class="filter-text">处理中</span>
                            <span class="filter-count">3</span>
                        </button>
                        <button class="filter-btn" data-status="shipped">
                            <span class="filter-text">已发货</span>
                            <span class="filter-count">4</span>
                        </button>
                        <button class="filter-btn" data-status="delivered">
                            <span class="filter-text">已送达</span>
                            <span class="filter-count">3</span>
                        </button>
                    </div>

                    <div class="orders-list">
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-info">
                                    <span class="order-number">
                                        <i class="fas fa-receipt"></i>
                                        订单号: COS2024011501
                                    </span>
                                    <span class="order-date">
                                        <i class="fas fa-calendar"></i>
                                        2024-01-15 14:30
                                    </span>
                                </div>
                                <div class="order-status-info">
                                    <span class="order-status delivered">
                                        <i class="fas fa-check-circle"></i>
                                        已送达
                                    </span>
                                    <div class="status-timeline">
                                        <div class="timeline-step completed">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>已下单</span>
                                        </div>
                                        <div class="timeline-step completed">
                                            <i class="fas fa-credit-card"></i>
                                            <span>已付款</span>
                                        </div>
                                        <div class="timeline-step completed">
                                            <i class="fas fa-box"></i>
                                            <span>已发货</span>
                                        </div>
                                        <div class="timeline-step completed">
                                            <i class="fas fa-truck"></i>
                                            <span>已送达</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="order-items">
                                <div class="order-item">
                                    <div class="item-image-container">
                                        <img src="images/product-1.jpg" alt="商品图片" class="item-image">
                                        <div class="item-quantity-badge">×1</div>
                                    </div>
                                    <div class="item-details">
                                        <h4 class="item-name">刻晴 - 星霜华裳</h4>
                                        <p class="item-specs">
                                            <span class="spec-item">
                                                <i class="fas fa-ruler"></i>
                                                尺码: M
                                            </span>
                                            <span class="spec-item">
                                                <i class="fas fa-palette"></i>
                                                颜色: 紫色
                                            </span>
                                        </p>
                                        <div class="item-rating">
                                            <span class="rating-label">商品评分:</span>
                                            <div class="stars">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                            <span class="rating-count">(4.9)</span>
                                        </div>
                                    </div>
                                    <div class="item-price-info">
                                        <div class="item-price">¥499</div>
                                        <div class="item-original-price">¥599</div>
                                        <div class="item-discount">-17%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="order-footer">
                                <div class="order-summary">
                                    <div class="summary-row">
                                        <span class="summary-label">商品金额:</span>
                                        <span class="summary-value">¥499</span>
                                    </div>
                                    <div class="summary-row">
                                        <span class="summary-label">运费:</span>
                                        <span class="summary-value">¥15</span>
                                    </div>
                                    <div class="summary-row">
                                        <span class="summary-label">优惠券:</span>
                                        <span class="summary-value discount">-¥0</span>
                                    </div>
                                    <div class="summary-row total">
                                        <span class="summary-label">实付金额:</span>
                                        <span class="summary-value">¥514</span>
                                    </div>
                                </div>
                                <div class="order-actions">
                                    <button class="action-btn secondary" data-action="view-details">
                                        <i class="fas fa-eye"></i>
                                        查看详情
                                    </button>
                                    <button class="action-btn secondary" data-action="reorder">
                                        <i class="fas fa-redo"></i>
                                        再次购买
                                    </button>
                                    <button class="action-btn primary" data-action="review">
                                        <i class="fas fa-star"></i>
                                        评价商品
                                    </button>
                                    <button class="action-btn text" data-action="contact-service">
                                        <i class="fas fa-headset"></i>
                                        联系客服
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 更多订单示例 -->
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-info">
                                    <span class="order-number">
                                        <i class="fas fa-receipt"></i>
                                        订单号: COS2024011202
                                    </span>
                                    <span class="order-date">
                                        <i class="fas fa-calendar"></i>
                                        2024-01-12 10:15
                                    </span>
                                </div>
                                <div class="order-status-info">
                                    <span class="order-status shipped">
                                        <i class="fas fa-shipping-fast"></i>
                                        运输中
                                    </span>
                                    <div class="tracking-info">
                                        <span class="tracking-number">快递单号: SF1234567890</span>
                                        <span class="estimated-delivery">预计明日 18:00 前送达</span>
                                    </div>
                                </div>
                            </div>
                            <div class="order-items">
                                <div class="order-item">
                                    <div class="item-image-container">
                                        <img src="images/product-2.jpg" alt="商品图片" class="item-image">
                                        <div class="item-quantity-badge">×2</div>
                                    </div>
                                    <div class="item-details">
                                        <h4 class="item-name">宝可梦 - 皮卡丘套装</h4>
                                        <p class="item-specs">
                                            <span class="spec-item">
                                                <i class="fas fa-ruler"></i>
                                                尺码: S, M
                                            </span>
                                            <span class="spec-item">
                                                <i class="fas fa-palette"></i>
                                                颜色: 黄色
                                            </span>
                                        </p>
                                    </div>
                                    <div class="item-price-info">
                                        <div class="item-price">¥598</div>
                                        <div class="free-shipping">免运费</div>
                                    </div>
                                </div>
                            </div>
                            <div class="order-footer">
                                <div class="order-summary">
                                    <div class="summary-row total">
                                        <span class="summary-label">实付金额:</span>
                                        <span class="summary-value">¥598</span>
                                    </div>
                                </div>
                                <div class="order-actions">
                                    <button class="action-btn primary" data-action="track-order">
                                        <i class="fas fa-truck"></i>
                                        查看物流
                                    </button>
                                    <button class="action-btn secondary" data-action="view-details">
                                        <i class="fas fa-eye"></i>
                                        查看详情
                                    </button>
                                    <button class="action-btn text" data-action="contact-service">
                                        <i class="fas fa-headset"></i>
                                        联系客服
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 心愿单 -->
                <div class="content-section" id="wishlist">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-heart"></i>
                            我的心愿单
                        </h2>
                        <div class="wishlist-controls">
                            <div class="view-toggle">
                                <button class="toggle-btn active" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="toggle-btn" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                            <div class="sort-options">
                                <select class="sort-select">
                                    <option value="date">按添加时间</option>
                                    <option value="price-low">价格从低到高</option>
                                    <option value="price-high">价格从高到低</option>
                                    <option value="rating">按评分排序</option>
                                </select>
                            </div>
                            <button class="clear-all-btn">
                                <i class="fas fa-trash"></i>
                                清空心愿单
                            </button>
                        </div>
                    </div>

                    <div class="wishlist-stats">
                        <div class="stat-item">
                            <span class="stat-number">8</span>
                            <span class="stat-label">商品总数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3</span>
                            <span class="stat-label">有折扣</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">¥2,156</span>
                            <span class="stat-label">总价值</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">¥1,890</span>
                            <span class="stat-label">折后价</span>
                        </div>
                    </div>

                    <div class="wishlist-grid" data-view="grid">
                        <div class="wishlist-item">
                            <div class="item-image">
                                <img src="images/product-2.jpg" alt="商品图片">
                                <div class="item-badges">
                                    <span class="discount-badge">-15%</span>
                                    <span class="stock-badge in-stock">有库存</span>
                                </div>
                                <button class="remove-wishlist" data-product-id="202" title="移除心愿单">
                                    <i class="fas fa-heart-broken"></i>
                                </button>
                                <div class="item-overlay">
                                    <button class="quick-view-btn" data-product-id="202">
                                        <i class="fas fa-eye"></i>
                                        快速查看
                                    </button>
                                </div>
                            </div>
                            <div class="item-info">
                                <div class="item-category">宝可梦</div>
                                <h4 class="item-name">宝可梦 - 皮卡丘经典套装</h4>
                                <div class="item-rating">
                                    <div class="stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="rating-score">4.2</span>
                                    <span class="rating-count">(128)</span>
                                </div>
                                <div class="item-price-info">
                                    <div class="current-price">¥254</div>
                                    <div class="original-price">¥299</div>
                                    <div class="save-amount">省¥45</div>
                                </div>
                                <div class="item-actions">
                                    <button class="add-to-cart-btn primary" data-product-id="202">
                                        <i class="fas fa-shopping-cart"></i>
                                        加入购物车
                                    </button>
                                    <button class="compare-btn" data-product-id="202" title="加入对比">
                                        <i class="fas fa-balance-scale"></i>
                                    </button>
                                    <button class="share-btn" data-product-id="202" title="分享商品">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                </div>
                                <div class="item-meta">
                                    <span class="added-date">
                                        <i class="fas fa-calendar-plus"></i>
                                        添加于 2024-01-10
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="wishlist-item">
                            <div class="item-image">
                                <img src="images/product-1.jpg" alt="商品图片">
                                <div class="item-badges">
                                    <span class="hot-badge">热销</span>
                                    <span class="stock-badge low-stock">库存紧张</span>
                                </div>
                                <button class="remove-wishlist" data-product-id="201" title="移除心愿单">
                                    <i class="fas fa-heart-broken"></i>
                                </button>
                                <div class="item-overlay">
                                    <button class="quick-view-btn" data-product-id="201">
                                        <i class="fas fa-eye"></i>
                                        快速查看
                                    </button>
                                </div>
                            </div>
                            <div class="item-info">
                                <div class="item-category">原神</div>
                                <h4 class="item-name">刻晴 - 星霜华裳限定版</h4>
                                <div class="item-rating">
                                    <div class="stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="rating-score">4.9</span>
                                    <span class="rating-count">(256)</span>
                                </div>
                                <div class="item-price-info">
                                    <div class="current-price">¥499</div>
                                    <div class="vip-price">VIP价: ¥449</div>
                                </div>
                                <div class="item-actions">
                                    <button class="add-to-cart-btn primary" data-product-id="201">
                                        <i class="fas fa-shopping-cart"></i>
                                        加入购物车
                                    </button>
                                    <button class="compare-btn" data-product-id="201" title="加入对比">
                                        <i class="fas fa-balance-scale"></i>
                                    </button>
                                    <button class="share-btn" data-product-id="201" title="分享商品">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                </div>
                                <div class="item-meta">
                                    <span class="added-date">
                                        <i class="fas fa-calendar-plus"></i>
                                        添加于 2024-01-08
                                    </span>
                                    <span class="price-alert">
                                        <i class="fas fa-bell"></i>
                                        降价提醒已开启
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="wishlist-item">
                            <div class="item-image">
                                <img src="images/product-3.jpg" alt="商品图片">
                                <div class="item-badges">
                                    <span class="new-badge">新品</span>
                                    <span class="stock-badge out-stock">缺货</span>
                                </div>
                                <button class="remove-wishlist" data-product-id="203" title="移除心愿单">
                                    <i class="fas fa-heart-broken"></i>
                                </button>
                                <div class="item-overlay">
                                    <button class="notify-btn" data-product-id="203">
                                        <i class="fas fa-bell"></i>
                                        到货通知
                                    </button>
                                </div>
                            </div>
                            <div class="item-info">
                                <div class="item-category">初音未来</div>
                                <h4 class="item-name">初音未来 - 演唱会限定服装</h4>
                                <div class="item-rating">
                                    <div class="stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="rating-score">4.8</span>
                                    <span class="rating-count">(89)</span>
                                </div>
                                <div class="item-price-info">
                                    <div class="current-price">¥799</div>
                                    <div class="preorder-info">预售中</div>
                                </div>
                                <div class="item-actions">
                                    <button class="notify-stock-btn" data-product-id="203">
                                        <i class="fas fa-bell"></i>
                                        到货通知
                                    </button>
                                    <button class="compare-btn" data-product-id="203" title="加入对比">
                                        <i class="fas fa-balance-scale"></i>
                                    </button>
                                    <button class="share-btn" data-product-id="203" title="分享商品">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                </div>
                                <div class="item-meta">
                                    <span class="added-date">
                                        <i class="fas fa-calendar-plus"></i>
                                        添加于 2024-01-05
                                    </span>
                                    <span class="restock-alert">
                                        <i class="fas fa-clock"></i>
                                        预计2月上旬到货
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 推荐相似商品 -->
                    <div class="similar-recommendations">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-lightbulb"></i>
                                您可能还喜欢
                            </h3>
                        </div>
                        <div class="recommendation-slider">
                            <div class="recommendation-item">
                                <img src="images/product-4.jpg" alt="推荐商品">
                                <h4>相似商品推荐</h4>
                                <div class="price">¥399</div>
                                <button class="add-to-wishlist-btn">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收货地址 -->
                <div class="content-section" id="addresses">
                    <div class="section-header">
                        <h2 class="section-title">收货地址</h2>
                        <button class="btn-primary" id="add-address-btn">
                            <i class="fas fa-plus"></i>
                            添加新地址
                        </button>
                    </div>

                    <div class="addresses-list">
                        <div class="address-card">
                            <div class="address-header">
                                <span class="address-name">张三</span>
                                <span class="address-phone">138****8888</span>
                                <span class="default-badge">默认</span>
                            </div>
                            <div class="address-content">
                                <p class="address-detail">北京市朝阳区某某街道某某小区1号楼101室</p>
                            </div>
                            <div class="address-actions">
                                <button class="btn-text">编辑</button>
                                <button class="btn-text">删除</button>
                                <button class="btn-text">设为默认</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 优惠券 -->
                <div class="content-section" id="coupons">
                    <div class="section-header">
                        <h2 class="section-title">我的优惠券</h2>
                        <div class="coupon-tabs">
                            <button class="tab-btn active" data-tab="available">可使用</button>
                            <button class="tab-btn" data-tab="used">已使用</button>
                            <button class="tab-btn" data-tab="expired">已过期</button>
                        </div>
                    </div>

                    <div class="coupons-list">
                        <div class="coupon-card">
                            <div class="coupon-amount">
                                <span class="amount">¥50</span>
                                <span class="condition">满500可用</span>
                            </div>
                            <div class="coupon-info">
                                <h4 class="coupon-name">新用户专享券</h4>
                                <p class="coupon-desc">适用于全场商品</p>
                                <p class="coupon-expire">有效期至: 2024-12-31</p>
                            </div>
                            <div class="coupon-action">
                                <button class="use-coupon-btn">立即使用</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人资料 -->
                <div class="content-section" id="profile">
                    <div class="section-header">
                        <h2 class="section-title">个人资料</h2>
                    </div>

                    <form class="profile-form">
                        <div class="form-group">
                            <label class="form-label">头像</label>
                            <div class="avatar-upload">
                                <div class="avatar-preview">
                                    <i class="fas fa-user"></i>
                                </div>
                                <button type="button" class="upload-btn">更换头像</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-input" value="<?php echo htmlspecialchars($current_user['username'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-input" value="<?php echo htmlspecialchars($current_user['email'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-input" placeholder="请输入手机号">
                        </div>

                        <div class="form-group">
                            <label class="form-label">生日</label>
                            <input type="date" class="form-input">
                        </div>

                        <div class="form-group">
                            <label class="form-label">性别</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="gender" value="male">
                                    <span class="radio-mark"></span>
                                    男
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="gender" value="female">
                                    <span class="radio-mark"></span>
                                    女
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="gender" value="other">
                                    <span class="radio-mark"></span>
                                    其他
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>

                <!-- 账户安全 -->
                <div class="content-section" id="security">
                    <div class="section-header">
                        <h2 class="section-title">账户安全</h2>
                    </div>

                    <div class="security-items">
                        <div class="security-item">
                            <div class="security-info">
                                <h4 class="security-title">登录密码</h4>
                                <p class="security-desc">定期更换密码，保护账户安全</p>
                            </div>
                            <div class="security-action">
                                <button class="btn-secondary">修改密码</button>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-info">
                                <h4 class="security-title">手机验证</h4>
                                <p class="security-desc">已绑定手机: 138****8888</p>
                            </div>
                            <div class="security-action">
                                <button class="btn-secondary">更换手机</button>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-info">
                                <h4 class="security-title">邮箱验证</h4>
                                <p class="security-desc">已验证邮箱: <EMAIL></p>
                            </div>
                            <div class="security-action">
                                <span class="status-verified">已验证</span>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- user-main-content -->
            </div> <!-- user-content -->
        </div> <!-- user-layout -->
    </div> <!-- container -->
</div> <!-- user-center-page -->

<!-- 自定义样式 -->
<style>
/* 用户中心页面布局 */
.user-center-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.user-layout {
    max-width: 1400px;
    margin: 0 auto;
}

.user-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 用户头部信息栏 */
.user-header {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 24px;
    align-items: start;
}

/* 用户资料卡片 */
.user-profile-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
    min-width: 320px;
}

.profile-background {
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.profile-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 30px 30px;
}

.profile-content {
    padding: 0 20px 20px;
    text-align: center;
    position: relative;
    margin-top: -30px;
}

.user-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 12px;
}

.user-avatar img,
.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.avatar-placeholder {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6c757d;
}

.user-avatar img {
    object-fit: cover;
}

.avatar-badge {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.avatar-badge.admin {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.avatar-badge.vip {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.avatar-badge.user {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.avatar-status {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
}

.avatar-status.online {
    background: #28a745;
}

.user-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
}

.user-level {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #f8f9fa;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.user-level i.fa-crown {
    color: #dc3545;
}

.user-level i.fa-star {
    color: #ffc107;
}

.user-level i.fa-user {
    color: #007bff;
}

.user-email {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0 0 20px 0;
}

/* 会员等级进度条 */
.member-progress {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 12px;
    margin-top: 16px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-size: 0.85rem;
    color: #6c757d;
}

.progress-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: #007bff;
}

.progress-bar {
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin: 0;
    text-align: center;
}

/* 快速统计 */
.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-width: 200px;
}

.stat-item {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-size: 1.2rem;
    color: white;
}

.stat-icon.orders {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-icon.wishlist {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.stat-icon.coupons {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-number {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 导航标签页 */
.user-nav-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    position: relative;
}

.nav-tabs-container {
    position: relative;
}

.nav-tabs-scroll {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-tabs-scroll::-webkit-scrollbar {
    display: none;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
    border-bottom: 3px solid transparent;
}

.nav-tab:hover {
    color: var(--primary-color);
    background: rgba(0,123,255,0.05);
}

.nav-tab.active {
    color: var(--primary-color);
    background: rgba(0,123,255,0.1);
    border-bottom-color: var(--primary-color);
}

.nav-tab i {
    font-size: 1rem;
}

.nav-tab span {
    font-size: 0.9rem;
    font-weight: 500;
}

.tab-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 4px;
}

.nav-tabs-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    border-radius: 2px 2px 0 0;
}

/* 主内容区域 */
.user-main-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.content-section {
    padding: 24px;
    display: none;
}

.content-section.active {
    display: block;
}

/* 概览页面样式 */
.overview-dashboard {
    margin-bottom: 30px;
}

.dashboard-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.orders {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.spending {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.wishlist {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.coupons {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(255,255,255,0.9);
}

.stat-trend.positive {
    color: #28a745;
}

.stat-trend.negative {
    color: #dc3545;
}

.stat-trend.neutral {
    color: #6c757d;
}

.stat-content .stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 4px;
    display: block;
}

.stat-content .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    display: block;
}

.stat-detail {
    font-size: 0.8rem;
    color: #6c757d;
}

.stat-chart {
    margin-top: 16px;
}

.mini-chart {
    display: flex;
    align-items: end;
    gap: 3px;
    height: 40px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, var(--primary-color), rgba(0,123,255,0.6));
    border-radius: 2px;
    min-height: 8px;
    transition: height 0.3s ease;
}

.stat-progress {
    margin-top: 16px;
}

.stat-progress .progress-bar {
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.stat-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #0056b3);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: #6c757d;
    text-align: center;
}

.stat-actions {
    margin-top: 16px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    justify-content: center;
}

.quick-action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.quick-action-btn.urgent {
    background: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* 导航菜单（保留用于移动端） */
.user-nav {
    background: white;
    border-radius: 16px;
    padding: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: none;
}

.nav-section {
    margin-bottom: 24px;
}

.nav-section:last-child {
    margin-bottom: 0;
}

.nav-section-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 0 12px 0;
    padding: 0 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

.nav-item.active {
    background: linear-gradient(90deg, rgba(0,123,255,0.1), transparent);
    color: #007bff;
    border-right: 3px solid #007bff;
}

.nav-icon {
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.nav-text {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
}

.nav-arrow {
    font-size: 0.7rem;
    color: #adb5bd;
    transition: transform 0.2s ease;
}

.nav-item:hover .nav-arrow {
    transform: translateX(2px);
}

/* 快捷操作样式 */
.quick-actions {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    min-width: 200px;
}

.quick-actions-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: #495057;
    margin: 0 0 12px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 12px;
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    color: #495057;
}

.action-btn:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.2rem;
    margin-bottom: 8px;
}

.action-btn span {
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

/* 统计趋势样式 */
.stat-trend {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255,255,255,0.9);
}

.stat-trend i.fa-arrow-up {
    color: #28a745;
}

.stat-trend i.fa-arrow-down {
    color: #dc3545;
}

.stat-trend span {
    font-weight: 600;
}

/* 欢迎消息增强 */
.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.welcome-text {
    flex: 1;
}

.welcome-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
}

.welcome-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: rgba(255,255,255,0.9);
}

.welcome-stats .stat-item i {
    width: 16px;
    text-align: center;
}

.welcome-weather {
    background: rgba(255,255,255,0.1);
    padding: 16px;
    border-radius: 12px;
    text-align: center;
    min-width: 140px;
}

.weather-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
}

.weather-info i {
    font-size: 1.5rem;
    color: #ffc107;
}

.weather-temp {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
}

.weather-desc {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.weather-tip {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
    margin: 0;
    line-height: 1.3;
}

/* 今日推荐样式 */
.daily-recommendations {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.daily-recommendations .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.daily-recommendations .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: #495057;
}

.refresh-btn {
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
}

.refresh-btn:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.recommendation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.recommendation-card {
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.recommendation-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.recommendation-card .card-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.recommendation-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.discount-badge, .hot-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
}

.discount-badge {
    background: #dc3545;
}

.hot-badge {
    background: #ff6b35;
}

.recommendation-card .card-content {
    padding: 16px;
}

.recommendation-card .product-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #212529;
}

.recommendation-card .product-origin {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0 0 8px 0;
}

.price-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.current-price {
    font-size: 1rem;
    font-weight: 600;
    color: #dc3545;
}

.original-price {
    font-size: 0.8rem;
    color: #6c757d;
    text-decoration: line-through;
}

.quick-add-btn {
    width: 100%;
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.quick-add-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 订单控制样式 */
.order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    flex: 1;
    min-width: 200px;
}

.search-input {
    flex: 1;
    padding: 10px 12px;
    border: none;
    outline: none;
    font-size: 0.9rem;
}

.search-btn {
    padding: 10px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-btn:hover {
    background: var(--primary-dark);
}

.date-select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    outline: none;
}

.export-orders-btn {
    padding: 10px 16px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.export-orders-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* 订单筛选增强 */
.order-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.filter-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 80px;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    background: rgba(0,123,255,0.05);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.filter-text {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.filter-count {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* 概览操作按钮 */
.overview-actions {
    display: flex;
    gap: 12px;
}

.action-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 8px;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-link:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 活动中心样式 */
.activity-center {
    margin-bottom: 30px;
}

.activity-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.activity-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.activity-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.activity-card.featured {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.activity-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.activity-content {
    margin-bottom: 16px;
}

.activity-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.activity-desc {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 12px;
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    opacity: 0.8;
}

.activity-btn {
    background: rgba(255,255,255,0.2);
    color: inherit;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.activity-card:not(.featured) .activity-btn {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.activity-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* 订单卡片增强样式 */
.order-card {
    background: white;
    border-radius: 12px;
    border: 2px solid #f8f9fa;
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.order-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.order-header {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.order-number, .order-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #6c757d;
}

.order-status-info {
    text-align: right;
}

.order-status {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.order-status.delivered {
    background: #d4edda;
    color: #155724;
}

.order-status.shipped {
    background: #cce7ff;
    color: #004085;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.status-timeline {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.timeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.7rem;
    color: #6c757d;
    opacity: 0.5;
}

.timeline-step.completed {
    color: #28a745;
    opacity: 1;
}

.timeline-step i {
    font-size: 0.8rem;
}

.tracking-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
}

.tracking-number {
    font-weight: 600;
}

.estimated-delivery {
    color: #28a745;
}

.order-items {
    padding: 20px;
}

.item-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.item-quantity-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.order-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 16px;
    align-items: center;
}

.item-specs {
    margin: 8px 0;
}

.spec-item {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-right: 12px;
    font-size: 0.8rem;
    color: #6c757d;
}

.item-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.rating-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.stars {
    color: #ffc107;
}

.rating-count {
    font-size: 0.8rem;
    color: #6c757d;
}

.item-price-info {
    text-align: right;
}

.item-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
}

.item-original-price {
    font-size: 0.8rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-top: 2px;
}

.item-discount {
    font-size: 0.8rem;
    color: #dc3545;
    font-weight: 600;
    margin-top: 2px;
}

.free-shipping {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
    margin-top: 2px;
}

.order-footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.order-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    font-size: 0.9rem;
}

.summary-row.total {
    font-weight: 600;
    font-size: 1rem;
    color: #212529;
    border-top: 1px solid #dee2e6;
    padding-top: 4px;
    margin-top: 4px;
}

.summary-label {
    color: #6c757d;
}

.summary-value.discount {
    color: #28a745;
}

.order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 最近订单增强样式 */
.order-count-badge {
    background: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
}

.recent-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.order-filter-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2px;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    color: #6c757d;
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
}

.tab-count {
    background: rgba(255,255,255,0.2);
    padding: 1px 4px;
    border-radius: 8px;
    font-size: 0.6rem;
}

.filter-tab.active .tab-count {
    background: rgba(255,255,255,0.3);
}

/* 订单统计概览 */
.recent-order-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 24px;
}

.order-stat-item {
    background: white;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.order-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    transition: all 0.3s ease;
}

.order-stat-item:nth-child(1)::before {
    background: linear-gradient(to bottom, #ffc107, #fd7e14);
}

.order-stat-item:nth-child(2)::before {
    background: linear-gradient(to bottom, #007bff, #0056b3);
}

.order-stat-item:nth-child(3)::before {
    background: linear-gradient(to bottom, #28a745, #20c997);
}

.order-stat-item:hover {
    border-color: transparent;
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.order-stat-item .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: white;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

.order-stat-item .stat-icon.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.order-stat-item .stat-icon.shipped {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.order-stat-item .stat-icon.delivered {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.order-stat-item .stat-content {
    width: 100%;
}

.order-stat-item .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #212529;
    display: block;
    margin-bottom: 4px;
}

.order-stat-item .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    display: block;
}

.stat-action {
    width: 100%;
}

.quick-pay-btn, .track-btn, .review-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.25s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.quick-pay-btn {
    background: linear-gradient(135deg, #ff4b2b, #ff416c);
    color: white;
}

.track-btn {
    background: linear-gradient(135deg, #0072ff, #00c6ff);
    color: white;
}

.review-btn {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
}

.quick-pay-btn:hover, .track-btn:hover, .review-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.quick-pay-btn:active, .track-btn:active, .review-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .recent-order-stats {
        grid-template-columns: repeat(1, 1fr);
        gap: 16px;
    }
    
    .order-stat-item {
        flex-direction: row;
        align-items: center;
        padding: 16px;
    }
    
    .order-stat-item::before {
        width: 100%;
        height: 6px;
        top: 0;
        left: 0;
    }
    
    .order-stat-item .stat-content {
        flex: 1;
        margin-bottom: 0;
    }
    
    .order-stat-item .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-action {
        width: auto;
    }
    
    .quick-pay-btn, .track-btn, .review-btn {
        width: auto;
        padding: 8px 12px;
    }
}

/* 订单项增强样式 */
.order-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.order-category-tag {
    position: absolute;
    top: 4px;
    left: 4px;
    background: rgba(0,0,0,0.7);
    color: white;
    font-size: 0.6rem;
    padding: 2px 4px;
    border-radius: 4px;
}

.order-header-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.order-rating {
    display: flex;
    align-items: center;
    gap: 4px;
}

.order-rating .stars {
    color: #ffc107;
    font-size: 0.8rem;
}

.rating-score {
    font-size: 0.8rem;
    font-weight: 600;
    color: #212529;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
}

.delivery-time {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
}

.delivery-detail, .delivery-location {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.order-progress {
    margin-top: 8px;
}

.order-progress .progress-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.order-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.7rem;
    color: #6c757d;
}

.price-original {
    font-size: 0.9rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-top: 2px;
}

.price-save {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
    margin-top: 2px;
}

.action-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    color: #212529;
    text-decoration: none;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .user-header {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .user-profile-card {
        min-width: auto;
    }

    .quick-stats {
        flex-direction: row;
        justify-content: space-between;
    }

    .quick-actions {
        min-width: auto;
    }

    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .user-center-page {
        padding: 10px 0;
    }

    .container {
        padding: 0 15px;
    }

    .user-content {
        gap: 16px;
    }

    .user-header {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .profile-content {
        padding: 0 16px 16px;
        margin-top: -25px;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
    }

    .quick-stats {
        flex-direction: column;
        gap: 8px;
    }

    .stat-item {
        padding: 12px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .nav-tabs-scroll {
        padding: 0 16px;
    }

    .nav-tab {
        padding: 12px 16px;
        font-size: 0.85rem;
    }

    .welcome-content {
        flex-direction: column;
        gap: 16px;
    }

    .welcome-weather {
        align-self: stretch;
    }

    .recommendation-cards {
        grid-template-columns: 1fr;
    }

    .order-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    /* 移动端显示传统导航 */
    .user-nav-tabs {
        display: none;
    }

    .user-nav {
        display: block;
    }
}

@media (max-width: 480px) {
    .user-center-page {
        padding: 10px 0;
    }

    .container {
        padding: 0 15px;
    }

    .profile-content {
        padding: 0 16px 16px;
    }

    .quick-stats {
        grid-template-columns: 1fr;
    }
}

/* 最近订单 */
.recent-orders {
    background: white;
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 6px 24px rgba(0,0,0,0.06);
    border: 1px solid rgba(0,0,0,0.04);
    transition: all 0.3s ease;
}

.recent-orders:hover {
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    transform: translateY(-3px);
}

.recent-orders .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 16px;
}

.recent-orders .section-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recent-orders .section-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.order-count-badge {
    background: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 10px;
    box-shadow: 0 2px 6px rgba(0,123,255,0.2);
}

.recent-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.order-filter-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 4px;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    color: #555;
    font-weight: 500;
}

.filter-tab:hover:not(.active) {
    background: rgba(0,123,255,0.08);
    color: var(--primary-color);
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0,123,255,0.25);
}

.tab-count {
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.filter-tab:not(.active) .tab-count {
    background: rgba(0,0,0,0.1);
    color: #666;
}

.view-all-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: #f0f7ff;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-all-link:hover {
    background: #e0f0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,123,255,0.1);
}

.view-all-link i {
    font-size: 0.8rem;
}

/* 订单列表样式 */
.recent-order-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.recent-order-item {
    background: #f9f9f9;
    border-radius: 16px;
    padding: 20px;
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 20px;
    align-items: center;
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.recent-order-item:hover {
    background: #ffffff;
    box-shadow: 0 6px 18px rgba(0,0,0,0.08);
    transform: translateY(-3px);
    border-color: rgba(0,123,255,0.1);
}

.recent-order-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
}

.recent-order-item[data-status="delivered"]:before {
    background: linear-gradient(to bottom, #28a745, #20c997);
}

.recent-order-item[data-status="shipped"]:before {
    background: linear-gradient(to bottom, #007bff, #0056b3);
}

.recent-order-item[data-status="pending"]:before {
    background: linear-gradient(to bottom, #ffc107, #fd7e14);
}

.order-image-container {
    position: relative;
    width: 100px;
    height: 130px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.order-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.recent-order-item:hover .order-image {
    transform: scale(1.05);
}

.order-quantity {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0,0,0,0.6);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 3px 6px;
    border-radius: 8px;
}

.order-category-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0,0,0,0.6);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 3px 6px;
    border-radius: 8px;
}

.order-hot-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: linear-gradient(135deg, #ff4b2b, #ff416c);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 3px 6px;
    border-radius: 8px;
}

.order-info {
    flex: 1;
    min-width: 0;
}

.order-header-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.order-product {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
}

.order-rating {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #fff9e6;
    padding: 4px 8px;
    border-radius: 8px;
}

.order-rating .stars {
    color: #ffc107;
    font-size: 0.85rem;
}

.rating-score {
    font-size: 0.85rem;
    font-weight: 700;
    color: #212529;
}

.order-details {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    color: #666;
    background: #f0f0f0;
    padding: 4px 10px;
    border-radius: 6px;
}

.order-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.order-number, .order-date, .delivery-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: #666;
}

.order-status {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    min-width: 180px;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    width: fit-content;
}

.status-badge.delivered {
    background: #d4edda;
    color: #155724;
}

.status-badge.shipped {
    background: #cce7ff;
    color: #004085;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.delivery-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    font-size: 0.8rem;
}

.delivery-detail, .delivery-location, .delivery-current {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
}

.order-progress {
    width: 100%;
}

.order-progress .progress-bar {
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 6px;
}

.order-progress .progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease-in-out;
}

.recent-order-item[data-status="delivered"] .progress-fill {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.recent-order-item[data-status="shipped"] .progress-fill {
    background: linear-gradient(90deg, #007bff, #00c6ff);
}

.recent-order-item[data-status="pending"] .progress-fill {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-text {
    font-size: 0.75rem;
    color: #666;
}

.order-price {
    text-align: right;
    min-width: 100px;
}

.price-main {
    font-size: 1.2rem;
    font-weight: 700;
    color: #dc3545;
    margin-bottom: 4px;
}

.price-original {
    font-size: 0.9rem;
    color: #999;
    text-decoration: line-through;
    margin-bottom: 4px;
}

.price-detail {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 4px;
}

.price-save {
    font-size: 0.8rem;
    font-weight: 600;
    color: #28a745;
}

.order-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    width: 100%;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.secondary {
    background: rgba(0,123,255,0.1);
    color: var(--primary-color);
}

.action-btn.text {
    background: transparent;
    color: #666;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.action-btn.secondary:hover {
    background: rgba(0,123,255,0.15);
}

.action-btn.text:hover {
    background: #f0f0f0;
}

.action-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0,0,0,0.1);
    min-width: 160px;
    z-index: 1000;
    display: none;
    padding: 8px 0;
    margin-top: 8px;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    color: #333;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

.dropdown-item i {
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .recent-order-item {
        grid-template-columns: auto 1fr;
        grid-template-rows: auto auto;
    }
    
    .order-status, .order-price {
        grid-column: 1 / -1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 16px;
        text-align: left;
    }
    
    .order-actions {
        grid-column: 1 / -1;
        flex-direction: row;
        flex-wrap: wrap;
        margin-top: 16px;
    }
    
    .action-btn {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .recent-order-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .order-filter-tabs {
        overflow-x: auto;
        white-space: nowrap;
        padding: 4px;
        scrollbar-width: none;
    }
    
    .order-filter-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .recent-order-item {
        padding: 16px;
        gap: 16px;
    }
    
    .order-image-container {
        width: 80px;
        height: 100px;
    }
    
    .order-status, .order-price {
        grid-template-columns: 1fr;
    }
    
    .order-progress {
        grid-column: 1 / -1;
    }
}

@media (max-width: 576px) {
    .recent-orders {
        padding: 16px;
        border-radius: 16px;
    }
    
    .recent-order-item {
        grid-template-columns: 1fr;
    }
    
    .order-image-container {
        width: 100%;
        height: 160px;
        margin-bottom: 8px;
    }
    
    .order-info, .order-status, .order-price, .order-actions {
        grid-column: 1;
    }
    
    .action-btn {
        padding: 10px;
    }
}
</style>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签页导航
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();

            const sectionId = this.dataset.section;

            // 更新标签页状态
            document.querySelector('.nav-tab.active').classList.remove('active');
            this.classList.add('active');

            // 切换内容区域
            document.querySelector('.content-section.active').classList.remove('active');
            document.getElementById(sectionId).classList.add('active');

            // 更新指示器位置
            updateTabIndicator(this);

            // 添加切换动画
            const contentSection = document.getElementById(sectionId);
            contentSection.style.opacity = '0';
            contentSection.style.transform = 'translateY(20px)';

            setTimeout(() => {
                contentSection.style.transition = 'all 0.3s ease';
                contentSection.style.opacity = '1';
                contentSection.style.transform = 'translateY(0)';
            }, 50);
        });
    });

    // 传统导航（移动端）
    document.querySelectorAll('.user-nav .nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const sectionId = this.dataset.section;

            // 更新导航状态
            const activeNavItem = document.querySelector('.nav-item.active');
            if (activeNavItem) {
                activeNavItem.classList.remove('active');
            }
            this.classList.add('active');

            // 切换内容区域
            const activeSection = document.querySelector('.content-section.active');
            if (activeSection) {
                activeSection.classList.remove('active');
            }
            document.getElementById(sectionId).classList.add('active');

            // 同步标签页状态
            const activeTab = document.querySelector('.nav-tab.active');
            if (activeTab) {
                activeTab.classList.remove('active');
            }
            const correspondingTab = document.querySelector(`.nav-tab[data-section="${sectionId}"]`);
            if (correspondingTab) {
                correspondingTab.classList.add('active');
                updateTabIndicator(correspondingTab);
            }
        });
    });

    // 更新标签页指示器位置
    function updateTabIndicator(activeTab) {
        const indicator = document.querySelector('.nav-tabs-indicator');
        if (indicator && activeTab) {
            const tabRect = activeTab.getBoundingClientRect();
            const containerRect = activeTab.closest('.nav-tabs-container').getBoundingClientRect();

            indicator.style.left = (tabRect.left - containerRect.left) + 'px';
            indicator.style.width = tabRect.width + 'px';
        }
    }

    // 初始化指示器位置
    const activeTab = document.querySelector('.nav-tab.active');
    if (activeTab) {
        setTimeout(() => updateTabIndicator(activeTab), 100);
    }

    // 快速统计卡片点击
    document.querySelectorAll('.stat-item').forEach(item => {
        item.addEventListener('click', function() {
            const targetSection = this.dataset.target;
            if (targetSection) {
                // 触发对应的导航项点击
                const navItem = document.querySelector(`[data-section="${targetSection}"]`);
                if (navItem) {
                    navItem.click();
                }
            }
        });
    });

    // 快捷操作按钮
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            switch(action) {
                case 'reorder':
                    showNotification('正在为您查找可重复购买的商品...', 'info');
                    break;
                case 'track':
                    showNotification('正在跳转到物流查询页面...', 'info');
                    break;
                case 'service':
                    showNotification('正在连接客服...', 'info');
                    break;
                case 'feedback':
                    showNotification('正在打开意见反馈表单...', 'info');
                    break;
            }
        });
    });

    // 推荐商品快速加购
    document.querySelectorAll('.quick-add-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.recommendation-card');
            const productName = card.querySelector('.product-name').textContent;

            // 添加加载状态
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
            this.disabled = true;

            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-check"></i> 已添加';
                showNotification(`${productName} 已添加到购物车`, 'success');

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-cart-plus"></i> 快速加购';
                    this.disabled = false;
                }, 1500);
            }, 1000);
        });
    });

    // 刷新推荐按钮
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            icon.style.animation = 'spin 1s linear infinite';

            setTimeout(() => {
                icon.style.animation = '';
                showNotification('推荐内容已刷新', 'success');
            }, 1000);
        });
    }

    // 数字动画效果
    function animateNumbers() {
        document.querySelectorAll('.stat-number[data-count]').forEach(element => {
            const target = parseInt(element.dataset.count);
            const duration = 1000;
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        });
    }

    // 页面加载完成后执行数字动画
    setTimeout(animateNumbers, 500);

    // 订单筛选
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelector('.filter-btn.active').classList.remove('active');
            this.classList.add('active');

            const status = this.dataset.status;
            // 这里应该根据状态筛选订单
            console.log('筛选订单状态:', status);
        });
    });

    // 优惠券标签页
    document.querySelectorAll('.coupon-tabs .tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelector('.coupon-tabs .tab-btn.active').classList.remove('active');
            this.classList.add('active');

            const tab = this.dataset.tab;
            // 这里应该根据标签页加载不同的优惠券
            console.log('切换优惠券标签:', tab);
        });
    });

    // 会员等级进度条动画
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const targetWidth = progressFill.style.width;
        progressFill.style.width = '0%';

        setTimeout(() => {
            progressFill.style.transition = 'width 1s ease-out';
            progressFill.style.width = targetWidth;
        }, 500);
    }

    // 添加头像上传功能
    const uploadBtn = document.querySelector('.upload-btn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            // 创建文件输入
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const avatarPreview = document.querySelector('.avatar-preview');
                        avatarPreview.innerHTML = `<img src="${e.target.result}" alt="头像预览" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                    };
                    reader.readAsDataURL(file);
                }
            });

            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
        });
    }

    // 响应式侧边栏切换（移动端）
    if (window.innerWidth <= 768) {
        const sidebar = document.querySelector('.user-sidebar');
        const content = document.querySelector('.user-content');

        // 添加移动端切换按钮
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'mobile-nav-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        `;

        document.body.appendChild(toggleBtn);

        toggleBtn.addEventListener('click', function() {
            sidebar.style.display = sidebar.style.display === 'none' ? 'flex' : 'none';
        });
    }
});

// 添加平滑滚动
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// 添加通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icon = type === 'success' ? 'fas fa-check-circle' :
                 type === 'error' ? 'fas fa-exclamation-circle' :
                 'fas fa-info-circle';

    notification.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
    `;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        max-width: 300px;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 添加天气更新功能
function updateWeather() {
    const weatherInfo = document.querySelector('.weather-info');
    const weatherTip = document.querySelector('.weather-tip');

    if (weatherInfo && weatherTip) {
        const weathers = [
            { icon: 'fas fa-sun', temp: '22°C', desc: '晴朗', tip: '今天天气不错，适合外出购物！' },
            { icon: 'fas fa-cloud-sun', temp: '18°C', desc: '多云', tip: '天气舒适，记得带件外套。' },
            { icon: 'fas fa-cloud-rain', temp: '15°C', desc: '小雨', tip: '下雨天，在家购物更舒适。' }
        ];

        const weather = weathers[Math.floor(Math.random() * weathers.length)];

        weatherInfo.innerHTML = `
            <i class="${weather.icon}"></i>
            <span class="weather-temp">${weather.temp}</span>
            <span class="weather-desc">${weather.desc}</span>
        `;
        weatherTip.textContent = weather.tip;
    }
}

// 添加实时时间更新
function updateTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });

    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// 添加搜索功能
function initSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');

    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            showNotification(`正在搜索 "${query}"...`, 'info');
            // 这里可以添加实际的搜索逻辑
        }
    }
}

// 添加导出功能
function initExport() {
    const exportBtn = document.querySelector('.export-orders-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
            this.disabled = true;

            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-download"></i> 导出订单';
                this.disabled = false;
                showNotification('订单数据导出成功！', 'success');
            }, 2000);
        });
    }
}

// 添加键盘快捷键支持
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + 数字键快速切换页面
        if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '7') {
            e.preventDefault();
            const sections = ['overview', 'orders', 'wishlist', 'addresses', 'coupons', 'profile', 'security'];
            const index = parseInt(e.key) - 1;
            if (sections[index]) {
                const navItem = document.querySelector(`[data-section="${sections[index]}"]`);
                if (navItem) {
                    navItem.click();
                }
            }
        }
    });
}

// 初始化所有新功能
document.addEventListener('DOMContentLoaded', function() {
    updateWeather();
    updateTime();
    setInterval(updateTime, 60000); // 每分钟更新时间
    initSearch();
    initExport();
    initKeyboardShortcuts();
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
