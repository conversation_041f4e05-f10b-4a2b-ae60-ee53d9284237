<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新的 Product Card 结构测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        body {
            color: #333 !important;
            background-color: #f8f9fa !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            box-sizing: border-box;
        }

        .section-title {
            color: #212529 !important;
            font-weight: 700 !important;
            font-size: 1.8rem !important;
            margin: 0 0 24px 0 !important;
        }

        /* 优化后的商品卡片样式 */
        .product-card {
            background: #ffffff !important;
            color: #212529 !important;
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            overflow: hidden !important;
            position: relative !important;
        }

        .product-card:hover {
            color: #212529 !important;
            text-decoration: none !important;
            transform: none !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        .product-info {
            background: #ffffff !important;
            color: #212529 !important;
            padding: 20px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: space-between !important;
            min-height: 160px !important;
            box-sizing: border-box !important;
            position: relative !important;
            z-index: 10 !important;
        }

        .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin: 0 0 12px 0 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 新的商品信息布局样式 */
        .product-meta {
            display: flex !important;
            flex-direction: column !important;
            gap: 12px !important;
            margin: 8px 0 16px 0 !important;
        }

        .product-category {
            color: #6c757d !important;
            font-size: 0.85rem !important;
            font-weight: 500 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            display: inline-block !important;
            background-color: #f8f9fa !important;
            padding: 4px 8px !important;
            border-radius: 12px !important;
            border: 1px solid #e9ecef !important;
            align-self: flex-start !important;
        }

        .product-pricing {
            display: flex !important;
            align-items: baseline !important;
            gap: 8px !important;
            flex-wrap: wrap !important;
        }

        .current-price {
            color: #212529 !important;
            font-weight: 800 !important;
            font-size: 1.3rem !important;
            line-height: 1.2 !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .original-price {
            color: #8e9ba8 !important;
            text-decoration: line-through !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .add-to-cart-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
            color: #ffffff !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 12px 16px !important;
            font-size: 0.9rem !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25) !important;
            margin-top: auto !important;
            width: 100% !important;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35) !important;
        }

        .add-to-cart-btn:active {
            transform: translateY(0) !important;
            box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3) !important;
        }

        .add-to-cart-btn i {
            color: #ffffff !important;
            font-size: 1rem !important;
            display: inline-block !important;
        }

        .add-to-cart-btn span {
            color: #ffffff !important;
            font-weight: 600 !important;
            display: inline-block !important;
        }

        .product-badge {
            background-color: #007bff !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 6px 12px !important;
            border-radius: 20px !important;
            position: absolute !important;
            top: 12px !important;
            left: 12px !important;
            z-index: 2 !important;
        }

        .product-badge.sale {
            background-color: #ffc107 !important;
            color: #212529 !important;
            top: 12px !important;
            left: auto !important;
            right: 12px !important;
        }

        .product-badge.hot {
            background-color: #dc3545 !important;
        }

        /* 商品网格布局 */
        .product-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
            gap: 24px !important;
            margin-top: 30px !important;
        }

        .product-image-container {
            position: relative !important;
            width: 100% !important;
            height: 280px !important;
            overflow: hidden !important;
            background: #f8f9fa !important;
            z-index: 1 !important;
        }

        .product-image {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: transform 0.3s ease !important;
            position: relative !important;
            z-index: 2 !important;
        }

        .comparison-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .comparison-title {
            color: #007bff;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .improvement-list {
            list-style: none;
            padding: 0;
        }

        .improvement-list li {
            margin: 8px 0;
            padding: 8px 12px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }

        .improvement-list li::before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="section-title">新的 Product Card 结构测试</h1>

        <div class="comparison-section">
            <h3 class="comparison-title">🔄 结构改进说明</h3>
            <ul class="improvement-list">
                <li><strong>删除了原本的：</strong> <code>&lt;p class="product-origin"&gt;</code> 和复杂的 <code>&lt;div class="product-price-row"&gt;</code></li>
                <li><strong>新增了：</strong> <code>&lt;div class="product-meta"&gt;</code> 统一管理分类和价格信息</li>
                <li><strong>分类显示：</strong> 使用 <code>&lt;span class="product-category"&gt;</code> 带背景的标签样式</li>
                <li><strong>价格显示：</strong> 使用 <code>&lt;div class="product-pricing"&gt;</code> 更清晰的价格布局</li>
                <li><strong>按钮优化：</strong> 新的 <code>&lt;button class="add-to-cart-btn"&gt;</code> 全宽按钮设计</li>
                <li><strong>图标更新：</strong> 使用 <code>fa-cart-plus</code> 更现代的购物车图标</li>
            </ul>
        </div>

        <div class="product-grid">
            <!-- 新结构商品卡片 1 - 特价商品 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge hot">热门</div>
                    <div class="product-badge sale">特价</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="原神 - 刻晴星霜华裳" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                    <div class="product-meta">
                        <span class="product-category">原神</span>
                        <div class="product-pricing">
                            <span class="current-price">¥499.00</span>
                            <span class="original-price">¥599.00</span>
                        </div>
                    </div>
                    <button class="add-to-cart-btn" data-product-id="1001" title="添加到购物车">
                        <i class="fas fa-cart-plus"></i>
                        <span>加入购物车</span>
                    </button>
                </div>
            </a>

            <!-- 新结构商品卡片 2 - 普通商品 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="英雄联盟 - 阿狸" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">英雄联盟 - 阿狸</h3>
                    <div class="product-meta">
                        <span class="product-category">英雄联盟</span>
                        <div class="product-pricing">
                            <span class="current-price">¥649.00</span>
                        </div>
                    </div>
                    <button class="add-to-cart-btn" data-product-id="1002" title="添加到购物车">
                        <i class="fas fa-cart-plus"></i>
                        <span>加入购物车</span>
                    </button>
                </div>
            </a>

            <!-- 新结构商品卡片 3 - 新品 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge">新品</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="鬼灭之刃 - 炭治郎" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">鬼灭之刃 - 炭治郎</h3>
                    <div class="product-meta">
                        <span class="product-category">鬼灭之刃</span>
                        <div class="product-pricing">
                            <span class="current-price">¥599.00</span>
                        </div>
                    </div>
                    <button class="add-to-cart-btn" data-product-id="1003" title="添加到购物车">
                        <i class="fas fa-cart-plus"></i>
                        <span>加入购物车</span>
                    </button>
                </div>
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 新的 Product Card 结构测试 ===');
            
            // 检查新结构的元素
            const productCards = document.querySelectorAll('.product-card');
            console.log('找到的商品卡片数量:', productCards.length);
            
            productCards.forEach((card, index) => {
                console.log(`\n商品卡片 ${index + 1}:`);
                
                // 检查标题
                const title = card.querySelector('.product-name');
                if (title) {
                    const styles = window.getComputedStyle(title);
                    console.log('- 标题:', title.textContent, '颜色:', styles.color);
                }
                
                // 检查分类
                const category = card.querySelector('.product-category');
                if (category) {
                    const styles = window.getComputedStyle(category);
                    console.log('- 分类:', category.textContent, '颜色:', styles.color);
                }
                
                // 检查当前价格
                const currentPrice = card.querySelector('.current-price');
                if (currentPrice) {
                    const styles = window.getComputedStyle(currentPrice);
                    console.log('- 当前价格:', currentPrice.textContent, '颜色:', styles.color);
                }
                
                // 检查原价
                const originalPrice = card.querySelector('.original-price');
                if (originalPrice) {
                    const styles = window.getComputedStyle(originalPrice);
                    console.log('- 原价:', originalPrice.textContent, '颜色:', styles.color);
                }
                
                // 检查按钮
                const button = card.querySelector('.add-to-cart-btn');
                if (button) {
                    const styles = window.getComputedStyle(button);
                    console.log('- 按钮背景:', styles.backgroundColor);
                }
            });
            
            // 添加按钮点击事件
            document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    console.log('点击了加入购物车按钮，商品ID:', productId);
                    
                    // 简单的视觉反馈
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
