<?php
/**
 * 网站头部模板
 * 包含导航栏和通用头部元素
 */

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 获取当前页面信息
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$page_title = $page_title ?? 'COSPlay购物网站';
$page_description = $page_description ?? '专业的角色扮演服装购物平台，为您提供高品质的动漫、游戏角色服装及配饰。';

// 网站配置
$site_url = 'http://localhost/cosplay';

// 获取购物车数量（模拟数据）
$cart_count = 0;
if (isset($_SESSION['cart'])) {
    $cart_count = array_sum(array_column($_SESSION['cart'], 'quantity'));
}

// 获取当前登录用户信息
$current_user = $_SESSION['user'] ?? null;

// 生成CSRF令牌（如果函数不存在）
if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="COSplay,角色扮演,动漫服装,游戏服装,原神,英雄联盟">
    <meta name="author" content="COSPlay购物网站">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo $site_url . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo $site_url; ?>/images/og-image.jpg">
    
    <title><?php echo htmlspecialchars($page_title); ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/enhanced-styles.css">
    <link rel="stylesheet" href="../css/card-styles.css">
    <link rel="stylesheet" href="css/layout-styles.css">
    <link rel="stylesheet" href="css/navbar-unified.css">
    <link rel="stylesheet" href="css/detail-styles.css">
    <link rel="stylesheet" href="css/category-fix.css">
    <link rel="stylesheet" href="css/category-responsive.css">
    <link rel="stylesheet" href="css/text-clarity-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>

<body>
    <!-- 跳转链接已删除 -->

    <!-- 顶部导航栏 -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="navbar-container">
                <a href="index.php" class="logo" aria-label="COSPlay首页">COS<span>Play</span></a>

                <div class="search-container">
                    <form action="category.php" method="get" role="search">
                        <input type="text" class="search-input" name="search"
                               placeholder="搜索角色、动漫或游戏..."
                               aria-label="搜索角色、动漫或游戏"
                               value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        <button type="submit" class="search-btn" aria-label="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                    <div class="search-results" aria-live="polite">
                        <!-- 搜索结果将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="nav-links">
                    <a href="category.php" class="nav-link <?php echo $current_page === 'category' ? 'active' : ''; ?>" aria-label="商品分类">
                        <i class="fas fa-th-large nav-link-icon"></i>
                        <span class="nav-link-text">分类</span>
                    </a>
                    <a href="cart.php" class="nav-link <?php echo $current_page === 'cart' ? 'active' : ''; ?>" aria-label="购物车">
                        <i class="fas fa-shopping-cart nav-link-icon"></i>
                        <?php if ($cart_count > 0): ?>
                            <span class="nav-link-count"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                        <span class="nav-link-text">购物车</span>
                    </a>
                    
                    <?php if ($current_user): ?>
                        <!-- 已登录用户菜单 -->
                        <div class="user-dropdown">
                            <button class="nav-link user-dropdown-toggle" aria-label="用户中心" type="button">
                                <?php if ($current_user['avatar']): ?>
                                    <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>"
                                         alt="用户头像" class="nav-user-avatar">
                                <?php else: ?>
                                    <i class="fas fa-user nav-link-icon"></i>
                                <?php endif; ?>
                                <span class="nav-link-text">
                                    <?php echo htmlspecialchars($current_user['nickname'] ?? $current_user['username'] ?? '用户'); ?>
                                </span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                            <div class="user-menu">
                                <a href="user.php" class="user-menu-item">
                                    <i class="fas fa-user"></i> 个人中心
                                </a>
                                <a href="order.php" class="user-menu-item">
                                    <i class="fas fa-shopping-bag"></i> 我的订单
                                </a>
                                <a href="user.php#wishlist" class="user-menu-item">
                                    <i class="fas fa-heart"></i> 心愿单
                                </a>
                                <a href="address.php" class="user-menu-item">
                                    <i class="fas fa-map-marker-alt"></i> 地址管理
                                </a>
                                <div class="user-menu-divider"></div>
                                <a href="login.php?action=logout" class="user-menu-item">
                                    <i class="fas fa-sign-out-alt"></i> 退出登录
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- 未登录用户菜单 -->
                        <div class="user-dropdown">
                            <button class="nav-link user-dropdown-toggle" aria-label="用户菜单" type="button">
                                <i class="fas fa-user nav-link-icon"></i>
                                <span class="nav-link-text">登录</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                            <div class="user-menu">
                                <a href="login.php" class="user-menu-item">
                                    <i class="fas fa-sign-in-alt"></i> 登录
                                </a>
                                <a href="login.php#register" class="user-menu-item">
                                    <i class="fas fa-user-plus"></i> 注册
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <button class="menu-toggle" aria-label="菜单" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- 通知消息 -->
    <?php if (isset($_SESSION['flash_messages'])): ?>
        <div class="toast-container">
            <?php foreach ($_SESSION['flash_messages'] as $type => $messages): ?>
                <?php foreach ($messages as $message): ?>
                    <div class="toast-message toast-<?php echo $type; ?>">
                        <div class="toast-icon">
                        <i class="fas fa-<?php echo $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle'); ?>"></i>
                        </div>
                        <div class="toast-content">
                        <span><?php echo htmlspecialchars($message); ?></span>
                        </div>
                        <button class="toast-close" aria-label="关闭">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </div>
        <?php unset($_SESSION['flash_messages']); ?>
    <?php endif; ?>

    <!-- 主要内容区域开始 -->
    <main id="main-content"><?php
// 注意：这里不关闭main标签，在footer.php中关闭
?>

<!-- 用户下拉菜单样式 -->
<style>
/* 用户下拉菜单容器 */
.user-dropdown {
    position: relative;
    display: inline-block;
}

/* 用户下拉菜单按钮 */
.user-dropdown-toggle {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.user-dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 全局文字清晰修复 - 移除所有模糊效果 */
* {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 下拉箭头动画 - 保留必要的transform */
.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 12px;
}

.user-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 用户头像 */
.nav-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 用户下拉菜单 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

/* 显示状态 */
.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 菜单项 */
.user-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.user-menu-item:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.user-menu-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.user-menu-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

.user-menu-item i {
    width: 16px;
    text-align: center;
    color: #666;
}

.user-menu-item:hover i {
    color: #007bff;
}

/* 分割线 */
.user-menu-divider {
    height: 1px;
    background: #e0e0e0;
    margin: 8px 0;
}

/* 弹窗通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

.toast-message {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    overflow: hidden;
    pointer-events: auto;
    max-width: 100%;
    position: relative;
    border-left: 4px solid #007bff;
}

.toast-message.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    border-left-color: #28a745;
}

.toast-error {
    border-left-color: #dc3545;
}

.toast-info {
    border-left-color: #17a2b8;
}

.toast-warning {
    border-left-color: #ffc107;
}

.toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 12px;
    flex-shrink: 0;
}

.toast-success .toast-icon i {
    color: #28a745;
}

.toast-error .toast-icon i {
    color: #dc3545;
}

.toast-info .toast-icon i {
    color: #17a2b8;
}

.toast-warning .toast-icon i {
    color: #ffc107;
}

.toast-content {
    flex-grow: 1;
    font-size: 14px;
    color: #333;
}

.toast-close {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333;
        }

/* 响应式设计 */
@media (max-width: 768px) {
    .user-menu {
        position: fixed;
        top: auto !important;
        right: 10px !important;
        left: 10px;
        width: auto;
        min-width: auto;
    }
    
    .toast-container {
        left: 20px;
        right: 20px;
        width: auto;
        max-width: none;
    }
}
</style>

<!-- 导航栏样式已移至 css/layout-styles.css -->

<!-- 用户下拉菜单脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 查找所有用户下拉菜单按钮
    const dropdownToggles = document.querySelectorAll('.user-dropdown-toggle');

    // 为每个下拉按钮添加点击事件
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 获取菜单元素和下拉容器
            const dropdown = this.closest('.user-dropdown');
            const menu = dropdown.querySelector('.user-menu');

            // 切换当前菜单的显示状态
            const isActive = dropdown.classList.contains('active');

            // 先关闭所有其他菜单
            document.querySelectorAll('.user-dropdown').forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('active');
                    const otherMenu = otherDropdown.querySelector('.user-menu');
                    if (otherMenu) {
                        otherMenu.classList.remove('show');
                    }
                }
            });

            // 切换当前菜单状态
            if (isActive) {
                dropdown.classList.remove('active');
                menu.classList.remove('show');
            } else {
                dropdown.classList.add('active');
                menu.classList.add('show');
            }
        });
    });

    // 点击页面其他地方关闭菜单
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.user-dropdown')) {
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });

    // 键盘支持 - ESC键关闭菜单
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });

    // 处理通知消息
    const toastMessages = document.querySelectorAll('.toast-message');
    if (toastMessages.length > 0) {
        // 显示通知
        toastMessages.forEach((toast, index) => {
            // 延迟显示，让通知一个接一个弹出
            setTimeout(() => {
                toast.classList.add('show');
                
                // 自动关闭通知（5秒后）
                setTimeout(() => {
                    closeToast(toast);
                }, 5000 + (index * 300)); // 根据索引增加一点延迟，使消失也是一个接一个
            }, 300 * index);
    });

        // 关闭按钮事件
        document.querySelectorAll('.toast-close').forEach(btn => {
            btn.addEventListener('click', function() {
                const toast = this.closest('.toast-message');
                closeToast(toast);
            });
        });
        
        // 关闭通知的函数
        function closeToast(toast) {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(20px)';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }
});
</script>
