<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Grid H3 标题测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            color: #212529;
            font-weight: 700;
            font-size: 1.8rem;
            margin-bottom: 24px;
        }

        /* 商品网格布局 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 24px;
            margin-top: 30px;
        }

        /* 商品卡片样式 */
        .product-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .product-card:hover {
            text-decoration: none;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
            border-color: #007bff;
        }

        .product-image-container {
            position: relative;
            width: 100%;
            height: 280px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-badge {
            background-color: #007bff;
            color: #ffffff;
            font-weight: 600;
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            position: absolute;
            top: 12px;
            left: 12px;
            z-index: 2;
        }

        .product-badge.new {
            background-color: #28a745;
        }

        .product-badge.sale {
            background-color: #ffc107;
            color: #212529;
            top: 12px;
            left: auto;
            right: 12px;
        }

        /* 商品信息区域 */
        .product-info {
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 140px;
            box-sizing: border-box;
            background: #ffffff;
        }

        /* H3 标题样式 - 重点修复 */
        .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin: 0 0 8px 0 !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
        }

        /* 确保 h3 标签可见 */
        h3.product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            margin: 0 0 8px 0 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-origin {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0 0 12px 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .product-price-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: auto;
            padding-top: 8px;
            gap: 8px;
        }

        .product-price-container {
            display: flex;
            align-items: baseline;
            gap: 6px;
            flex: 1;
        }

        .product-price {
            color: #212529;
            font-weight: 800;
            font-size: 1.25rem;
            margin: 0;
        }

        .product-price-original {
            color: #8e9ba8;
            text-decoration: line-through;
            font-size: 1rem;
            font-weight: 500;
        }

        .add-to-cart {
            background-color: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .add-to-cart:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        .add-to-cart i {
            color: #ffffff;
            font-size: 14px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
                gap: 16px;
            }
            
            .product-name {
                font-size: 1rem !important;
            }
        }

        @media (max-width: 480px) {
            .product-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
            
            .product-name {
                font-size: 0.95rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="section-title">Product Grid H3 标题测试</h2>
        
        <div class="product-grid">
            <!-- 测试商品卡片 1 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge">热门</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="原神 - 刻晴星霜华裳" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                    <p class="product-origin">原神</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥499.00</span>
                            <span class="product-price-original">¥599.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>

            <!-- 测试商品卡片 2 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge new">新品</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="英雄联盟 - 阿狸" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">英雄联盟 - 阿狸</h3>
                    <p class="product-origin">英雄联盟</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥649.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>

            <!-- 测试商品卡片 3 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge sale">特价</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="鬼灭之刃 - 炭治郎" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">鬼灭之刃 - 炭治郎</h3>
                    <p class="product-origin">鬼灭之刃</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥599.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>

            <!-- 测试商品卡片 4 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge">热门</div>
                    <div class="product-badge sale">特价</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="赛博朋克2077" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">赛博朋克2077</h3>
                    <p class="product-origin">赛博朋克</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥599.00</span>
                            <span class="product-price-original">¥699.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <script>
        // 检查 h3 标题是否可见
        document.addEventListener('DOMContentLoaded', function() {
            const h3Elements = document.querySelectorAll('h3.product-name');
            console.log('找到的 h3 标题数量:', h3Elements.length);
            
            h3Elements.forEach((h3, index) => {
                const styles = window.getComputedStyle(h3);
                console.log(`H3 标题 ${index + 1}:`, {
                    text: h3.textContent,
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity,
                    color: styles.color,
                    fontSize: styles.fontSize,
                    fontWeight: styles.fontWeight
                });
            });
        });
    </script>
</body>
</html>
