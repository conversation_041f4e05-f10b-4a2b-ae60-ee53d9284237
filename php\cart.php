<?php
/**
 * COSPlay购物网站 - 购物车页面
 * 显示购物车商品、数量调整和结算功能
 */

// 启动会话
session_start();

// 页面信息设置
$page_title = '购物车 - COSPlay购物网站';
$page_description = '查看您的购物车商品，调整数量并进行结算';

// 购物车管理类
class CartManager {
    private $session_key = 'shopping_cart';

    public function __construct() {
        if (!isset($_SESSION[$this->session_key])) {
            $_SESSION[$this->session_key] = [];
        }
    }

    // 获取购物车商品
    public function getItems() {
        return $_SESSION[$this->session_key];
    }

    // 添加商品到购物车
    public function addItem($product_id, $name, $price, $sale_price = null, $size = '', $color = '', $sku = '', $image = '', $quantity = 1) {
        $item_id = $this->generateItemId($product_id, $size, $color);

        if (isset($_SESSION[$this->session_key][$item_id])) {
            $_SESSION[$this->session_key][$item_id]['quantity'] += $quantity;
        } else {
            $_SESSION[$this->session_key][$item_id] = [
                'id' => $item_id,
                'product_id' => $product_id,
                'name' => $name,
                'image' => $image,
                'price' => $price,
                'sale_price' => $sale_price,
                'quantity' => $quantity,
                'size' => $size,
                'color' => $color,
                'sku' => $sku
            ];
        }
    }

    // 更新商品数量
    public function updateQuantity($item_id, $quantity) {
        if (isset($_SESSION[$this->session_key][$item_id])) {
            if ($quantity <= 0) {
                $this->removeItem($item_id);
            } else {
                $_SESSION[$this->session_key][$item_id]['quantity'] = $quantity;
            }
        }
    }

    // 软删除商品（标记为删除但不移除）
    public function softRemoveItem($item_id) {
        if (isset($_SESSION[$this->session_key][$item_id])) {
            $_SESSION[$this->session_key][$item_id]['is_deleted'] = true;
            $_SESSION[$this->session_key][$item_id]['deleted_at'] = time();
        }
    }

    // 恢复已删除的商品
    public function restoreItem($item_id) {
        if (isset($_SESSION[$this->session_key][$item_id])) {
            unset($_SESSION[$this->session_key][$item_id]['is_deleted']);
            unset($_SESSION[$this->session_key][$item_id]['deleted_at']);
        }
    }

    // 永久删除商品
    public function removeItem($item_id) {
        if (isset($_SESSION[$this->session_key][$item_id])) {
            unset($_SESSION[$this->session_key][$item_id]);
        }
    }

    // 移动到心愿单
    public function moveToWishlist($item_id) {
        if (isset($_SESSION[$this->session_key][$item_id])) {
            $item = $_SESSION[$this->session_key][$item_id];

            // 初始化心愿单
            if (!isset($_SESSION['wishlist'])) {
                $_SESSION['wishlist'] = [];
            }

            // 添加到心愿单
            $_SESSION['wishlist'][$item_id] = [
                'product_id' => $item['product_id'],
                'name' => $item['name'],
                'image' => $item['image'],
                'price' => $item['price'],
                'sale_price' => $item['sale_price'],
                'size' => $item['size'],
                'color' => $item['color'],
                'sku' => $item['sku'],
                'added_at' => time()
            ];

            // 从购物车删除
            unset($_SESSION[$this->session_key][$item_id]);
        }
    }

    // 清空购物车
    public function clearCart() {
        $_SESSION[$this->session_key] = [];
    }

    // 获取商品总数（排除已删除的商品）
    public function getTotalItems() {
        $total = 0;
        foreach ($_SESSION[$this->session_key] as $item) {
            if (!isset($item['is_deleted']) || !$item['is_deleted']) {
                $total += $item['quantity'];
            }
        }
        return $total;
    }

    // 计算小计（排除已删除的商品）
    public function getSubtotal() {
        $subtotal = 0;
        foreach ($_SESSION[$this->session_key] as $item) {
            if (!isset($item['is_deleted']) || !$item['is_deleted']) {
                $item_price = $item['sale_price'] ?: $item['price'];
                $subtotal += $item_price * $item['quantity'];
            }
        }
        return $subtotal;
    }

    // 获取已删除的商品（用于撤销功能）
    public function getDeletedItems() {
        $deleted_items = [];
        foreach ($_SESSION[$this->session_key] as $item_id => $item) {
            if (isset($item['is_deleted']) && $item['is_deleted']) {
                $deleted_items[$item_id] = $item;
            }
        }
        return $deleted_items;
    }

    // 清理过期的已删除商品（超过5分钟自动永久删除）
    public function cleanupDeletedItems() {
        $current_time = time();
        foreach ($_SESSION[$this->session_key] as $item_id => $item) {
            if (isset($item['is_deleted']) && $item['is_deleted'] &&
                isset($item['deleted_at']) &&
                ($current_time - $item['deleted_at']) > 300) { // 5分钟
                unset($_SESSION[$this->session_key][$item_id]);
            }
        }
    }

    // 生成商品ID（基于商品ID、尺码、颜色）
    private function generateItemId($product_id, $size, $color) {
        return $product_id . '_' . $size . '_' . $color;
    }
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $cart = new CartManager();
    $response = ['success' => false, 'message' => ''];

    switch ($_POST['action']) {
        case 'update_quantity':
            if (isset($_POST['item_id']) && isset($_POST['quantity'])) {
                $cart->updateQuantity($_POST['item_id'], (int)$_POST['quantity']);
                $response['success'] = true;
                $response['message'] = '数量已更新';
                $response['subtotal'] = $cart->getSubtotal();
                $response['total_items'] = $cart->getTotalItems();
            }
            break;

        case 'soft_remove_item':
            if (isset($_POST['item_id'])) {
                $cart->softRemoveItem($_POST['item_id']);
                $response['success'] = true;
                $response['message'] = '商品已移除，可在5分钟内撤销';
                $response['subtotal'] = $cart->getSubtotal();
                $response['total_items'] = $cart->getTotalItems();
                $response['deleted_items'] = $cart->getDeletedItems();
            }
            break;

        case 'restore_item':
            if (isset($_POST['item_id'])) {
                $cart->restoreItem($_POST['item_id']);
                $response['success'] = true;
                $response['message'] = '商品已恢复';
                $response['subtotal'] = $cart->getSubtotal();
                $response['total_items'] = $cart->getTotalItems();
            }
            break;

        case 'move_to_wishlist':
            if (isset($_POST['item_id'])) {
                $cart->moveToWishlist($_POST['item_id']);
                $response['success'] = true;
                $response['message'] = '商品已移至心愿单';
                $response['subtotal'] = $cart->getSubtotal();
                $response['total_items'] = $cart->getTotalItems();
            }
            break;

        case 'remove_item':
            if (isset($_POST['item_id'])) {
                $cart->removeItem($_POST['item_id']);
                $response['success'] = true;
                $response['message'] = '商品已永久删除';
                $response['subtotal'] = $cart->getSubtotal();
                $response['total_items'] = $cart->getTotalItems();
            }
            break;

        case 'clear_cart':
            $cart->clearCart();
            $response['success'] = true;
            $response['message'] = '购物车已清空';
            break;
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// 初始化购物车
$cart = new CartManager();

// 处理来自JavaScript的购物车数据同步
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'sync_cart') {
    if (isset($_POST['cart_data'])) {
        $cart_data = json_decode($_POST['cart_data'], true);
        if ($cart_data && is_array($cart_data)) {
            // 清空当前购物车
            $cart->clearCart();

            // 添加JavaScript传来的商品
            foreach ($cart_data as $item) {
                if (isset($item['product_id'], $item['name'], $item['price'], $item['quantity'])) {
                    $cart->addItem(
                        $item['product_id'],
                        $item['name'],
                        $item['price'],
                        $item['sale_price'] ?? null,
                        $item['size'] ?? '',
                        $item['color'] ?? '',
                        $item['sku'] ?? '',
                        $item['image'] ?? '',
                        $item['quantity']
                    );
                }
            }
        }
    }

    $response = [
        'success' => true,
        'message' => '购物车已同步',
        'subtotal' => $cart->getSubtotal(),
        'total_items' => $cart->getTotalItems()
    ];

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// 清理过期的已删除商品
$cart->cleanupDeletedItems();

// 获取购物车数据（只获取未删除的商品）
$all_cart_items = $cart->getItems();
$cart_items = [];
foreach ($all_cart_items as $item_id => $item) {
    if (!isset($item['is_deleted']) || !$item['is_deleted']) {
        $cart_items[$item_id] = $item;
    }
}

$deleted_items = $cart->getDeletedItems();
$subtotal = $cart->getSubtotal();
$total_items = $cart->getTotalItems();
$shipping_fee = $subtotal >= 299 ? 0 : 15; // 满299免运费
$total = $subtotal + $shipping_fee;

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.cart-title {
    color: #212529 !important;
}

.cart-summary {
    color: #6c757d !important;
}

.item-name {
    color: #212529 !important;
}

.item-origin {
    color: #6c757d !important;
}

.current-price {
    color: #007bff !important;
}

.original-price {
    color: #6c757d !important;
}

.total-price {
    color: #212529 !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.btn-primary, .checkout-btn, .apply-coupon-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.qty-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.remove-btn {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保购物车项目文字可见 */
.cart-item {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* 确保侧边栏文字可见 */
.cart-summary-card, .coupon-card, .recommended-products {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* 确保空购物车文字可见 */
.empty-cart {
    background-color: #ffffff !important;
    color: #6c757d !important;
}

/* 确保表单文字可见 */
.coupon-input {
    background-color: #ffffff !important;
    color: #495057 !important;
}

/* 确保推荐商品文字可见 */
.recommended-name {
    color: #212529 !important;
}

.recommended-price {
    color: #007bff !important;
}
</style>

<!-- 购物车JavaScript管理器 -->
<script>
// 购物车本地存储管理器
class CartStorageManager {
    constructor() {
        this.storageKey = 'cosplay_shopping_cart';
        this.deletedKey = 'cosplay_deleted_items';
        this.wishlistKey = 'cosplay_wishlist';
        this.initializeDefaultData();
    }

    // 初始化默认数据（仅在第一次访问时）
    initializeDefaultData() {
        if (!this.hasCartData()) {
            const defaultItems = [
                {
                    id: '1001_M_紫色',
                    product_id: 1001,
                    name: '原神 - 刻晴星霜华裳',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=200&fit=crop',
                    price: 599.00,
                    sale_price: 499.00,
                    quantity: 2,
                    size: 'M',
                    color: '紫色',
                    sku: 'GI-KQ-001'
                },
                {
                    id: '1002_L_白色',
                    product_id: 1002,
                    name: '英雄联盟 - 阿狸',
                    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=150&h=200&fit=crop',
                    price: 649.00,
                    sale_price: null,
                    quantity: 1,
                    size: 'L',
                    color: '白色',
                    sku: 'LOL-AH-001'
                }
            ];
            this.saveCartData(defaultItems);
        }
    }

    // 检查是否有购物车数据
    hasCartData() {
        return localStorage.getItem(this.storageKey) !== null;
    }

    // 获取购物车数据
    getCartData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : [];
        } catch (e) {
            console.error('Error parsing cart data:', e);
            return [];
        }
    }

    // 保存购物车数据
    saveCartData(cartData) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(cartData));
            return true;
        } catch (e) {
            console.error('Error saving cart data:', e);
            return false;
        }
    }

    // 获取已删除商品数据
    getDeletedItems() {
        try {
            const data = localStorage.getItem(this.deletedKey);
            return data ? JSON.parse(data) : {};
        } catch (e) {
            console.error('Error parsing deleted items:', e);
            return {};
        }
    }

    // 保存已删除商品数据
    saveDeletedItems(deletedData) {
        try {
            localStorage.setItem(this.deletedKey, JSON.stringify(deletedData));
            return true;
        } catch (e) {
            console.error('Error saving deleted items:', e);
            return false;
        }
    }

    // 获取心愿单数据
    getWishlistData() {
        try {
            const data = localStorage.getItem(this.wishlistKey);
            return data ? JSON.parse(data) : [];
        } catch (e) {
            console.error('Error parsing wishlist data:', e);
            return [];
        }
    }

    // 保存心愿单数据
    saveWishlistData(wishlistData) {
        try {
            localStorage.setItem(this.wishlistKey, JSON.stringify(wishlistData));
            return true;
        } catch (e) {
            console.error('Error saving wishlist data:', e);
            return false;
        }
    }

    // 添加商品到购物车
    addItem(item) {
        const cartData = this.getCartData();
        const existingIndex = cartData.findIndex(cartItem => cartItem.id === item.id);

        if (existingIndex >= 0) {
            cartData[existingIndex].quantity += item.quantity || 1;
        } else {
            cartData.push({
                ...item,
                quantity: item.quantity || 1
            });
        }

        return this.saveCartData(cartData);
    }

    // 更新商品数量
    updateQuantity(itemId, quantity) {
        const cartData = this.getCartData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);

        if (itemIndex >= 0) {
            if (quantity <= 0) {
                cartData.splice(itemIndex, 1);
            } else {
                cartData[itemIndex].quantity = quantity;
            }
            return this.saveCartData(cartData);
        }
        return false;
    }

    // 软删除商品
    softRemoveItem(itemId) {
        const cartData = this.getCartData();
        const deletedItems = this.getDeletedItems();
        const itemIndex = cartData.findIndex(item => item.id === itemId);

        if (itemIndex >= 0) {
            const item = cartData[itemIndex];
            deletedItems[itemId] = {
                ...item,
                deleted_at: Date.now()
            };

            cartData.splice(itemIndex, 1);
            this.saveCartData(cartData);
            this.saveDeletedItems(deletedItems);
            return true;
        }
        return false;
    }

    // 恢复已删除的商品
    restoreItem(itemId) {
        const deletedItems = this.getDeletedItems();

        if (deletedItems[itemId]) {
            const item = { ...deletedItems[itemId] };
            delete item.deleted_at;

            this.addItem(item);
            delete deletedItems[itemId];
            this.saveDeletedItems(deletedItems);
            return true;
        }
        return false;
    }

    // 永久删除商品
    removeItem(itemId) {
        const cartData = this.getCartData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);

        if (itemIndex >= 0) {
            cartData.splice(itemIndex, 1);
            return this.saveCartData(cartData);
        }
        return false;
    }

    // 移动到心愿单
    moveToWishlist(itemId) {
        const cartData = this.getCartData();
        const wishlistData = this.getWishlistData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);

        if (itemIndex >= 0) {
            const item = cartData[itemIndex];
            wishlistData.push({
                ...item,
                added_at: Date.now()
            });

            cartData.splice(itemIndex, 1);
            this.saveCartData(cartData);
            this.saveWishlistData(wishlistData);
            return true;
        }
        return false;
    }

    // 清空购物车
    clearCart() {
        return this.saveCartData([]);
    }

    // 获取商品总数
    getTotalItems() {
        const cartData = this.getCartData();
        return cartData.reduce((total, item) => total + item.quantity, 0);
    }

    // 计算小计
    getSubtotal() {
        const cartData = this.getCartData();
        return cartData.reduce((total, item) => {
            const price = item.sale_price || item.price;
            return total + (price * item.quantity);
        }, 0);
    }

    // 清理过期的已删除商品（超过5分钟）
    cleanupExpiredItems() {
        const deletedItems = this.getDeletedItems();
        const currentTime = Date.now();
        const fiveMinutes = 5 * 60 * 1000; // 5分钟

        let hasChanges = false;
        for (const itemId in deletedItems) {
            if (deletedItems[itemId].deleted_at &&
                (currentTime - deletedItems[itemId].deleted_at) > fiveMinutes) {
                delete deletedItems[itemId];
                hasChanges = true;
            }
        }

        if (hasChanges) {
            this.saveDeletedItems(deletedItems);
        }
    }

    // 同步数据到服务器
    async syncToServer() {
        const cartData = this.getCartData();

        try {
            const response = await fetch('cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'sync_cart',
                    cart_data: JSON.stringify(cartData)
                })
            });

            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('Error syncing to server:', error);
            return false;
        }
    }
}

// 全局购物车管理器实例
window.cartStorage = new CartStorageManager();

// 通用购物车操作函数
window.CartUtils = {
    // 添加商品到购物车
    addToCart: function(productData) {
        try {
            // 生成唯一ID
            const itemId = `${productData.product_id}_${productData.size || 'default'}_${productData.color || 'default'}`;

            // 构建商品对象
            const item = {
                id: itemId,
                product_id: productData.product_id,
                name: productData.name,
                image: productData.image || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=200&fit=crop',
                price: parseFloat(productData.price) || 0,
                sale_price: productData.sale_price ? parseFloat(productData.sale_price) : null,
                quantity: parseInt(productData.quantity) || 1,
                size: productData.size || '',
                color: productData.color || '',
                sku: productData.sku || '',
                package: productData.package || ''
            };

            // 添加到购物车
            const success = window.cartStorage.addItem(item);

            if (success) {
                // 同步到服务器
                window.cartStorage.syncToServer();

                // 更新购物车图标数量
                this.updateCartIcon();

                // 显示成功提示
                this.showMessage('商品已添加到购物车！', 'success');

                return true;
            } else {
                this.showMessage('添加失败，请重试', 'error');
                return false;
            }
        } catch (error) {
            console.error('添加到购物车失败:', error);
            this.showMessage('添加失败，请重试', 'error');
            return false;
        }
    },

    // 更新购物车图标数量
    updateCartIcon: function() {
        const totalItems = window.cartStorage.getTotalItems();

        // 更新导航栏购物车数量
        const cartBadges = document.querySelectorAll('.cart-badge, .cart-count');
        cartBadges.forEach(badge => {
            if (badge) {
                badge.textContent = totalItems;
                badge.style.display = totalItems > 0 ? 'inline' : 'none';
            }
        });

        // 更新购物车图标
        const cartIcons = document.querySelectorAll('.cart-icon');
        cartIcons.forEach(icon => {
            if (totalItems > 0) {
                icon.classList.add('has-items');
            } else {
                icon.classList.remove('has-items');
            }
        });
    },

    // 显示消息提示
    showMessage: function(message, type = 'success') {
        // 移除现有的消息
        const existingMessage = document.querySelector('.cart-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息
        const messageDiv = document.createElement('div');
        messageDiv.className = `cart-message cart-message-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;
        messageDiv.textContent = message;

        // 添加CSS动画
        if (!document.querySelector('#cart-message-styles')) {
            const style = document.createElement('style');
            style.id = 'cart-message-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动隐藏
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 300);
        }, 3000);
    },

    // 初始化购物车功能
    init: function() {
        // 更新购物车图标
        this.updateCartIcon();

        // 绑定所有"加入购物车"按钮
        this.bindAddToCartButtons();

        // 监听存储变化
        window.addEventListener('storage', () => {
            this.updateCartIcon();
        });
    },

    // 绑定加入购物车按钮
    bindAddToCartButtons: function() {
        // 绑定所有加入购物车按钮
        document.querySelectorAll('.add-to-cart, .add-to-cart-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // 获取商品数据
                const productData = this.getProductDataFromButton(button);

                if (productData) {
                    // 添加按钮动效
                    button.classList.add('adding');

                    // 添加到购物车
                    const success = this.addToCart(productData);

                    if (success) {
                        button.classList.add('added');
                        setTimeout(() => {
                            button.classList.remove('adding', 'added');
                        }, 1000);
                    } else {
                        button.classList.remove('adding');
                    }
                } else {
                    this.showMessage('商品信息获取失败', 'error');
                }
            });
        });
    },

    // 从按钮获取商品数据
    getProductDataFromButton: function(button) {
        try {
            // 获取商品ID
            const productId = button.dataset.productId || button.getAttribute('data-product-id');

            if (!productId) {
                console.error('未找到商品ID');
                return null;
            }

            // 查找商品容器
            const productCard = button.closest('.product-card, .product-item, .product-info, .product-details');

            if (!productCard) {
                console.error('未找到商品容器');
                return null;
            }

            // 提取商品信息
            const nameElement = productCard.querySelector('.product-name, .item-name, h1, h2, h3');
            const priceElement = productCard.querySelector('.product-price, .current-price, .price');
            const salePriceElement = productCard.querySelector('.sale-price, .discounted-price');
            const imageElement = productCard.querySelector('img');

            // 获取选中的尺码和颜色（如果有）
            const selectedSize = document.querySelector('.size-option.active')?.dataset.size ||
                               document.querySelector('input[name="size"]:checked')?.value || '';
            const selectedColor = document.querySelector('.color-option.active')?.dataset.color ||
                                document.querySelector('input[name="color"]:checked')?.value || '';

            // 获取数量
            const qtyInput = document.querySelector('.qty-input, input[name="quantity"]');
            const quantity = qtyInput ? parseInt(qtyInput.value) || 1 : 1;

            // 构建商品数据
            const productData = {
                product_id: parseInt(productId),
                name: nameElement ? nameElement.textContent.trim() : `商品 ${productId}`,
                price: this.extractPrice(priceElement?.textContent),
                sale_price: salePriceElement ? this.extractPrice(salePriceElement.textContent) : null,
                image: imageElement ? imageElement.src : '',
                size: selectedSize,
                color: selectedColor,
                quantity: quantity,
                sku: `PROD-${productId}`,
                package: document.querySelector('input[name="package"]:checked')?.value || ''
            };

            return productData;
        } catch (error) {
            console.error('获取商品数据失败:', error);
            return null;
        }
    },

    // 提取价格数字
    extractPrice: function(priceText) {
        if (!priceText) return 0;
        const match = priceText.match(/[\d,]+\.?\d*/);
        return match ? parseFloat(match[0].replace(/,/g, '')) : 0;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.CartUtils) {
        window.CartUtils.init();
    }
});
</script>

<style>
/* 购物车页面样式 */
.cart-page {
    min-height: 80vh;
    padding: 40px 0;
    background: #f8f9fa;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.cart-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.cart-title i {
    color: #007bff;
}

.cart-summary {
    font-size: 1.1rem;
    color: #6c757d;
}

.cart-summary strong {
    color: #007bff;
    font-weight: 600;
}

.cart-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
    align-items: start;
}

/* 购物车商品列表 */
.cart-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.cart-item {
    display: grid;
    grid-template-columns: 120px 1fr auto auto auto auto;
    gap: 20px;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cart-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.item-image {
    width: 120px;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.4;
}

.item-origin {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.item-specs {
    display: flex;
    gap: 15px;
}

.spec {
    font-size: 0.85rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.item-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: #e74c3c;
}

.original-price {
    font-size: 0.9rem;
    color: #6c757d;
    text-decoration: line-through;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.qty-btn:hover {
    background: #0056b3;
}

.qty-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.qty-input {
    width: 50px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    background: white;
}

.item-total {
    text-align: center;
}

.total-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.item-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.action-btn {
    width: 40px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn {
    background: #dc3545;
    color: white;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.wishlist-btn {
    background: #ffc107;
    color: #212529;
}

.wishlist-btn:hover {
    background: #e0a800;
    transform: scale(1.05);
}

/* 删除选项弹窗 */
.delete-options-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.delete-options-modal.show {
    opacity: 1;
    visibility: visible;
}

.delete-options-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.delete-options-modal.show .delete-options-content {
    transform: scale(1);
}

.delete-options-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

.delete-options-subtitle {
    color: #6c757d;
    margin-bottom: 25px;
    text-align: center;
    line-height: 1.5;
}

.delete-options-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.delete-option-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.delete-option-btn:hover {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
}

.delete-option-btn i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.delete-option-btn.soft-delete i {
    color: #ffc107;
}

.delete-option-btn.move-wishlist i {
    color: #e74c3c;
}

.delete-option-btn.permanent-delete i {
    color: #dc3545;
}

.delete-option-info {
    flex: 1;
}

.delete-option-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.delete-option-desc {
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.4;
}

.delete-options-cancel {
    margin-top: 15px;
    padding: 12px 20px;
    border: 2px solid #6c757d;
    border-radius: 8px;
    background: white;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    font-weight: 600;
}

.delete-options-cancel:hover {
    background: #6c757d;
    color: white;
}

/* 撤销提示条 */
.undo-bar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #2c3e50;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.undo-bar.show {
    opacity: 1;
    visibility: visible;
}

.undo-bar-text {
    flex: 1;
    font-size: 0.9rem;
}

.undo-btn {
    padding: 8px 16px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.undo-btn:hover {
    background: #0056b3;
}

.undo-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.undo-close:hover {
    opacity: 0.7;
}

/* 加载占位符 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    color: #6c757d;
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #007bff;
}

.loading-placeholder p {
    margin: 0;
    font-size: 1.1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 购物车按钮动效 */
.add-to-cart, .add-to-cart-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.add-to-cart.adding, .add-to-cart-btn.adding {
    transform: scale(0.95);
    opacity: 0.8;
}

.add-to-cart.added, .add-to-cart-btn.added {
    background: #28a745 !important;
    transform: scale(1.05);
}

.add-to-cart.added::after, .add-to-cart-btn.added::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
    color: white;
    animation: checkmark 0.5s ease;
}

@keyframes checkmark {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
    100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

/* 购物车图标状态 */
.cart-icon.has-items {
    animation: cartBounce 0.5s ease;
}

@keyframes cartBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 空购物车 */
.empty-cart {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-cart-content {
    text-align: center;
    color: #6c757d;
}

.empty-cart-content i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-cart-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #2c3e50;
}

.empty-cart-content p {
    margin-bottom: 20px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* 购物车侧边栏 */
.cart-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    position: sticky;
    top: 20px;
}

.cart-summary-card,
.coupon-card,
.recommended-products {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.summary-title,
.coupon-title,
.recommended-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
}

.summary-title i,
.coupon-title i,
.recommended-title i {
    color: #007bff;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    border-top: 2px solid #007bff;
    margin-top: 10px;
    padding-top: 15px;
}

.summary-label {
    color: #6c757d;
}

.summary-value {
    font-weight: 600;
    color: #2c3e50;
}

.free-shipping {
    color: #28a745;
    font-weight: 600;
}

.summary-divider {
    height: 1px;
    background: #dee2e6;
    margin: 15px 0;
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.continue-shopping-btn,
.checkout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.continue-shopping-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.continue-shopping-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.checkout-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.checkout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

/* 优惠券 */
.coupon-input {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.coupon-code {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.9rem;
}

.apply-coupon-btn,
.use-coupon-btn {
    padding: 10px 16px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.apply-coupon-btn:hover,
.use-coupon-btn:hover {
    background: #0056b3;
}

.coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.coupon-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.coupon-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.coupon-desc {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 推荐商品 */
.recommended-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.recommended-item img {
    width: 60px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
}

.recommended-info {
    flex: 1;
}

.recommended-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.recommended-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #e74c3c;
}

.add-recommended-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #28a745;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.add-recommended-btn:hover {
    background: #218838;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .cart-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cart-sidebar {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .cart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .cart-item {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: center;
    }

    .cart-item .item-image {
        justify-self: center;
        width: 100px;
        height: 130px;
    }

    .cart-item .item-price,
    .cart-item .item-total {
        text-align: center;
    }

    .item-specs {
        justify-content: center;
    }

    .item-quantity {
        justify-self: center;
    }
}

@media (max-width: 480px) {
    .cart-page {
        padding: 20px 0;
    }

    .cart-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 8px;
    }

    .cart-summary-card,
    .coupon-card,
    .recommended-products {
        padding: 20px;
    }

    .cart-item {
        padding: 15px;
    }
}
</style>

<div class="cart-page">
    <div class="container">
        <div class="cart-header">
            <h1 class="cart-title">
                <i class="fas fa-shopping-cart"></i>
                我的购物车
            </h1>
            <div class="cart-summary">
                <span class="cart-count">共 <strong><?php echo $total_items; ?></strong> 件商品</span>
            </div>
        </div>

        <div class="cart-content">
            <!-- 购物车商品列表 -->
            <div class="cart-items" id="cartItemsList">
                <!-- 商品将通过JavaScript动态加载 -->
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>正在加载购物车...</p>
                </div>
            </div>

            <!-- 购物车侧边栏 -->
            <div class="cart-sidebar">
                <div class="cart-summary-card">
                    <h3 class="summary-title">订单摘要</h3>
                    
                    <div class="summary-row">
                        <span class="summary-label">商品小计</span>
                        <span class="summary-value">¥<?php echo number_format($subtotal, 2); ?></span>
                    </div>

                    <div class="summary-row">
                        <span class="summary-label">运费</span>
                        <span class="summary-value">
                            <?php if ($shipping_fee > 0): ?>
                                ¥<?php echo number_format($shipping_fee, 2); ?>
                            <?php else: ?>
                                <span class="free-shipping">免运费</span>
                            <?php endif; ?>
                        </span>
                    </div>

                    <?php if ($subtotal >= 299): ?>
                    <div class="summary-row discount">
                        <span class="summary-label">满减优惠</span>
                        <span class="summary-value">免运费</span>
                    </div>
                    <?php endif; ?>

                    <div class="summary-divider"></div>

                    <div class="summary-row total">
                        <span class="summary-label">总计</span>
                        <span class="summary-value">¥<?php echo number_format($total, 2); ?></span>
                    </div>
                    
                    <div class="cart-actions">
                        <button class="continue-shopping-btn">
                            <i class="fas fa-arrow-left"></i>
                            继续购物
                        </button>
                        <button class="checkout-btn">
                            <i class="fas fa-credit-card"></i>
                            立即结算
                        </button>
                    </div>
                </div>

                <!-- 优惠券 -->
                <div class="coupon-card">
                    <h3 class="coupon-title">
                        <i class="fas fa-ticket-alt"></i>
                        优惠券
                    </h3>
                    <div class="coupon-input">
                        <input type="text" placeholder="请输入优惠券代码" class="coupon-code">
                        <button class="apply-coupon-btn">使用</button>
                    </div>
                    <div class="available-coupons">
                        <div class="coupon-item">
                            <div class="coupon-info">
                                <span class="coupon-name">新用户专享</span>
                                <span class="coupon-desc">满500减50</span>
                            </div>
                            <button class="use-coupon-btn">使用</button>
                        </div>
                    </div>
                </div>

                <!-- 推荐商品 -->
                <div class="recommended-products">
                    <h3 class="recommended-title">
                        <i class="fas fa-heart"></i>
                        为您推荐
                    </h3>
                    <div class="recommended-list">
                        <div class="recommended-item">
                            <img src="images/product-3.jpg" alt="推荐商品">
                            <div class="recommended-info">
                                <h4 class="recommended-name">鬼灭之刃 - 炭治郎</h4>
                                <span class="recommended-price">¥599</span>
                            </div>
                            <button class="add-recommended-btn" data-product-id="203">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除选项弹窗 -->
<div class="delete-options-modal" id="deleteOptionsModal">
    <div class="delete-options-content">
        <h3 class="delete-options-title">选择删除方式</h3>
        <p class="delete-options-subtitle">您希望如何处理这个商品？</p>

        <div class="delete-options-buttons">
            <button class="delete-option-btn soft-delete" data-action="soft_remove_item">
                <i class="fas fa-clock"></i>
                <div class="delete-option-info">
                    <div class="delete-option-title">暂时移除</div>
                    <div class="delete-option-desc">商品将被移除，但可在5分钟内撤销恢复</div>
                </div>
            </button>

            <button class="delete-option-btn move-wishlist" data-action="move_to_wishlist">
                <i class="fas fa-heart"></i>
                <div class="delete-option-info">
                    <div class="delete-option-title">移至心愿单</div>
                    <div class="delete-option-desc">将商品保存到心愿单，稍后可以重新添加</div>
                </div>
            </button>

            <button class="delete-option-btn permanent-delete" data-action="remove_item">
                <i class="fas fa-trash"></i>
                <div class="delete-option-info">
                    <div class="delete-option-title">永久删除</div>
                    <div class="delete-option-desc">彻底删除商品，无法恢复</div>
                </div>
            </button>
        </div>

        <button class="delete-options-cancel">取消</button>
    </div>
</div>

<!-- 撤销提示条 -->
<div class="undo-bar" id="undoBar">
    <div class="undo-bar-text">商品已移除</div>
    <button class="undo-btn" id="undoBtn">撤销</button>
    <button class="undo-close" id="undoClose">×</button>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializePage();

    // 初始化页面函数
    async function initializePage() {
        // 清理过期的已删除商品
        cartStorage.cleanupExpiredItems();

        // 同步数据到服务器
        await cartStorage.syncToServer();

        // 渲染购物车
        renderCartItems();

        // 更新页面统计信息
        updatePageStats();
    }

    // 渲染购物车商品
    function renderCartItems() {
        const cartItemsList = document.getElementById('cartItemsList');
        const cartData = cartStorage.getCartData();

        if (cartData.length === 0) {
            cartItemsList.innerHTML = `
                <div class="empty-cart">
                    <div class="empty-cart-content">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>购物车是空的</h3>
                        <p>去看看有什么好东西吧</p>
                        <a href="category.php" class="btn btn-primary">开始购物</a>
                    </div>
                </div>
            `;
        } else {
            cartItemsList.innerHTML = cartData.map(item => `
                <div class="cart-item" data-item-id="${item.id}">
                    <div class="item-image">
                        <img src="${item.image}" alt="${escapeHtml(item.name)}">
                    </div>
                    <div class="item-info">
                        <h3 class="item-name">${escapeHtml(item.name)}</h3>
                        <p class="item-origin">${escapeHtml(item.sku)}</p>
                        <div class="item-specs">
                            <span class="spec">尺码: ${escapeHtml(item.size)}</span>
                            <span class="spec">颜色: ${escapeHtml(item.color)}</span>
                        </div>
                    </div>
                    <div class="item-price">
                        ${item.sale_price ? `
                            <span class="current-price">¥${item.sale_price.toFixed(2)}</span>
                            <span class="original-price">¥${item.price.toFixed(2)}</span>
                        ` : `
                            <span class="current-price">¥${item.price.toFixed(2)}</span>
                        `}
                    </div>
                    <div class="item-quantity">
                        <button class="qty-btn minus" data-item-id="${item.id}">-</button>
                        <input type="number" class="qty-input" value="${item.quantity}" min="1" data-item-id="${item.id}">
                        <button class="qty-btn plus" data-item-id="${item.id}">+</button>
                    </div>
                    <div class="item-total">
                        <span class="total-price">¥${((item.sale_price || item.price) * item.quantity).toFixed(2)}</span>
                    </div>
                    <div class="item-actions">
                        <button class="remove-btn" data-item-id="${item.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 重新绑定事件监听器
        bindEventListeners();
    }

    // 更新页面统计信息
    function updatePageStats() {
        const totalItems = cartStorage.getTotalItems();
        const subtotal = cartStorage.getSubtotal();
        const shipping = subtotal >= 299 ? 0 : 15;
        const total = subtotal + shipping;

        // 更新商品数量
        const countElement = document.querySelector('.cart-count strong');
        if (countElement) {
            countElement.textContent = totalItems;
        }

        // 更新小计
        const subtotalElement = document.querySelector('.summary-row .summary-value');
        if (subtotalElement) {
            subtotalElement.textContent = '¥' + subtotal.toFixed(2);
        }

        // 更新运费
        const shippingElement = document.querySelector('.summary-row:nth-child(2) .summary-value');
        if (shippingElement) {
            if (shipping > 0) {
                shippingElement.innerHTML = '¥' + shipping.toFixed(2);
            } else {
                shippingElement.innerHTML = '<span class="free-shipping">免运费</span>';
            }
        }

        // 更新总计
        const totalElement = document.querySelector('.summary-row.total .summary-value');
        if (totalElement) {
            totalElement.textContent = '¥' + total.toFixed(2);
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 重新绑定数量调整按钮
        document.querySelectorAll('.qty-btn').forEach(btn => {
            btn.addEventListener('click', handleQuantityChange);
        });

        // 重新绑定输入框
        document.querySelectorAll('.qty-input').forEach(input => {
            input.addEventListener('change', handleQuantityInputChange);
        });

        // 重新绑定删除按钮
        document.querySelectorAll('.remove-btn').forEach(btn => {
            btn.addEventListener('click', handleDeleteClick);
        });

        // 初始化减号按钮状态
        document.querySelectorAll('.qty-input').forEach(input => {
            const itemId = input.dataset.itemId;
            const minusBtn = document.querySelector(`.qty-btn.minus[data-item-id="${itemId}"]`);
            const currentValue = parseInt(input.value);
            if (minusBtn) {
                minusBtn.disabled = currentValue <= 1;
            }
        });
    }
    // 显示消息提示
    function showMessage(message, type = 'success') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // AJAX请求函数
    function sendCartRequest(action, data) {
        return fetch('cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: action,
                ...data
            })
        })
        .then(response => response.json())
        .catch(error => {
            console.error('Error:', error);
            showMessage('操作失败，请重试', 'error');
        });
    }

    // 更新页面显示
    function updatePageDisplay(data) {
        if (data.subtotal !== undefined) {
            // 更新小计
            const subtotalElement = document.querySelector('.summary-row .summary-value');
            if (subtotalElement) {
                subtotalElement.textContent = '¥' + parseFloat(data.subtotal).toFixed(2);
            }

            // 更新总计
            const shipping = parseFloat(data.subtotal) >= 299 ? 0 : 15;
            const total = parseFloat(data.subtotal) + shipping;
            const totalElement = document.querySelector('.summary-row.total .summary-value');
            if (totalElement) {
                totalElement.textContent = '¥' + total.toFixed(2);
            }
        }

        if (data.total_items !== undefined) {
            // 更新商品数量
            const countElement = document.querySelector('.cart-count strong');
            if (countElement) {
                countElement.textContent = data.total_items;
            }
        }
    }

    // 数量调整事件处理
    function handleQuantityChange() {
        const itemId = this.dataset.itemId;
        const input = document.querySelector(`input[data-item-id="${itemId}"]`);
        const isPlus = this.classList.contains('plus');
        const minusBtn = document.querySelector(`.qty-btn.minus[data-item-id="${itemId}"]`);

        let currentValue = parseInt(input.value);
        let newValue = currentValue;

        if (isPlus) {
            newValue = currentValue + 1;
        } else if (currentValue > 1) {
            newValue = currentValue - 1;
        }

        if (newValue !== currentValue) {
            // 更新localStorage
            cartStorage.updateQuantity(itemId, newValue);

            // 更新界面
            input.value = newValue;
            minusBtn.disabled = newValue <= 1;

            // 更新商品小计
            const cartItem = this.closest('.cart-item');
            const priceElement = cartItem.querySelector('.current-price');
            const totalElement = cartItem.querySelector('.total-price');

            if (priceElement && totalElement) {
                const price = parseFloat(priceElement.textContent.replace('¥', ''));
                const newTotal = price * newValue;
                totalElement.textContent = '¥' + newTotal.toFixed(2);
            }

            // 更新页面统计
            updatePageStats();

            // 同步到服务器
            cartStorage.syncToServer();

            showMessage('数量已更新');
        }
    }

    // 输入框直接修改数量事件处理
    function handleQuantityInputChange() {
        const itemId = this.dataset.itemId;
        const newValue = Math.max(1, parseInt(this.value) || 1);
        const minusBtn = document.querySelector(`.qty-btn.minus[data-item-id="${itemId}"]`);

        // 更新localStorage
        cartStorage.updateQuantity(itemId, newValue);

        // 更新界面
        this.value = newValue;
        minusBtn.disabled = newValue <= 1;

        // 更新商品小计
        const cartItem = this.closest('.cart-item');
        const priceElement = cartItem.querySelector('.current-price');
        const totalElement = cartItem.querySelector('.total-price');

        if (priceElement && totalElement) {
            const price = parseFloat(priceElement.textContent.replace('¥', ''));
            const newTotal = price * newValue;
            totalElement.textContent = '¥' + newTotal.toFixed(2);
        }

        // 更新页面统计
        updatePageStats();

        // 同步到服务器
        cartStorage.syncToServer();

        showMessage('数量已更新');
    }

    // 删除按钮点击事件处理
    function handleDeleteClick() {
        const itemId = this.dataset.itemId;
        showDeleteOptions(itemId);
    }

    // 删除选项弹窗管理
    const deleteModal = document.getElementById('deleteOptionsModal');
    const undoBar = document.getElementById('undoBar');
    let currentDeleteItemId = null;
    let undoTimeout = null;

    // 显示删除选项弹窗
    function showDeleteOptions(itemId) {
        currentDeleteItemId = itemId;
        deleteModal.classList.add('show');
    }

    // 隐藏删除选项弹窗
    function hideDeleteOptions() {
        deleteModal.classList.remove('show');
        currentDeleteItemId = null;
    }

    // 显示撤销提示条
    function showUndoBar(itemId, itemName) {
        const undoText = undoBar.querySelector('.undo-bar-text');
        undoText.textContent = `"${itemName}" 已移除`;
        undoBar.classList.add('show');

        // 设置撤销按钮的数据
        const undoBtn = document.getElementById('undoBtn');
        undoBtn.dataset.itemId = itemId;

        // 5分钟后自动隐藏
        if (undoTimeout) clearTimeout(undoTimeout);
        undoTimeout = setTimeout(() => {
            hideUndoBar();
        }, 300000); // 5分钟
    }

    // 隐藏撤销提示条
    function hideUndoBar() {
        undoBar.classList.remove('show');
        if (undoTimeout) {
            clearTimeout(undoTimeout);
            undoTimeout = null;
        }
    }

    // 删除商品按钮点击
    document.querySelectorAll('.remove-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            showDeleteOptions(itemId);
        });
    });

    // 删除选项按钮点击
    document.querySelectorAll('.delete-option-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            const itemId = currentDeleteItemId;
            const cartData = cartStorage.getCartData();
            const item = cartData.find(item => item.id === itemId);

            if (!item) return;

            const itemName = item.name;
            hideDeleteOptions();

            let success = false;
            let message = '';

            // 执行相应的操作
            if (action === 'soft_remove_item') {
                success = cartStorage.softRemoveItem(itemId);
                message = '商品已移除，可在5分钟内撤销';

                if (success) {
                    // 重新渲染购物车（商品会被隐藏）
                    renderCartItems();
                    updatePageStats();
                    showUndoBar(itemId, itemName);
                }
            } else if (action === 'move_to_wishlist') {
                success = cartStorage.moveToWishlist(itemId);
                message = '商品已移至心愿单';

                if (success) {
                    renderCartItems();
                    updatePageStats();
                }
            } else if (action === 'remove_item') {
                success = cartStorage.removeItem(itemId);
                message = '商品已永久删除';

                if (success) {
                    renderCartItems();
                    updatePageStats();
                }
            }

            if (success) {
                // 同步到服务器
                cartStorage.syncToServer();
                showMessage(message);
            } else {
                showMessage('操作失败，请重试', 'error');
            }
        });
    });

    // 取消删除
    document.querySelector('.delete-options-cancel').addEventListener('click', hideDeleteOptions);

    // 点击弹窗外部关闭
    deleteModal.addEventListener('click', function(e) {
        if (e.target === deleteModal) {
            hideDeleteOptions();
        }
    });

    // 撤销删除
    document.getElementById('undoBtn').addEventListener('click', function() {
        const itemId = this.dataset.itemId;

        const success = cartStorage.restoreItem(itemId);

        if (success) {
            // 重新渲染购物车
            renderCartItems();
            updatePageStats();

            // 同步到服务器
            cartStorage.syncToServer();

            showMessage('商品已恢复');
            hideUndoBar();
        } else {
            showMessage('恢复失败，商品可能已过期', 'error');
        }
    });

    // 关闭撤销提示条
    document.getElementById('undoClose').addEventListener('click', hideUndoBar);

    // 结算按钮
    const checkoutBtn = document.querySelector('.checkout-btn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function() {
            window.location.href = 'checkout.php';
        });
    }

    // 继续购物按钮
    const continueBtn = document.querySelector('.continue-shopping-btn');
    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            window.location.href = 'category.php';
        });
    }

    // 优惠券使用
    const applyCouponBtn = document.querySelector('.apply-coupon-btn');
    if (applyCouponBtn) {
        applyCouponBtn.addEventListener('click', function() {
            const couponCode = document.querySelector('.coupon-code').value.trim();
            if (couponCode) {
                showMessage('优惠券功能开发中...');
            } else {
                showMessage('请输入优惠券代码', 'error');
            }
        });
    }

    // 推荐商品添加
    document.querySelectorAll('.add-recommended-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.dataset.productId;
            showMessage('推荐商品添加功能开发中...');
        });
    });
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
