<?php
/**
 * COSPlay购物网站 - 商品分类页面
 * 展示商品分类、筛选和搜索功能
 */

// 页面信息设置
$page_title = '商品分类 - COSPlay购物网站';
$page_description = 'COSPlay商品分类页面 - 浏览各种动漫、游戏角色服装，按分类筛选您喜欢的角色扮演服装';

// 获取URL参数
$category_type = $_GET['type'] ?? '';
$search_query = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? '';

// 模拟商品数据
$products = [
    [
        'id' => 201,
        'name' => '原神 - 刻晴星霜华裳',
        'slug' => 'genshin-keqing-costume',
        'price' => 499.00,
        'category' => 'anime',
        'origin' => '原神',
        'rating' => 4.5,
        'reviews' => 42,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => 'new'
    ],
    [
        'id' => 202,
        'name' => '宝可梦 - 皮卡丘',
        'slug' => 'pokemon-pikachu-costume',
        'price' => 299.00,
        'category' => 'anime',
        'origin' => '宝可梦',
        'rating' => 4.0,
        'reviews' => 28,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => 'hot'
    ],
    [
        'id' => 203,
        'name' => '鬼灭之刃 - 炭治郎',
        'slug' => 'kimetsu-tanjiro-costume',
        'price' => 599.00,
        'category' => 'anime',
        'origin' => '鬼灭之刃',
        'rating' => 5.0,
        'reviews' => 56,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => ''
    ],
    [
        'id' => 204,
        'name' => '英雄联盟 - 阿狸',
        'slug' => 'lol-ahri-costume',
        'price' => 649.00,
        'category' => 'game',
        'origin' => '英雄联盟',
        'rating' => 4.8,
        'reviews' => 73,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => ''
    ],
    [
        'id' => 205,
        'name' => '赛博朋克2077',
        'slug' => 'cyberpunk-2077-costume',
        'price' => 699.00,
        'category' => 'game',
        'origin' => '赛博朋克2077',
        'rating' => 4.3,
        'reviews' => 35,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => 'sale'
    ],
    [
        'id' => 206,
        'name' => '火影忍者 - 鸣人',
        'slug' => 'naruto-naruto-costume',
        'price' => 459.00,
        'category' => 'anime',
        'origin' => '火影忍者',
        'rating' => 4.6,
        'reviews' => 89,
        'image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
        'badge' => ''
    ]
];

// 根据筛选条件过滤商品
if ($category_type) {
    $products = array_filter($products, function($product) use ($category_type) {
        return $product['category'] === $category_type;
    });
}

if ($search_query) {
    $products = array_filter($products, function($product) use ($search_query) {
        return stripos($product['name'], $search_query) !== false ||
               stripos($product['origin'], $search_query) !== false;
    });
}

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">
<link rel="stylesheet" href="css/category-fix.css">
<link rel="stylesheet" href="css/category-responsive.css">

<!-- 确保Font Awesome正确加载 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.product-title, .product-name {
    color: #212529 !important;
}

.product-origin, .product-category {
    color: #6c757d !important;
}

.product-price {
    color: #007bff !important;
}

.banner-title {
    color: #212529 !important;
}

.banner-subtitle {
    color: #6c757d !important;
}

.sidebar-title {
    color: #212529 !important;
}

.filter-title {
    color: #212529 !important;
}

.filter-label {
    color: #495057 !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.btn-primary, .add-to-cart-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保商品卡片文字可见 */
.product-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.product-content {
    color: #212529 !important;
}

/* 确保侧边栏文字可见 */
.category-sidebar {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* 确保分类横幅文字可见 */
.category-banner {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* 修复Font Awesome图标显示问题 */
.fas, .far, .fab, .fal, .fad {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

.far {
    font-weight: 400 !important;
}

.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* 确保所有图标都可见 */
i[class*="fa-"] {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* 特别修复quick-categories中的图标 */
.quick-categories i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    color: #ffffff !important;
    position: relative !important;
    z-index: 10 !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 确保图标在背景上方显示 */
.quick-category-icon {
    position: relative !important;
    overflow: visible !important;
}

.quick-category-icon::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: inherit !important;
    border-radius: inherit !important;
    z-index: 1 !important;
}

.quick-category-icon i {
    position: relative !important;
    z-index: 10 !important;
}

/* 强制所有quick-categories图标为白色且无模糊 */
.quick-categories .quick-category-icon i,
.quick-category-icon i,
.quick-categories i[class*="fa-"] {
    color: #ffffff !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    transform: none !important;
    -webkit-transform: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
}

/* 全局文字清晰修复 - 移除所有模糊效果 */
* {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 移除所有可能导致模糊的变换 */
*:not(.quick-category-item):not(.product-card):not(.add-to-cart-btn) {
    transform: none !important;
    -webkit-transform: none !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
    will-change: auto !important;
}
</style>

<!-- 优化页面布局和文字颜色样式 -->
<style>
/* 重置和优化商品卡片样式 */
.product-card {
    background: #ffffff !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

.product-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
    border-color: #007bff !important;
}

.product-image-wrapper {
    position: relative !important;
    overflow: hidden !important;
    height: 280px !important;
    background: #f8f9fa !important;
}

.product-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.4s ease !important;
}

.product-card:hover .product-image {
    transform: scale(1.08) !important;
}

.product-badge {
    position: absolute !important;
    top: 12px !important;
    left: 12px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    z-index: 3 !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
}

.product-badge.new {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.product-badge.hot {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important;
}

.product-badge.sale {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: #212529 !important;
    left: auto !important;
    right: 12px !important;
}

.product-quick-actions {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    opacity: 0 !important;
    transform: translateX(10px) !important;
    transition: all 0.3s ease !important;
}

.product-card:hover .product-quick-actions {
    opacity: 1 !important;
    transform: translateX(0) !important;
}

.quick-action-btn {
    width: 40px !important;
    height: 40px !important;
    border: none !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.95) !important;
    color: #495057 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    backdrop-filter: none !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.quick-action-btn:hover {
    background: #007bff !important;
    color: #ffffff !important;
    transform: scale(1.1) !important;
}

.product-content {
    padding: 20px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
}

.product-meta {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
}

.product-category {
    font-size: 0.9rem !important;
    color: #007bff !important;
    font-weight: 600 !important;
    background: rgba(0, 123, 255, 0.1) !important;
    padding: 4px 10px !important;
    border-radius: 15px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.product-rating {
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.rating-stars {
    display: flex !important;
    gap: 2px !important;
}

.rating-stars i {
    font-size: 0.9rem !important;
    color: #fbbf24 !important;
}

.rating-count {
    font-size: 0.8rem !important;
    color: #6c757d !important;
    font-weight: 500 !important;
}

.product-title {
    margin: 0 !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    color: #2c3e50 !important;
    margin-bottom: 8px !important;
}

.product-title a {
    text-decoration: none !important;
    color: #2c3e50 !important;
    transition: color 0.3s ease !important;
}

.product-title a:hover {
    color: #007bff !important;
    text-decoration: none !important;
}

.product-price-section {
    margin-top: auto !important;
    padding-top: 12px !important;
    border-top: 1px solid #f1f3f4 !important;
}

.product-price {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: #007bff !important;
    text-shadow: 0 1px 2px rgba(0,123,255,0.2) !important;
}

.product-actions {
    margin-top: 15px !important;
}

.add-to-cart-btn {
    width: 100% !important;
    padding: 12px !important;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.add-to-cart-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0,123,255,0.3) !important;
}

/* 分类横幅优化 */
.category-banner {
    position: relative;
    overflow: hidden;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding: 50px 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.category-banner .text-container {
    max-width: 700px;
    position: relative;
    z-index: 2;
}

.category-banner .banner-title {
    font-weight: 800;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
    font-size: 2.8rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    color: #1a1a1a;
    background: linear-gradient(135deg, #1a1a1a 0%, #007bff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.category-banner .banner-subtitle {
    font-weight: 500;
    margin-bottom: 30px;
    font-size: 1.2rem;
    line-height: 1.7;
    max-width: 600px;
    color: #2c3e50;
}

.banner-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 35px;
    flex-wrap: wrap;
}

.banner-stats .stat-item {
    text-align: center;
    padding: 15px 20px;
    background: rgba(255,255,255,0.8);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    backdrop-filter: none;
    transition: transform 0.3s ease;
}

.banner-stats .stat-item:hover {
    transform: translateY(-5px);
}

.banner-stats .stat-number {
    font-weight: 800;
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
    color: #007bff;
    text-shadow: 0 1px 3px rgba(0,123,255,0.3);
}

.banner-stats .stat-label {
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.banner-cta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.banner-btn {
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-primary.banner-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: #ffffff;
    border: none;
}

.btn-secondary.banner-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: #ffffff;
    border: none;
}

.banner-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    color: #ffffff;
    text-decoration: none;
}

/* 快速分类导航优化 */
.quick-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
    padding: 25px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.06);
}

.quick-category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 15px;
    text-decoration: none;
    color: #495057;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: 2px solid transparent;
}

.quick-category-item:hover {
    color: #007bff;
    background: #ffffff;
    border-color: #007bff;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
    text-decoration: none;
}

.quick-category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.quick-category-item:hover .quick-category-icon {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: scale(1.1);
}

.quick-category-icon i {
    font-size: 1.8rem !important;
    color: #ffffff !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    position: relative !important;
    z-index: 10 !important;
    line-height: 1 !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    transform: none !important;
}

.quick-category-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: inherit;
}

.quick-category-count {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* 商品网格布局优化 */
.product-grid-view {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
    gap: 25px !important;
    padding: 20px 0 !important;
}

/* 无商品状态优化 */
.no-products-found {
    grid-column: 1 / -1 !important;
    text-align: center !important;
    padding: 60px 20px !important;
    background: #ffffff !important;
    border-radius: 15px !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.08) !important;
}

.no-products-content {
    max-width: 400px !important;
    margin: 0 auto !important;
}

.no-products-icon {
    font-size: 4rem !important;
    color: #6c757d !important;
    margin-bottom: 20px !important;
}

.no-products-title {
    font-size: 1.5rem !important;
    color: #2c3e50 !important;
    margin-bottom: 15px !important;
    font-weight: 600 !important;
}

.no-products-text {
    color: #6c757d !important;
    margin-bottom: 25px !important;
    font-size: 1.1rem !important;
}

/* 分页导航优化 */
.pagination-wrapper {
    margin-top: 40px !important;
    padding: 25px !important;
    background: #ffffff !important;
    border-radius: 15px !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.08) !important;
}

.pagination {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px !important;
    margin-bottom: 15px !important;
}

.pagination-btn, .pagination-number {
    padding: 10px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    color: #495057 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
}

.pagination-btn:hover, .pagination-number:hover {
    border-color: #007bff !important;
    color: #007bff !important;
    text-decoration: none !important;
    transform: translateY(-2px) !important;
}

.pagination-number.active {
    background: #007bff !important;
    border-color: #007bff !important;
    color: #ffffff !important;
}

.pagination-info {
    text-align: center !important;
    color: #6c757d !important;
    font-size: 0.95rem !important;
    font-weight: 500 !important;
}

/* 适配不同屏幕尺寸 */
@media (max-width: 768px) {
    .category-banner {
        padding: 30px 20px;
    }

    .category-banner .banner-title {
        font-size: 2.2rem;
    }

    .category-banner .banner-subtitle {
        font-size: 1.1rem;
    }

    .banner-stats {
        gap: 20px;
        justify-content: center;
    }

    .banner-stats .stat-number {
        font-size: 1.6rem;
    }

    .quick-categories {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        padding: 20px;
    }

    .quick-category-item {
        padding: 20px 10px;
    }

    .quick-category-icon {
        width: 50px;
        height: 50px;
    }

    .quick-category-icon i {
        font-size: 1.5rem;
    }

    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)) !important;
        gap: 20px !important;
    }

    .product-image-wrapper {
        height: 240px !important;
    }

    .product-content {
        padding: 15px !important;
    }

    .product-title {
        font-size: 1rem !important;
    }

    .product-price {
        font-size: 1.2rem !important;
    }
}

@media (max-width: 480px) {
    .category-banner .banner-title {
        font-size: 1.8rem;
    }

    .banner-cta {
        flex-direction: column;
        align-items: center;
    }

    .banner-btn {
        width: 100%;
        max-width: 250px;
    }

    .quick-categories {
        grid-template-columns: repeat(2, 1fr);
    }

    .product-grid-view {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }

    .product-image-wrapper {
        height: 200px !important;
    }

    .product-content {
        padding: 12px !important;
    }

    .product-title {
        font-size: 0.95rem !important;
    }

    .product-price {
        font-size: 1.1rem !important;
    }

    .add-to-cart-btn {
        padding: 10px !important;
        font-size: 0.85rem !important;
    }
}
</style>

<div class="category-page">
    <div class="container">
        <!-- 分类横幅 -->
        <div class="category-banner">
            <div class="banner-content">
                <div class="text-container">
                    <h1 class="banner-title">探索二次元角色服装</h1>
                    <p class="banner-subtitle">高品质角色扮演服装，让您的COSPLAY更加完美</p>
                </div>
                <div class="banner-stats">
                    <div class="stat-item">
                        <span class="stat-number">300+</span>
                        <span class="stat-label">精选服装</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100+</span>
                        <span class="stat-label">热门IP</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15K+</span>
                        <span class="stat-label">满意用户</span>
                    </div>
                </div>
                <div class="banner-cta">
                    <a href="#product-list" class="btn-primary banner-btn">立即选购</a>
                    <a href="custom.php" class="btn-secondary banner-btn">定制服务</a>
                </div>
            </div>
            <div class="banner-decoration">
                <div class="floating-icon primary">
                    <i class="fas fa-mask"></i>
                </div>
                <div class="floating-icon secondary">
                    <i class="fas fa-star"></i>
                </div>
                <div class="floating-icon accent">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="floating-icon highlight">
                    <i class="fas fa-magic"></i>
                </div>
            </div>
        </div>

        <!-- 快速分类导航 -->
        <div class="quick-categories">
            <a href="category.php?type=anime&source=quick" class="quick-category-item" data-category="anime">
                <div class="quick-category-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <span class="quick-category-name">动漫角色</span>
                <span class="quick-category-count">120+</span>
            </a>
            <a href="category.php?type=game&source=quick" class="quick-category-item" data-category="game">
                <div class="quick-category-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <span class="quick-category-name">游戏角色</span>
                <span class="quick-category-count">85+</span>
            </a>
            <a href="category.php?type=movie&source=quick" class="quick-category-item" data-category="movie">
                <div class="quick-category-icon">
                    <i class="fas fa-film"></i>
                </div>
                <span class="quick-category-name">电影角色</span>
                <span class="quick-category-count">42+</span>
            </a>
            <a href="category.php?type=original&source=quick" class="quick-category-item" data-category="original">
                <div class="quick-category-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <span class="quick-category-name">原创设计</span>
                <span class="quick-category-count">28+</span>
            </a>
            <a href="category.php?type=accessories&source=quick" class="quick-category-item" data-category="accessories">
                <div class="quick-category-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <span class="quick-category-name">配饰道具</span>
                <span class="quick-category-count">112+</span>
            </a>
            <a href="category.php?type=custom&source=quick" class="quick-category-item" data-category="custom">
                <div class="quick-category-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <span class="quick-category-name">定制服务</span>
                <span class="quick-category-count">∞</span>
            </a>
            <a href="category.php?type=special&source=quick" class="quick-category-item" data-category="special">
                <div class="quick-category-icon">
                    <i class="fas fa-star"></i>
                </div>
                <span class="quick-category-name">特别推荐</span>
                <span class="quick-category-count">66+</span>
            </a>
        </div>

        <!-- 分类内容 -->
        <div class="category-content">
            <!-- 侧边栏筛选 -->
            <aside class="category-sidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">
                        <i class="fas fa-filter"></i>
                        筛选条件
                    </h3>
                    <button class="filter-toggle" aria-label="切换筛选面板">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    <div class="filter-section">
                        <h4 class="filter-title">
                            <i class="fas fa-tags"></i>
                            作品分类
                        </h4>
                        <ul class="filter-list">
                            <li class="filter-item">
                                <input type="checkbox" id="anime" class="filter-checkbox">
                                <label for="anime" class="filter-label">
                                    <span class="filter-name">动漫</span>
                                    <span class="filter-count">120</span>
                                </label>
                            </li>
                            <li class="filter-item">
                                <input type="checkbox" id="game" class="filter-checkbox">
                                <label for="game" class="filter-label">
                                    <span class="filter-name">游戏</span>
                                    <span class="filter-count">85</span>
                                </label>
                            </li>
                            <li class="filter-item">
                                <input type="checkbox" id="movie" class="filter-checkbox">
                                <label for="movie" class="filter-label">
                                    <span class="filter-name">电影</span>
                                    <span class="filter-count">42</span>
                                </label>
                            </li>
                            <li class="filter-item">
                                <input type="checkbox" id="original" class="filter-checkbox">
                                <label for="original" class="filter-label">
                                    <span class="filter-name">原创</span>
                                    <span class="filter-count">28</span>
                                </label>
                            </li>
                        </ul>
                    </div>

                    <div class="filter-section">
                        <h3 class="filter-title">价格区间</h3>
                        <div class="price-slider">
                            <div class="price-range">
                                <div class="price-progress"></div>
                            </div>
                            <div class="price-values">
                                <span class="price-min">¥0</span>
                                <span class="price-max">¥2000</span>
                            </div>
                        </div>
                    </div>

                    <button class="filter-reset-btn">重置筛选</button>
                </div>
            </aside>

            <!-- 商品列表 -->
            <main class="category-products" style="margin-left: 0px;">
                <!-- 商品控制栏 -->
                <div class="products-toolbar">
                    <div class="toolbar-left">
                        <h2 class="products-title">全部商品 <span class="products-count">(275)</span></h2>
                        <div class="products-filter-tags">
                            <!-- 动态显示已选择的筛选标签 -->
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <div class="view-switcher">
                            <button class="view-btn active" data-view="grid" aria-label="网格视图" title="网格视图">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" aria-label="列表视图" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        <div class="sort-dropdown">
                            <label for="sort-select" class="sort-label">排序：</label>
                            <select id="sort-select" class="sort-select">
                                <option value="popular">人气优先</option>
                                <option value="newest">最新上架</option>
                                <option value="price-asc">价格从低到高</option>
                                <option value="price-desc">价格从高到低</option>
                                <option value="rating">评分优先</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 商品网格容器 -->
                <div class="products-container" id="product-list">
                    <div class="product-grid-view">
                        <?php if (!empty($products)): ?>
                            <?php foreach ($products as $product): ?>
                                <article class="product-card">
                                    <?php if ($product['badge']): ?>
                                        <div class="product-badge <?php echo $product['badge']; ?>">
                                            <?php
                                            $badge_text = [
                                                'new' => '新品',
                                                'hot' => '热卖',
                                                'sale' => '特价'
                                            ];
                                            echo $badge_text[$product['badge']] ?? '';
                                            ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="product-image-wrapper">
                                        <a href="detail.php?id=<?php echo $product['id']; ?>&name=<?php echo urlencode($product['slug']); ?>&source=category" class="product-image-link">
                                            <img src="<?php echo $product['image']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 class="product-image"
                                                 loading="lazy">
                                        </a>
                                        <div class="product-quick-actions">
                                            <button class="quick-action-btn wishlist-btn" data-product-id="<?php echo $product['id']; ?>" aria-label="添加到心愿单" title="添加到心愿单">
                                                <i class="far fa-heart"></i>
                                            </button>
                                            <button class="quick-action-btn preview-btn" data-product-id="<?php echo $product['id']; ?>" aria-label="快速预览" title="快速预览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="product-content">
                                        <div class="product-meta">
                                            <span class="product-category"><?php echo htmlspecialchars($product['origin']); ?></span>
                                            <div class="product-rating">
                                                <div class="rating-stars">
                                                    <?php
                                                    $rating = $product['rating'];
                                                    $full_stars = floor($rating);
                                                    $half_star = ($rating - $full_stars) >= 0.5;
                                                    $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

                                                    // 显示满星
                                                    for ($i = 0; $i < $full_stars; $i++) {
                                                        echo '<i class="fas fa-star"></i>';
                                                    }

                                                    // 显示半星
                                                    if ($half_star) {
                                                        echo '<i class="fas fa-star-half-alt"></i>';
                                                    }

                                                    // 显示空星
                                                    for ($i = 0; $i < $empty_stars; $i++) {
                                                        echo '<i class="far fa-star"></i>';
                                                    }
                                                    ?>
                                                </div>
                                                <span class="rating-count">(<?php echo $product['reviews']; ?>)</span>
                                            </div>
                                        </div>

                                        <h3 class="product-title">
                                            <a href="detail.php?id=<?php echo $product['id']; ?>&name=<?php echo urlencode($product['slug']); ?>&source=category">
                                                <?php echo htmlspecialchars($product['name']); ?>
                                            </a>
                                        </h3>

                                        <div class="product-price-section">
                                            <span class="product-price">¥<?php echo number_format($product['price'], 2); ?></span>
                                        </div>

                                        <div class="product-actions">
                                            <button class="add-to-cart-btn" data-product-id="<?php echo $product['id']; ?>">
                                                <i class="fas fa-shopping-cart"></i>
                                                <span>加入购物车</span>
                                            </button>
                                        </div>
                                    </div>
                                </article>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-products-found">
                                <div class="no-products-content">
                                    <div class="no-products-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <h3 class="no-products-title">没有找到相关商品</h3>
                                    <p class="no-products-text">试试调整筛选条件或搜索其他关键词</p>
                                    <a href="category.php" class="btn btn-primary">查看全部商品</a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 分页导航 -->
                <div class="pagination-wrapper">
                    <nav class="pagination" aria-label="商品分页导航">
                        <a href="#" class="pagination-btn pagination-prev" aria-label="上一页">
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </a>
                        <div class="pagination-numbers">
                            <a href="#" class="pagination-number active">1</a>
                            <a href="#" class="pagination-number">2</a>
                            <a href="#" class="pagination-number">3</a>
                            <span class="pagination-dots">...</span>
                            <a href="#" class="pagination-number">12</a>
                        </div>
                        <a href="#" class="pagination-btn pagination-next" aria-label="下一页">
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                    <div class="pagination-info">
                        <span>显示 1-12 / 共 275 件商品</span>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script src="js/cart-storage.js"></script>
<script src="js/cart-utils.js"></script>
<script src="js/main.js"></script>
<script>
// 获取URL参数
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        type: params.get('type'),
        search: params.get('search'),
        filter: params.get('filter'),
        source: params.get('source')
    };
}

// 根据参数更新页面内容
function updatePageByParams() {
    const params = getUrlParams();
    
    if (params.type) {
        // 更新分类标题
        const categoryNames = {
            'anime': '动漫角色',
            'game': '游戏角色',
            'movie': '电影角色',
            'original': '原创设计',
            'accessories': '配饰道具',
            'custom': '定制服务',
            'special': '特别推荐'
        };

        if (categoryNames[params.type]) {
            document.querySelector('.category-title').innerHTML =
                `${categoryNames[params.type]} <span>(${Math.floor(Math.random() * 100) + 50})</span>`;
        }
    }

    if (params.search) {
        // 显示搜索结果
        document.querySelector('.category-title').innerHTML =
            `搜索结果："${params.search}" <span>(${Math.floor(Math.random() * 50) + 10})</span>`;
    }

    if (params.filter) {
        // 应用筛选条件
        console.log('应用筛选条件:', params.filter);
    }
}

// 分类筛选功能
function filterByCategory(category) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('type', category);
    currentUrl.searchParams.set('source', 'filter');
    window.location.href = currentUrl.toString();
}

// 修复Font Awesome图标显示问题
function fixFontAwesomeIcons() {
    console.log('开始修复Font Awesome图标...');

    // 强制设置所有图标的字体
    document.querySelectorAll('.quick-categories i, i[class*="fa-"], .fas, .far, .fab').forEach(icon => {
        icon.style.fontFamily = '"Font Awesome 6 Free", "Font Awesome 5 Free"';
        icon.style.fontWeight = '900';
        icon.style.display = 'inline-block';
        icon.style.textRendering = 'auto';
        icon.style.webkitFontSmoothing = 'antialiased';
        icon.style.mozOsxFontSmoothing = 'grayscale';

        // 确保图标可见
        if (icon.style.color === '' || icon.style.color === 'transparent') {
            icon.style.color = '#ffffff';
        }
    });

    // 特别处理quick-categories中的图标
    document.querySelectorAll('.quick-category-icon i').forEach(icon => {
        icon.style.fontFamily = '"Font Awesome 6 Free"';
        icon.style.fontWeight = '900';
        icon.style.color = '#ffffff';
        icon.style.fontSize = '1.8rem';
        icon.style.display = 'inline-block';
        icon.style.position = 'relative';
        icon.style.zIndex = '10';
        icon.style.textShadow = 'none';       // 移除阴影
        icon.style.filter = 'none';           // 移除滤镜
        icon.style.webkitFilter = 'none';     // 移除webkit滤镜
        icon.style.transform = 'none';        // 移除变换
        icon.style.webkitFontSmoothing = 'auto';  // 清晰字体
        icon.style.mozOsxFontSmoothing = 'auto';  // 清晰字体
    });

    console.log('Font Awesome图标修复完成');
}

// 视图切换功能
function toggleView(viewType) {
    const gridView = document.querySelector('.product-grid-view');
    const viewBtns = document.querySelectorAll('.view-btn');

    // 更新按钮状态
    viewBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.view === viewType) {
            btn.classList.add('active');
        }
    });

    // 切换视图样式
    if (viewType === 'list') {
        gridView.classList.add('list-view');
    } else {
        gridView.classList.remove('list-view');
    }

    // 保存用户偏好
    localStorage.setItem('categoryView', viewType);
}

// 筛选面板切换
function toggleFilterPanel() {
    const sidebar = document.querySelector('.category-sidebar');
    const toggleBtn = document.querySelector('.filter-toggle');
    const toggleIcon = toggleBtn.querySelector('i');

    sidebar.classList.toggle('collapsed');
    
    if(sidebar.classList.contains('collapsed')) {
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    } else {
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    }
}

// 价格筛选功能
function initPriceSlider() {
    const priceRange = document.querySelector('.price-range');
    const priceProgress = document.querySelector('.price-progress');
    const priceMin = document.querySelector('.price-min');
    const priceMax = document.querySelector('.price-max');

    if (!priceRange) return;

    let isDragging = false;
    let currentHandle = null;

    priceRange.addEventListener('mousedown', function(e) {
        isDragging = true;
        const rect = priceRange.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;

        // 简单的价格计算
        const price = Math.round(percent * 2000);
        priceMax.textContent = `¥${price}`;
        priceProgress.style.width = `${percent * 100}%`;
    });

    document.addEventListener('mouseup', function() {
        isDragging = false;
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
    updatePageByParams();

    // 恢复用户视图偏好
    const savedView = localStorage.getItem('categoryView') || 'grid';
    toggleView(savedView);

    // 为快速分类添加点击事件
    document.querySelectorAll('.quick-category-item').forEach(item => {
        item.addEventListener('click', function (e) {
            if (this.tagName !== 'A') {
                e.preventDefault();
                const category = this.dataset.category;
                filterByCategory(category);
            }
        });
    });

    // 视图切换事件
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const viewType = this.dataset.view;
            toggleView(viewType);
        });
    });

    // 筛选面板切换事件
    const filterToggle = document.querySelector('.filter-toggle');
    if (filterToggle) {
        filterToggle.addEventListener('click', toggleFilterPanel);
    }

    // 初始化价格滑块
    initPriceSlider();

    // 修复Font Awesome图标
    fixFontAwesomeIcons();

    // 延迟再次修复图标，确保完全加载
    setTimeout(fixFontAwesomeIcons, 500);

    // 筛选复选框事件
    document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 这里可以添加实时筛选逻辑
            console.log('筛选条件变更:', this.id, this.checked);
        });
    });

    // 重置筛选按钮
    const resetBtn = document.querySelector('.filter-reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            // 重置所有筛选条件
            document.querySelectorAll('.filter-checkbox').forEach(cb => cb.checked = false);
            document.querySelector('.price-min').textContent = '¥0';
            document.querySelector('.price-max').textContent = '¥2000';
            document.querySelector('.price-progress').style.width = '100%';
        });
    }
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
