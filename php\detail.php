<?php
/**
 * COSPlay购物网站 - 商品详情页面
 * 显示商品详细信息、图片、规格和购买选项
 */

// 获取商品ID
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$product_name = isset($_GET['name']) ? htmlspecialchars($_GET['name'] ?? '') : '';

// 模拟商品数据库
$products_db = [
    1001 => [
        'id' => 1001,
        'name' => '原神 - 刻晴星霜华裳',
        'slug' => 'genshin-keqing-costume',
        'price' => 599.00,
        'sale_price' => 499.00,
        'description' => '精心制作的原神刻晴角色服装，采用高品质面料，细节还原度极高。适合角色扮演、拍照、展会等场合。',
        'category' => '原神',
        'brand' => 'COSPlay Official',
        'stock' => 15,
        'rating' => 4.8,
        'reviews_count' => 127,
        'sku' => 'GI-KQ-001',
        'images' => [
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=750&fit=crop',
            'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=750&fit=crop',
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=750&fit=crop',
            'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=750&fit=crop'
        ]
    ],
    1002 => [
        'id' => 1002,
        'name' => '英雄联盟 - 阿狸',
        'slug' => 'lol-ahri-costume',
        'price' => 649.00,
        'sale_price' => null,
        'description' => '英雄联盟阿狸角色服装，精美制作，完美还原游戏中的经典形象。',
        'category' => '英雄联盟',
        'brand' => 'COSPlay Official',
        'stock' => 8,
        'rating' => 4.6,
        'reviews_count' => 89,
        'sku' => 'LOL-AH-001',
        'images' => [
            'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=750&fit=crop',
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=750&fit=crop'
        ]
    ]
];

// 获取商品信息
$product = $products_db[$product_id] ?? $products_db[1001]; // 默认显示第一个商品

// 页面信息设置
$page_title = $product['name'] . ' - COSPlay购物网站';
$page_description = $product['description'];

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">
<link rel="stylesheet" href="css/detail-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.product-title {
    color: #212529 !important;
}

.product-origin, .product-sku {
    color: #6c757d !important;
}

.current-price {
    color: #007bff !important;
}

.original-price {
    color: #6c757d !important;
}

.product-description {
    color: #495057 !important;
}

.option-label {
    color: #212529 !important;
}

.quantity-label {
    color: #212529 !important;
}

.stock-info {
    color: #6c757d !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.btn-primary, .add-to-cart-btn, .buy-now-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.size-option {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

.size-option.active {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保标签页文字可见 */
.tab-btn {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

.tab-btn.active {
    color: #007bff !important;
    background-color: #ffffff !important;
}

/* 确保表格文字可见 */
.specs-table td {
    color: #495057 !important;
}

/* 确保评价文字可见 */
.rating-text, .review-count {
    color: #6c757d !important;
}

/* 确保面包屑导航可见 */
.breadcrumbs {
    color: #6c757d !important;
}

.breadcrumbs a {
    color: #007bff !important;
}
</style>

<!-- 面包屑导航 -->
<div class="breadcrumbs">
    <div class="container">
        <nav>
            <a href="index.php">首页</a>
            <span>/</span>
            <a href="category.php">商品分类</a>
            <span>/</span>
            <span><?php echo htmlspecialchars($product['category']); ?></span>
            <span>/</span>
            <span><?php echo htmlspecialchars($product['name']); ?></span>
        </nav>
    </div>
</div>

<style>
/* 面包屑导航 */
.breadcrumbs {
    background: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumbs .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.breadcrumbs nav {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.breadcrumbs a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumbs a:hover {
    text-decoration: underline;
}

.breadcrumbs span {
    color: #6c757d;
}
</style>

<div class="product-detail-page">
    <div class="container">
        <div class="product-detail">
            <!-- 商品图片 -->
            <div class="product-images">
                <div class="main-image">
                    <img src="<?php echo $product['images'][0]; ?>"
                         alt="<?php echo htmlspecialchars($product['name']); ?>" id="mainImage">
                    <div class="image-zoom">
                        <i class="fas fa-search-plus"></i>
                    </div>
                </div>
                <div class="thumbnail-images">
                    <?php foreach ($product['images'] as $index => $image): ?>
                        <img src="<?php echo str_replace('w=600&h=750', 'w=100&h=125', $image); ?>"
                             alt="图片<?php echo $index + 1; ?>"
                             class="thumbnail <?php echo $index === 0 ? 'active' : ''; ?>"
                             data-full-image="<?php echo $image; ?>">
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="product-info">
                <div class="product-header">
                    <h1 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h1>
                    <div class="product-badges">
                        <?php if ($product['sale_price']): ?>
                            <span class="badge sale">特价</span>
                        <?php endif; ?>
                        <?php if ($product['stock'] > 0): ?>
                            <span class="badge hot">现货</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="product-meta">
                    <span class="product-origin"><?php echo htmlspecialchars($product['category']); ?></span>
                    <span class="product-sku">SKU: <?php echo htmlspecialchars($product['sku']); ?></span>
                </div>

                <div class="product-rating">
                    <div class="stars">
                        <?php
                        $rating = $product['rating'];
                        $full_stars = floor($rating);
                        $half_star = ($rating - $full_stars) >= 0.5;
                        $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

                        // 显示满星
                        for ($i = 0; $i < $full_stars; $i++) {
                            echo '<i class="fas fa-star"></i>';
                        }

                        // 显示半星
                        if ($half_star) {
                            echo '<i class="fas fa-star-half-alt"></i>';
                        }

                        // 显示空星
                        for ($i = 0; $i < $empty_stars; $i++) {
                            echo '<i class="far fa-star"></i>';
                        }
                        ?>
                    </div>
                    <span class="rating-text"><?php echo number_format($product['rating'], 1); ?>分</span>
                    <span class="review-count">(<?php echo $product['reviews_count']; ?>条评价)</span>
                </div>

                <div class="product-price">
                    <?php if ($product['sale_price']): ?>
                        <span class="current-price">¥<?php echo number_format($product['sale_price'], 2); ?></span>
                        <span class="original-price">¥<?php echo number_format($product['price'], 2); ?></span>
                        <span class="discount">-<?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>%</span>
                    <?php else: ?>
                        <span class="current-price">¥<?php echo number_format($product['price'], 2); ?></span>
                    <?php endif; ?>
                </div>

                <div class="product-description">
                    <p><?php echo htmlspecialchars($product['description']); ?></p>
                </div>

                <!-- 商品选项 -->
                <div class="product-options">
                    <div class="option-group">
                        <label class="option-label">尺码</label>
                        <div class="size-options">
                            <button class="size-option" data-size="XS">XS</button>
                            <button class="size-option" data-size="S">S</button>
                            <button class="size-option active" data-size="M">M</button>
                            <button class="size-option" data-size="L">L</button>
                            <button class="size-option" data-size="XL">XL</button>
                            <button class="size-option" data-size="XXL">XXL</button>
                        </div>
                        <a href="index.php#size-guide" class="size-guide">尺码指南</a>
                    </div>

                    <div class="option-group">
                        <label class="option-label">颜色</label>
                        <div class="color-options">
                            <button class="color-option active" data-color="purple" style="background-color: #8B5CF6;"></button>
                            <button class="color-option" data-color="blue" style="background-color: #3B82F6;"></button>
                        </div>
                    </div>

                    <div class="option-group">
                        <label class="option-label">套装选择</label>
                        <div class="package-options">
                            <label class="package-option">
                                <input type="radio" name="package" value="basic" checked>
                                <span class="package-info">
                                    <span class="package-name">基础套装</span>
                                    <span class="package-price">¥499</span>
                                </span>
                                <span class="package-desc">包含：上衣、裙子、腰带</span>
                            </label>
                            <label class="package-option">
                                <input type="radio" name="package" value="deluxe">
                                <span class="package-info">
                                    <span class="package-name">豪华套装</span>
                                    <span class="package-price">¥699</span>
                                </span>
                                <span class="package-desc">包含：基础套装 + 假发 + 配饰</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 数量和购买 -->
                <div class="purchase-section">
                    <div class="quantity-selector">
                        <label class="quantity-label">数量</label>
                        <div class="quantity-controls">
                            <button class="qty-btn minus">-</button>
                            <input type="number" class="qty-input" value="1" min="1" max="10">
                            <button class="qty-btn plus">+</button>
                        </div>
                        <span class="stock-info">库存：<?php echo $product['stock']; ?>件</span>
                    </div>

                    <div class="purchase-buttons">
                        <button class="add-to-cart-btn">
                            <i class="fas fa-shopping-cart"></i>
                            加入购物车
                        </button>
                        <button class="buy-now-btn">
                            <i class="fas fa-bolt"></i>
                            立即购买
                        </button>
                        <button class="wishlist-btn">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                </div>

                <!-- 服务保障 -->
                <div class="service-guarantees">
                    <div class="guarantee-item">
                        <i class="fas fa-shipping-fast"></i>
                        <span>48小时发货</span>
                    </div>
                    <div class="guarantee-item">
                        <i class="fas fa-undo"></i>
                        <span>7天无理由退换</span>
                    </div>
                    <div class="guarantee-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>正品保证</span>
                    </div>
                    <div class="guarantee-item">
                        <i class="fas fa-headset"></i>
                        <span>24小时客服</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品详情标签页 -->
        <div class="product-tabs">
            <div class="tab-nav">
                <button class="tab-btn active" data-tab="details">商品详情</button>
                <button class="tab-btn" data-tab="specs">规格参数</button>
                <button class="tab-btn" data-tab="reviews">用户评价</button>
                <button class="tab-btn" data-tab="qa">常见问题</button>
            </div>

            <div class="tab-content">
                <div class="tab-pane active" id="details">
                    <div class="product-details">
                        <h3>商品详情</h3>
                        <p>这是一套精心制作的原神刻晴角色服装，采用高品质面料制作，细节还原度极高。</p>
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop" alt="详情图片">
                    </div>
                </div>

                <div class="tab-pane" id="specs">
                    <div class="product-specs">
                        <h3>规格参数</h3>
                        <table class="specs-table">
                            <tr>
                                <td>材质</td>
                                <td>聚酯纤维、棉质混纺</td>
                            </tr>
                            <tr>
                                <td>适用身高</td>
                                <td>150-175cm</td>
                            </tr>
                            <tr>
                                <td>洗涤方式</td>
                                <td>手洗，阴干</td>
                            </tr>
                            <tr>
                                <td>产地</td>
                                <td>中国</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="tab-pane" id="reviews">
                    <div class="product-reviews">
                        <h3>用户评价 (128)</h3>
                        <div class="review-summary">
                            <div class="rating-breakdown">
                                <div class="rating-bar">
                                    <span>5星</span>
                                    <div class="bar"><div class="fill" style="width: 70%"></div></div>
                                    <span>70%</span>
                                </div>
                                <div class="rating-bar">
                                    <span>4星</span>
                                    <div class="bar"><div class="fill" style="width: 20%"></div></div>
                                    <span>20%</span>
                                </div>
                                <div class="rating-bar">
                                    <span>3星</span>
                                    <div class="bar"><div class="fill" style="width: 8%"></div></div>
                                    <span>8%</span>
                                </div>
                                <div class="rating-bar">
                                    <span>2星</span>
                                    <div class="bar"><div class="fill" style="width: 2%"></div></div>
                                    <span>2%</span>
                                </div>
                                <div class="rating-bar">
                                    <span>1星</span>
                                    <div class="bar"><div class="fill" style="width: 0%"></div></div>
                                    <span>0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="qa">
                    <div class="product-qa">
                        <h3>常见问题</h3>
                        <div class="qa-item">
                            <h4>Q: 这套服装的质量如何？</h4>
                            <p>A: 我们采用高品质面料制作，做工精细，质量有保证。</p>
                        </div>
                        <div class="qa-item">
                            <h4>Q: 尺码如何选择？</h4>
                            <p>A: 请参考我们的尺码指南，或联系客服为您推荐合适的尺码。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script src="js/cart-storage.js"></script>
<script src="js/cart-utils.js"></script>
<script src="js/main.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 图片切换与预览
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');

    thumbnails.forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除所有缩略图的激活状态
            document.querySelector('.thumbnail.active')?.classList.remove('active');
            
            // 为当前缩略图添加激活状态
            this.classList.add('active');
            
            // 更新主图片
            const fullImage = this.dataset.fullImage || this.src.replace('w=100&h=125', 'w=600&h=750');
            
            // 添加淡入效果
            mainImage.style.opacity = '0';
            setTimeout(() => {
                mainImage.src = fullImage;
                mainImage.style.opacity = '1';
            }, 300);
        });
    });

    // 图片放大功能
    document.querySelector('.image-zoom').addEventListener('click', function() {
        const currentImage = mainImage.src;
        
        // 创建模态弹窗
        const modal = document.createElement('div');
        modal.classList.add('image-modal');
        
        // 添加弹窗内容
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <img src="${currentImage}" alt="放大图片">
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(modal);
        
        // 禁止页面滚动
        document.body.style.overflow = 'hidden';
        
        // 添加弹出效果
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
        
        // 关闭模态弹窗
        modal.querySelector('.close-modal').addEventListener('click', function() {
            closeModal();
        });
        
        // 点击模态背景关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
        
        // 关闭模态弹窗函数
        function closeModal() {
            modal.classList.remove('active');
            setTimeout(() => {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            }, 300);
        }
    });

    // 尺码选择
    const sizeOptions = document.querySelectorAll('.size-option');
    sizeOptions.forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.classList.contains('disabled')) return;
            
            document.querySelector('.size-option.active')?.classList.remove('active');
            this.classList.add('active');
            
            // 更新价格（根据尺码调整价格，这里只是示例）
            updateTotalPrice();
        });
    });

    // 颜色选择
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelector('.color-option.active')?.classList.remove('active');
            this.classList.add('active');
            
            // 这里可以添加切换对应颜色图片的逻辑
        });
    });

    // 套装选择
    const packageOptions = document.querySelectorAll('input[name="package"]');
    packageOptions.forEach(radio => {
        radio.addEventListener('change', function() {
            // 高亮显示选中项
            document.querySelectorAll('.package-option').forEach(option => {
                option.classList.toggle('selected', option.querySelector('input') === this);
            });
            
            // 更新价格
            updateTotalPrice();
        });
    });

    // 数量调整
    const qtyInput = document.querySelector('.qty-input');
    const minusBtn = document.querySelector('.qty-btn.minus');
    const plusBtn = document.querySelector('.qty-btn.plus');
    const maxStock = parseInt('<?php echo $product['stock']; ?>');

    minusBtn.addEventListener('click', function() {
        let currentValue = parseInt(qtyInput.value);
        if (currentValue > 1) {
            qtyInput.value = currentValue - 1;
            updateTotalPrice();
        }
        
        // 更新按钮状态
        updateQuantityButtons();
    });

    plusBtn.addEventListener('click', function() {
        let currentValue = parseInt(qtyInput.value);
        if (currentValue < maxStock) {
            qtyInput.value = currentValue + 1;
            updateTotalPrice();
        }
        
        // 更新按钮状态
        updateQuantityButtons();
    });

    qtyInput.addEventListener('change', function() {
        let value = parseInt(this.value);
        
        // 确保数量在有效范围内
        if (isNaN(value) || value < 1) value = 1;
        if (value > maxStock) value = maxStock;
        
        this.value = value;
        updateTotalPrice();
        updateQuantityButtons();
    });

    // 更新数量按钮状态
    function updateQuantityButtons() {
        let currentValue = parseInt(qtyInput.value);
        minusBtn.disabled = currentValue <= 1;
        plusBtn.disabled = currentValue >= maxStock;
        
        // 视觉反馈
        minusBtn.style.opacity = currentValue <= 1 ? '0.5' : '1';
        plusBtn.style.opacity = currentValue >= maxStock ? '0.5' : '1';
    }

    // 初始化按钮状态
    updateQuantityButtons();

    // 更新总价格
    function updateTotalPrice() {
        // 这里可以根据选择的套装、尺码、数量等计算最终价格
        // 这只是示例逻辑
        let basePrice = <?php echo $product['sale_price'] ?? $product['price']; ?>;
        let quantity = parseInt(qtyInput.value);
        let packageType = document.querySelector('input[name="package"]:checked').value;
        
        // 套装价格调整
        if (packageType === 'deluxe') {
            basePrice = 699; // 豪华套装价格
        }
        
        // 计算总价
        let totalPrice = basePrice * quantity;
        
        // 更新显示
        document.querySelector('.current-price').textContent = '¥' + totalPrice.toFixed(2);
    }

    // 收藏按钮
    const wishlistBtn = document.querySelector('.wishlist-btn');
    wishlistBtn.addEventListener('click', function() {
        this.classList.toggle('active');
        
        // 切换图标
        const icon = this.querySelector('i');
        if (this.classList.contains('active')) {
            icon.className = 'fas fa-heart';
            showToast('商品已加入收藏夹');
        } else {
            icon.className = 'far fa-heart';
            showToast('商品已移出收藏夹');
        }
    });

    // 标签页切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabId = this.dataset.tab;
            
            // 切换按钮状态
            document.querySelector('.tab-btn.active')?.classList.remove('active');
            this.classList.add('active');
            
            // 切换内容，带动画效果
            const activePane = document.querySelector('.tab-pane.active');
            const targetPane = document.getElementById(tabId);
            
            if (activePane === targetPane) return;
            
            activePane.style.opacity = '0';
            
            setTimeout(() => {
                activePane.classList.remove('active');
                targetPane.classList.add('active');
                targetPane.style.opacity = '0';
                
                setTimeout(() => {
                    targetPane.style.opacity = '1';
                }, 50);
            }, 300);
        });
    });

    // 加入购物车
    document.querySelector('.add-to-cart-btn').addEventListener('click', function() {
        // 获取选中选项
        const selectedSize = document.querySelector('.size-option.active').dataset.size;
        const selectedColor = document.querySelector('.color-option.active').dataset.color;
        const selectedPackage = document.querySelector('input[name="package"]:checked').value;
        const quantity = parseInt(qtyInput.value);
        
        // 构建商品信息
        const productInfo = {
            product_id: <?php echo $product['id']; ?>,
            name: '<?php echo addslashes($product['name']); ?>',
            price: <?php echo $product['price']; ?>,
            sale_price: <?php echo $product['sale_price'] ?: 'null'; ?>,
            size: selectedSize,
            color: selectedColor,
            package: selectedPackage,
            quantity: quantity,
            image: '<?php echo $product['images'][0]; ?>',
            sku: '<?php echo $product['sku'] ?: 'PROD-' . $product['id']; ?>'
        };

        // 使用新的购物车系统添加商品
        if (window.CartUtils) {
            const success = window.CartUtils.addToCart(productInfo);

            if (success) {
                // 添加按钮动效
                this.classList.add('added');
                setTimeout(() => {
                    this.classList.remove('added');
                }, 1000);
            }
        } else {
            console.error('购物车系统未加载');
            // 显示错误提示
            if (typeof showToast === 'function') {
                showToast('购物车系统未加载，请刷新页面重试', 'error');
            } else {
                alert('购物车系统未加载，请刷新页面重试');
            }
        }
    });

    // 立即购买
    document.querySelector('.buy-now-btn').addEventListener('click', function() {
        // 先添加到购物车
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.click();
            // 等待添加完成后跳转
            setTimeout(() => {
                window.location.href = 'checkout.php';
            }, 800);
        } else {
            window.location.href = 'checkout.php';
        }
    });
    
    // 弹出提示函数
    function showToast(message) {
        // 创建或获取已有的Toast
        let toast = document.querySelector('.toast-message');
        if (!toast) {
            toast = document.createElement('div');
            toast.className = 'toast-message';
            document.body.appendChild(toast);
        }
        
        // 设置消息并显示
        toast.textContent = message;
        toast.classList.add('active');
        
        // 定时隐藏
        setTimeout(() => {
            toast.classList.remove('active');
        }, 3000);
    }
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
