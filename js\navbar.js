/**
 * 导航栏增强功能
 * 包含用户下拉菜单、移动端菜单、搜索功能等
 */

class NavbarManager {
    constructor() {
        this.init();
    }

    init() {
        this.initUserDropdown();
        this.initMobileMenu();
        this.initSearch();
        this.initScrollEffect();
        this.initKeyboardNavigation();
    }

    /**
     * 初始化用户下拉菜单
     */
    initUserDropdown() {
        const userDropdowns = document.querySelectorAll('.user-dropdown');

        userDropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.user-dropdown-toggle');
            const menu = dropdown.querySelector('.user-menu');

            if (!toggle || !menu) return;

            // 点击切换菜单
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // 关闭其他下拉菜单
                this.closeAllDropdowns(dropdown);

                // 切换当前菜单
                const isActive = dropdown.classList.contains('active');
                if (isActive) {
                    this.closeDropdown(dropdown);
                } else {
                    this.openDropdown(dropdown);
                }
            });

            // 菜单项点击处理
            menu.addEventListener('click', (e) => {
                const menuItem = e.target.closest('.user-menu-item');
                if (menuItem && menuItem.tagName === 'A') {
                    // 允许链接正常跳转
                    this.closeDropdown(dropdown);
                }
            });

            // 键盘导航
            toggle.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle.click();
                } else if (e.key === 'Escape') {
                    this.closeDropdown(dropdown);
                }
            });
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-dropdown')) {
                this.closeAllDropdowns();
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });
    }

    /**
     * 打开下拉菜单
     */
    openDropdown(dropdown) {
        const menu = dropdown.querySelector('.user-menu');
        dropdown.classList.add('active');
        menu.classList.add('show');
        console.log('Opening dropdown menu', dropdown);

        // 设置焦点到第一个菜单项
        setTimeout(() => {
            const firstItem = menu.querySelector('.user-menu-item');
            if (firstItem) {
                firstItem.focus();
            }
        }, 100);
    }

    /**
     * 关闭下拉菜单
     */
    closeDropdown(dropdown) {
        const menu = dropdown.querySelector('.user-menu');
        dropdown.classList.remove('active');
        menu.classList.remove('show');
        console.log('Closing dropdown menu', dropdown);
    }

    /**
     * 关闭所有下拉菜单
     */
    closeAllDropdowns(except = null) {
        document.querySelectorAll('.user-dropdown').forEach(dropdown => {
            if (dropdown !== except) {
                this.closeDropdown(dropdown);
            }
        });
    }

    /**
     * 初始化移动端菜单
     */
    initMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        if (!menuToggle) return;

        // 创建移动端菜单
        this.createMobileMenu();

        const mobileNav = document.querySelector('.mobile-nav');
        if (!mobileNav) return;

        menuToggle.addEventListener('click', () => {
            const isActive = menuToggle.classList.contains('active');

            if (isActive) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        });

        // 点击菜单项后关闭菜单
        mobileNav.addEventListener('click', (e) => {
            const link = e.target.closest('.mobile-nav-link');
            if (link) {
                this.closeMobileMenu();
            }
        });
    }

    /**
     * 创建移动端菜单
     */
    createMobileMenu() {
        if (document.querySelector('.mobile-nav')) return;

        const mobileNav = document.createElement('div');
        mobileNav.className = 'mobile-nav';

        const currentPage = window.location.pathname.split('/').pop().replace('.php', '');

        mobileNav.innerHTML = `
            <div class="mobile-nav-content">
                <div class="mobile-nav-links">
                    <a href="index.php" class="mobile-nav-link ${currentPage === 'index' || currentPage === '' ? 'active' : ''}">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    <a href="category.php" class="mobile-nav-link ${currentPage === 'category' ? 'active' : ''}">
                        <i class="fas fa-th-large"></i>
                        <span>分类</span>
                    </a>
                    <a href="cart.php" class="mobile-nav-link ${currentPage === 'cart' ? 'active' : ''}">
                        <i class="fas fa-shopping-cart"></i>
                        <span>购物车</span>
                    </a>
                    <a href="user.php" class="mobile-nav-link ${currentPage === 'user' ? 'active' : ''}">
                        <i class="fas fa-user"></i>
                        <span>个人中心</span>
                    </a>
                    <a href="order.php" class="mobile-nav-link ${currentPage === 'order' ? 'active' : ''}">
                        <i class="fas fa-shopping-bag"></i>
                        <span>我的订单</span>
                    </a>
                    <a href="about.php" class="mobile-nav-link ${currentPage === 'about' ? 'active' : ''}">
                        <i class="fas fa-info-circle"></i>
                        <span>关于我们</span>
                    </a>
                </div>
            </div>
        `;

        document.body.appendChild(mobileNav);
    }

    /**
     * 打开移动端菜单
     */
    openMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const mobileNav = document.querySelector('.mobile-nav');

        menuToggle.classList.add('active');
        mobileNav.classList.add('show');

        // 防止背景滚动
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭移动端菜单
     */
    closeMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const mobileNav = document.querySelector('.mobile-nav');

        menuToggle.classList.remove('active');
        mobileNav.classList.remove('show');

        // 恢复背景滚动
        document.body.style.overflow = '';
    }

    /**
     * 初始化搜索功能
     */
    initSearch() {
        const searchInput = document.querySelector('.search-input');
        const searchContainer = document.querySelector('.search-container');
        const searchResults = document.querySelector('.search-results');

        if (!searchInput || !searchContainer || !searchResults) return;

        let searchTimeout;

        // 搜索输入处理
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            clearTimeout(searchTimeout);

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 300);
            } else {
                this.hideSearchResults();
            }
        });

        // 焦点处理
        searchInput.addEventListener('focus', () => {
            searchContainer.classList.add('focused');
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                searchContainer.classList.remove('focused');
                this.hideSearchResults();
            }, 200);
        });

        // 键盘导航
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideSearchResults();
                searchInput.blur();
            }
        });
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        const searchResults = document.querySelector('.search-results');

        // 模拟搜索结果
        const mockResults = [
            { name: '原神 甘雨 cos服装', price: '¥299', image: 'images/products/ganyu.jpg' },
            { name: '英雄联盟 阿狸 cos服装', price: '¥259', image: 'images/products/ahri.jpg' },
            { name: '鬼灭之刃 祢豆子 cos服装', price: '¥199', image: 'images/products/nezuko.jpg' }
        ].filter(item => item.name.toLowerCase().includes(query.toLowerCase()));

        if (mockResults.length > 0) {
            this.showSearchResults(mockResults);
        } else {
            this.showNoResults();
        }
    }

    /**
     * 显示搜索结果
     */
    showSearchResults(results) {
        const searchResults = document.querySelector('.search-results');

        searchResults.innerHTML = results.map(result => `
            <div class="search-result-item" onclick="window.location.href='detail.php?id=${result.name}'">
                <img src="${result.image}" alt="${result.name}" class="search-result-image" onerror="this.src='images/placeholder.jpg'">
                <div class="search-result-info">
                    <div class="search-result-name">${result.name}</div>
                    <div class="search-result-price">${result.price}</div>
                </div>
            </div>
        `).join('');

        searchResults.classList.add('show');
    }

    /**
     * 显示无结果
     */
    showNoResults() {
        const searchResults = document.querySelector('.search-results');

        searchResults.innerHTML = `
            <div class="search-result-item">
                <div class="search-result-info">
                    <div class="search-result-name">未找到相关商品</div>
                </div>
            </div>
        `;

        searchResults.classList.add('show');
    }

    /**
     * 隐藏搜索结果
     */
    hideSearchResults() {
        const searchResults = document.querySelector('.search-results');
        searchResults.classList.remove('show');
    }

    /**
     * 初始化滚动效果
     */
    initScrollEffect() {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            lastScrollY = currentScrollY;
        });
    }

    /**
     * 初始化键盘导航
     */
    initKeyboardNavigation() {
        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            // Alt + S 聚焦搜索框
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Alt + M 切换移动端菜单
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                const menuToggle = document.querySelector('.menu-toggle');
                if (menuToggle && window.innerWidth <= 768) {
                    menuToggle.click();
                }
            }
        });
    }
}

// 初始化导航栏管理器
document.addEventListener('DOMContentLoaded', () => {
    new NavbarManager();
});

// 导出供其他脚本使用
window.NavbarManager = NavbarManager;
