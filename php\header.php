  <div class="user-dropdown">
      <button class="nav-link user-dropdown-toggle" aria-label="用户中心" type="button">
          <?php if ($current_user['avatar']): ?>
              <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>"
                   alt="用户头像" class="nav-user-avatar">
          <?php else: ?>
              <i class="fas fa-user nav-link-icon"></i>
          <?php endif; ?>
          <span class="nav-link-text">
              <?php echo htmlspecialchars($current_user['nickname'] ?? $current_user['username'] ?? '用户'); ?>
              <!-- 当前用户角色: <?php echo htmlspecialchars($current_user['role'] ?? 'not set'); ?> -->
          </span>
          <i class="fas fa-chevron-down dropdown-arrow"></i>
      </button>
      <div class="user-menu">
          <a href="user.php" class="user-menu-item">
              <i class="fas fa-user"></i> 个人中心
          </a>
          <a href="order.php" class="user-menu-item">
              <i class="fas fa-shopping-bag"></i> 我的订单
          </a>
          <a href="user.php#wishlist" class="user-menu-item">
              <i class="fas fa-heart"></i> 心愿单
          </a>
          <a href="address.php" class="user-menu-item">
              <i class="fas fa-map-marker-alt"></i> 地址管理
          </a>
          
          <?php if ($current_user['role'] === 'admin'): ?>
          <!-- 管理员专用选项 -->
          <div class="user-menu-divider"></div>
          <a href="admin/dashboard.php" class="user-menu-item admin-menu-item">
              <i class="fas fa-tachometer-alt"></i> 管理中心
          </a>
          <a href="admin/orders.php" class="user-menu-item admin-menu-item">
              <i class="fas fa-clipboard-list"></i> 订单管理
          </a>
          <a href="admin/products.php" class="user-menu-item admin-menu-item">
              <i class="fas fa-box"></i> 商品管理
          </a>
          <a href="admin/users.php" class="user-menu-item admin-menu-item">
              <i class="fas fa-users-cog"></i> 用户管理
          </a>
          <?php elseif ($current_user['role'] === 'vip'): ?>
          <!-- VIP用户专属选项 -->
          <div class="user-menu-divider"></div>
          <a href="vip-services.php" class="user-menu-item vip-menu-item">
              <i class="fas fa-crown"></i> VIP专属服务
          </a>
          <a href="vip-discount.php" class="user-menu-item vip-menu-item">
              <i class="fas fa-percentage"></i> 专属折扣
          </a>
          <a href="vip-support.php" class="user-menu-item vip-menu-item">
              <i class="fas fa-headset"></i> 优先客服
          </a>
          <?php endif; ?>
          
          <div class="user-menu-divider"></div>
          <a href="login.php?action=logout" class="user-menu-item">
              <i class="fas fa-sign-out-alt"></i> 退出登录
          </a>
      </div>
  </div>
  
<!-- 用户菜单角色特定样式 -->
<style>
/* 管理员菜单项样式 */
.admin-menu-item {
    background-color: rgba(220, 53, 69, 0.08);
    border-left: 3px solid #dc3545;
    font-weight: 500;
}

.admin-menu-item i {
    color: #dc3545;
}

.admin-menu-item:hover {
    background-color: rgba(220, 53, 69, 0.15);
}

/* VIP菜单项样式 */
.vip-menu-item {
    background-color: rgba(255, 193, 7, 0.08);
    border-left: 3px solid #ffc107;
    font-weight: 500;
}

.vip-menu-item i {
    color: #ffc107;
}

.vip-menu-item:hover {
    background-color: rgba(255, 193, 7, 0.15);
}
</style>