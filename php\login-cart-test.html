<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录状态和购物车功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .test-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #007bff;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }

        .status-logged-in {
            background-color: #d4edda;
            color: #155724;
        }

        .status-logged-out {
            background-color: #f8d7da;
            color: #721c24;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .product-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-info h4 {
            margin: 0 0 5px 0;
            color: #212529;
        }

        .product-price {
            color: #007bff;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .add-to-cart-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
        }

        .add-to-cart-btn:disabled {
            background: #6c757d;
            transform: none;
            cursor: not-allowed;
        }

        .cart-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录状态和购物车功能测试</h1>

        <!-- 登录状态测试 -->
        <div class="test-section">
            <h3 class="section-title">1. 登录状态管理测试</h3>
            
            <div>
                <strong>当前登录状态:</strong>
                <span id="login-status">检查中...</span>
                <span id="login-indicator" class="status-indicator">未知</span>
            </div>
            
            <div style="margin: 15px 0;">
                <button class="test-button" onclick="simulateLogin()">模拟登录</button>
                <button class="test-button" onclick="simulateLogout()">模拟退出</button>
                <button class="test-button" onclick="checkLoginStatus()">检查状态</button>
                <button class="test-button" onclick="testLoginRedirect()">测试登录跳转</button>
            </div>
            
            <div id="user-info" style="margin-top: 10px;"></div>
        </div>

        <!-- 购物车功能测试 -->
        <div class="test-section">
            <h3 class="section-title">2. 购物车功能测试</h3>
            
            <div class="cart-info">
                <strong>购物车状态:</strong>
                <span id="cart-count">0</span> 件商品
                <button class="test-button" onclick="updateCartDisplay()" style="margin-left: 10px;">刷新显示</button>
                <button class="test-button" onclick="clearCart()">清空购物车</button>
            </div>

            <!-- 模拟商品 -->
            <div class="product-card">
                <div class="product-info">
                    <h4>原神 - 刻晴星霜华裳</h4>
                    <div class="product-price">¥499.00</div>
                </div>
                <button class="add-to-cart-btn" data-product-id="1001" onclick="testAddToCart(this)">
                    <i class="fas fa-cart-plus"></i>
                    <span>加入购物车</span>
                </button>
            </div>

            <div class="product-card">
                <div class="product-info">
                    <h4>英雄联盟 - 阿狸</h4>
                    <div class="product-price">¥649.00</div>
                </div>
                <button class="add-to-cart-btn" data-product-id="1002" onclick="testAddToCart(this)">
                    <i class="fas fa-cart-plus"></i>
                    <span>加入购物车</span>
                </button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3 class="section-title">3. 测试日志</h3>
            <div id="log-area" class="log-area">测试开始...\n</div>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 模拟用户认证系统
        const UserAuth = {
            isLoggedIn: false,
            currentUser: null,
            
            checkLogin: function() {
                return this.isLoggedIn;
            },
            
            getCurrentUser: function() {
                return this.currentUser;
            },
            
            redirectToLogin: function(returnUrl = null) {
                const currentUrl = returnUrl || window.location.href;
                const loginUrl = 'login.php?redirect=' + encodeURIComponent(currentUrl);
                log(`跳转到登录页面: ${loginUrl}`);
                // 在实际环境中会执行: window.location.href = loginUrl;
                alert(`将跳转到登录页面:\n${loginUrl}`);
            },
            
            showLoginPrompt: function(message = '请先登录后再进行此操作') {
                if (confirm(message + '\n\n点击确定跳转到登录页面')) {
                    this.redirectToLogin();
                }
            },
            
            login: function(user) {
                this.isLoggedIn = true;
                this.currentUser = user;
                localStorage.setItem('user_login_state', JSON.stringify({
                    isLoggedIn: true,
                    user: user,
                    timestamp: Date.now()
                }));
                log(`用户登录成功: ${user.username}`);
                updateLoginDisplay();
            },
            
            logout: function() {
                this.isLoggedIn = false;
                this.currentUser = null;
                localStorage.removeItem('user_login_state');
                log('用户已退出登录');
                updateLoginDisplay();
            }
        };

        // 购物车管理系统
        const CartManager = {
            addToCart: function(productId, quantity = 1) {
                if (!UserAuth.checkLogin()) {
                    UserAuth.showLoginPrompt('添加商品到购物车需要先登录');
                    return false;
                }
                
                log(`添加商品到购物车: 商品ID=${productId}, 数量=${quantity}`);
                
                let cart = this.getCart();
                const existingItem = cart.find(item => item.productId === productId);
                
                if (existingItem) {
                    existingItem.quantity += quantity;
                    log(`更新商品数量: ${existingItem.quantity}`);
                } else {
                    cart.push({
                        productId: productId,
                        quantity: quantity,
                        addedAt: Date.now()
                    });
                    log(`新增商品到购物车`);
                }
                
                this.saveCart(cart);
                this.showNotification('商品已添加到购物车！', 'success');
                this.updateCartBadge();
                
                return true;
            },
            
            getCart: function() {
                const cartData = localStorage.getItem('shopping_cart');
                return cartData ? JSON.parse(cartData) : [];
            },
            
            saveCart: function(cart) {
                localStorage.setItem('shopping_cart', JSON.stringify(cart));
                log(`购物车已保存: ${cart.length} 种商品`);
            },
            
            getCartCount: function() {
                const cart = this.getCart();
                return cart.reduce((total, item) => total + item.quantity, 0);
            },
            
            updateCartBadge: function() {
                const count = this.getCartCount();
                document.getElementById('cart-count').textContent = count;
                log(`购物车徽章已更新: ${count} 件商品`);
            },
            
            clearCart: function() {
                localStorage.removeItem('shopping_cart');
                this.updateCartBadge();
                log('购物车已清空');
            },
            
            showNotification: function(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);
                
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        };

        // 日志功能
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('log-area');
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-area').textContent = '日志已清空...\n';
        }

        // 更新登录状态显示
        function updateLoginDisplay() {
            const statusElement = document.getElementById('login-status');
            const indicatorElement = document.getElementById('login-indicator');
            const userInfoElement = document.getElementById('user-info');
            
            if (UserAuth.checkLogin()) {
                const user = UserAuth.getCurrentUser();
                statusElement.textContent = `已登录 (${user.username})`;
                indicatorElement.textContent = '已登录';
                indicatorElement.className = 'status-indicator status-logged-in';
                userInfoElement.innerHTML = `
                    <div style="background: #d4edda; padding: 10px; border-radius: 8px; margin-top: 10px;">
                        <strong>用户信息:</strong><br>
                        用户名: ${user.username}<br>
                        邮箱: ${user.email}<br>
                        角色: ${user.role}<br>
                        昵称: ${user.nickname}
                    </div>
                `;
            } else {
                statusElement.textContent = '未登录';
                indicatorElement.textContent = '未登录';
                indicatorElement.className = 'status-indicator status-logged-out';
                userInfoElement.innerHTML = '';
            }
        }

        // 测试函数
        function simulateLogin() {
            const testUser = {
                id: 1,
                username: 'testuser',
                email: '<EMAIL>',
                role: 'user',
                nickname: '测试用户'
            };
            UserAuth.login(testUser);
        }

        function simulateLogout() {
            UserAuth.logout();
        }

        function checkLoginStatus() {
            log(`登录状态检查: ${UserAuth.checkLogin() ? '已登录' : '未登录'}`);
            updateLoginDisplay();
        }

        function testLoginRedirect() {
            UserAuth.redirectToLogin();
        }

        function testAddToCart(button) {
            const productId = button.getAttribute('data-product-id');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
            button.disabled = true;
            
            setTimeout(() => {
                const success = CartManager.addToCart(productId, 1);
                
                button.innerHTML = originalText;
                button.disabled = false;
                
                if (success) {
                    button.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        button.style.transform = '';
                    }, 150);
                }
            }, 500);
        }

        function updateCartDisplay() {
            CartManager.updateCartBadge();
        }

        function clearCart() {
            CartManager.clearCart();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...');
            updateLoginDisplay();
            CartManager.updateCartBadge();
            log('初始化完成');
        });
    </script>
</body>
</html>
