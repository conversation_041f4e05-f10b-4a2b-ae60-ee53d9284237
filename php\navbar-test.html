<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏测试 - COSPlay购物网站</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/layout-styles.css">
    <link rel="stylesheet" href="css/navbar-unified.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 测试页面样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }

        .test-content {
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .test-title {
            color: #212529;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .navbar-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .navbar-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .navbar-info p {
            color: #424242;
            margin: 5px 0;
        }

        .measurement-box {
            border: 2px dashed #007bff;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: rgba(0, 123, 255, 0.05);
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="navbar-container">
                    <a href="index.php" class="logo" aria-label="COSPlay首页">COS<span>Play</span></a>

                    <div class="search-container">
                        <form action="category.php" method="get" role="search">
                            <input type="text" class="search-input" name="search" placeholder="搜索角色、动漫或游戏..."
                                aria-label="搜索角色、动漫或游戏">
                            <button type="submit" class="search-btn" aria-label="搜索">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>

                    <div class="nav-links">
                        <a href="category.php" class="nav-link" aria-label="商品分类">
                            <i class="fas fa-th-large nav-link-icon"></i>
                            <span class="nav-link-text">分类</span>
                        </a>
                        <a href="cart.php" class="nav-link" aria-label="购物车">
                            <i class="fas fa-shopping-cart nav-link-icon"></i>
                            <span class="nav-link-count">3</span>
                            <span class="nav-link-text">购物车</span>
                        </a>

                        <!-- 用户菜单 -->
                        <div class="user-dropdown">
                            <button class="nav-link user-dropdown-toggle" aria-label="用户菜单" type="button">
                                <i class="fas fa-user nav-link-icon"></i>
                                <span class="nav-link-text">登录</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- 测试内容 -->
    <main class="test-content">
        <div class="test-section">
            <h1 class="test-title">导航栏样式测试</h1>

            <div class="navbar-info">
                <h3>📏 导航栏尺寸信息</h3>
                <p><strong>桌面端高度：</strong> 80px (--navbar-height)</p>
                <p><strong>桌面端内边距：</strong> 16px 0 (--navbar-padding)</p>
                <p><strong>平板端高度：</strong> 70px</p>
                <p><strong>平板端内边距：</strong> 12px 0</p>
                <p><strong>手机端高度：</strong> 60px</p>
                <p><strong>手机端内边距：</strong> 10px 0</p>
            </div>

            <div class="measurement-box">
                <h3>🎯 测试说明</h3>
                <p>请在不同设备尺寸下测试导航栏的显示效果</p>
                <p>检查导航栏的高度和内边距是否统一</p>
                <p>确保所有元素垂直居中对齐</p>
            </div>

            <div class="test-section">
                <h2 class="test-title">响应式测试</h2>
                <p>请调整浏览器窗口大小来测试不同断点下的导航栏效果：</p>
                <ul>
                    <li><strong>桌面端：</strong> > 768px - 高度 80px，内边距 16px 0</li>
                    <li><strong>平板端：</strong> ≤ 768px - 高度 70px，内边距 12px 0</li>
                    <li><strong>手机端：</strong> ≤ 480px - 高度 60px，内边距 10px 0</li>
                </ul>
            </div>

            <div class="test-section">
                <h2 class="test-title">视觉检查项目</h2>
                <ul>
                    <li>✅ Logo 垂直居中</li>
                    <li>✅ 搜索框垂直居中</li>
                    <li>✅ 导航链接垂直居中</li>
                    <li>✅ 用户菜单垂直居中</li>
                    <li>✅ 整体高度统一</li>
                    <li>✅ 内边距一致</li>
                </ul>
            </div>
        </div>
    </main>

    <script>
        // 显示当前窗口尺寸
        function updateWindowSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            console.log(`窗口尺寸: ${width} x ${height}`);

            // 显示当前断点
            if (width <= 480) {
                console.log('当前断点: 手机端 (≤ 480px)');
            } else if (width <= 768) {
                console.log('当前断点: 平板端 (≤ 768px)');
            } else {
                console.log('当前断点: 桌面端 (> 768px)');
            }
        }

        window.addEventListener('resize', updateWindowSize);
        updateWindowSize();

        // 测试导航栏高度
        function checkNavbarHeight() {
            const navbar = document.querySelector('.navbar');
            const navbarContainer = document.querySelector('.navbar-container');

            if (navbar && navbarContainer) {
                const navbarHeight = navbar.offsetHeight;
                const containerStyles = window.getComputedStyle(navbarContainer);
                const paddingTop = containerStyles.paddingTop;
                const paddingBottom = containerStyles.paddingBottom;

                console.log(`导航栏实际高度: ${navbarHeight}px`);
                console.log(`容器上内边距: ${paddingTop}`);
                console.log(`容器下内边距: ${paddingBottom}`);
            }
        }

        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', checkNavbarHeight);
    </script>
</body>

</html>