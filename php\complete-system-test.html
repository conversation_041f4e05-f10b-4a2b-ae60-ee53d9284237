<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整登录和购物车系统测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .cart-info {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.2);
            padding: 8px 12px;
            border-radius: 20px;
        }

        .nav-link-count {
            position: relative;
            background-color: #dc3545;
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            height: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            margin-left: 5px;
        }

        .test-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #007bff;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .product-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            color: #212529;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0 0 8px 0;
        }

        .product-meta {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin: 8px 0 16px 0;
        }

        .product-category {
            color: #6c757d;
            font-size: 0.85rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            align-self: flex-start;
        }

        .product-pricing {
            display: flex;
            align-items: baseline;
            gap: 8px;
        }

        .current-price {
            color: #212529;
            font-weight: 800;
            font-size: 1.3rem;
        }

        .original-price {
            color: #8e9ba8;
            text-decoration: line-through;
            font-size: 1rem;
            font-weight: 500;
        }

        .add-to-cart-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
        }

        .add-to-cart-btn:disabled {
            background: #6c757d;
            transform: none;
            cursor: not-allowed;
        }

        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: #218838;
        }

        .test-button.logout {
            background: #dc3545;
        }

        .test-button.logout:hover {
            background: #c82333;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-logged-in {
            background-color: #d4edda;
            color: #155724;
        }

        .status-logged-out {
            background-color: #f8d7da;
            color: #721c24;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 模拟导航栏 -->
        <div class="header">
            <div>
                <h1 style="margin: 0;">COSPlay购物网站</h1>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">完整登录和购物车系统测试</p>
            </div>
            <div class="user-info">
                <div id="user-status">
                    <span id="login-status">检查中...</span>
                    <span id="login-indicator" class="status-indicator">未知</span>
                </div>
                <div class="cart-info">
                    <i class="fas fa-shopping-cart"></i>
                    <span>购物车</span>
                    <span id="cart-count" class="nav-link-count" style="display: none;">0</span>
                </div>
            </div>
        </div>

        <!-- 登录控制 -->
        <div class="test-section">
            <h3 class="section-title">登录状态控制</h3>
            <button class="test-button" onclick="simulateLogin()">模拟登录</button>
            <button class="test-button logout" onclick="simulateLogout()">退出登录</button>
            <button class="test-button" onclick="checkLoginStatus()">检查状态</button>
            <div id="user-details" style="margin-top: 15px;"></div>
        </div>

        <!-- 商品展示 -->
        <div class="test-section">
            <h3 class="section-title">商品展示 - 测试购物车功能</h3>
            <p>点击"加入购物车"按钮测试登录检查和购物车功能</p>
            
            <div class="product-grid">
                <!-- 商品1 -->
                <div class="product-card">
                    <div class="product-image">原神角色服装</div>
                    <div class="product-info">
                        <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                        <div class="product-meta">
                            <span class="product-category">原神</span>
                            <div class="product-pricing">
                                <span class="current-price">¥499.00</span>
                                <span class="original-price">¥599.00</span>
                            </div>
                        </div>
                        <button class="add-to-cart-btn" data-product-id="1001">
                            <i class="fas fa-cart-plus"></i>
                            <span>加入购物车</span>
                        </button>
                    </div>
                </div>

                <!-- 商品2 -->
                <div class="product-card">
                    <div class="product-image">英雄联盟角色</div>
                    <div class="product-info">
                        <h3 class="product-name">英雄联盟 - 阿狸</h3>
                        <div class="product-meta">
                            <span class="product-category">英雄联盟</span>
                            <div class="product-pricing">
                                <span class="current-price">¥649.00</span>
                            </div>
                        </div>
                        <button class="add-to-cart-btn" data-product-id="1002">
                            <i class="fas fa-cart-plus"></i>
                            <span>加入购物车</span>
                        </button>
                    </div>
                </div>

                <!-- 商品3 -->
                <div class="product-card">
                    <div class="product-image">鬼灭之刃角色</div>
                    <div class="product-info">
                        <h3 class="product-name">鬼灭之刃 - 炭治郎</h3>
                        <div class="product-meta">
                            <span class="product-category">鬼灭之刃</span>
                            <div class="product-pricing">
                                <span class="current-price">¥599.00</span>
                            </div>
                        </div>
                        <button class="add-to-cart-btn" data-product-id="1003">
                            <i class="fas fa-cart-plus"></i>
                            <span>加入购物车</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 购物车操作 -->
        <div class="test-section">
            <h3 class="section-title">购物车操作</h3>
            <button class="test-button" onclick="updateCartDisplay()">刷新购物车</button>
            <button class="test-button logout" onclick="clearCart()">清空购物车</button>
            <button class="test-button" onclick="viewCart()">查看购物车内容</button>
        </div>
    </div>

    <script>
        // 从 index.php 复制的用户认证和购物车管理系统
        const UserAuth = {
            isLoggedIn: false,
            currentUser: null,
            
            checkLogin: function() {
                return this.isLoggedIn;
            },
            
            getCurrentUser: function() {
                return this.currentUser;
            },
            
            redirectToLogin: function(returnUrl = null) {
                const currentUrl = returnUrl || window.location.href;
                const loginUrl = 'login.php?redirect=' + encodeURIComponent(currentUrl);
                alert(`将跳转到登录页面:\n${loginUrl}`);
            },
            
            showLoginPrompt: function(message = '请先登录后再进行此操作') {
                if (confirm(message + '\n\n点击确定跳转到登录页面')) {
                    this.redirectToLogin();
                }
            },
            
            login: function(user) {
                this.isLoggedIn = true;
                this.currentUser = user;
                localStorage.setItem('user_login_state', JSON.stringify({
                    isLoggedIn: true,
                    user: user,
                    timestamp: Date.now()
                }));
                updateLoginDisplay();
                CartManager.updateCartBadge();
            },
            
            logout: function() {
                this.isLoggedIn = false;
                this.currentUser = null;
                localStorage.removeItem('user_login_state');
                updateLoginDisplay();
                CartManager.updateCartBadge();
            }
        };

        const CartManager = {
            addToCart: function(productId, quantity = 1) {
                if (!UserAuth.checkLogin()) {
                    UserAuth.showLoginPrompt('添加商品到购物车需要先登录');
                    return Promise.resolve(false);
                }
                
                // 模拟API调用
                return new Promise((resolve) => {
                    setTimeout(() => {
                        this.addToCartLocal(productId, quantity);
                        this.showNotification('商品已添加到购物车！', 'success');
                        this.updateCartBadge();
                        resolve(true);
                    }, 800);
                });
            },
            
            addToCartLocal: function(productId, quantity = 1) {
                let cart = this.getCart();
                const existingItem = cart.find(item => item.productId === productId);
                
                if (existingItem) {
                    existingItem.quantity += quantity;
                } else {
                    cart.push({
                        productId: productId,
                        quantity: quantity,
                        addedAt: Date.now()
                    });
                }
                
                this.saveCart(cart);
            },
            
            getCart: function() {
                const cartData = localStorage.getItem('shopping_cart');
                return cartData ? JSON.parse(cartData) : [];
            },
            
            saveCart: function(cart) {
                localStorage.setItem('shopping_cart', JSON.stringify(cart));
            },
            
            getCartCount: function() {
                const cart = this.getCart();
                return cart.reduce((total, item) => total + item.quantity, 0);
            },
            
            updateCartBadge: function() {
                const count = UserAuth.checkLogin() ? this.getCartCount() : 0;
                const badge = document.getElementById('cart-count');
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline-flex' : 'none';
            },
            
            clearCart: function() {
                localStorage.removeItem('shopping_cart');
                this.updateCartBadge();
                this.showNotification('购物车已清空', 'success');
            },
            
            showNotification: function(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);
                
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        };

        // 更新登录状态显示
        function updateLoginDisplay() {
            const statusElement = document.getElementById('login-status');
            const indicatorElement = document.getElementById('login-indicator');
            const userDetailsElement = document.getElementById('user-details');
            
            if (UserAuth.checkLogin()) {
                const user = UserAuth.getCurrentUser();
                statusElement.textContent = `已登录 (${user.username})`;
                indicatorElement.textContent = '已登录';
                indicatorElement.className = 'status-indicator status-logged-in';
                userDetailsElement.innerHTML = `
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;">
                        <strong>当前用户信息:</strong><br>
                        用户名: ${user.username}<br>
                        邮箱: ${user.email}<br>
                        昵称: ${user.nickname}
                    </div>
                `;
            } else {
                statusElement.textContent = '未登录';
                indicatorElement.textContent = '未登录';
                indicatorElement.className = 'status-indicator status-logged-out';
                userDetailsElement.innerHTML = `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb;">
                        <strong>提示:</strong> 请先登录后再使用购物车功能
                    </div>
                `;
            }
        }

        // 测试函数
        function simulateLogin() {
            const testUser = {
                id: 1,
                username: 'testuser',
                email: '<EMAIL>',
                nickname: '测试用户'
            };
            UserAuth.login(testUser);
        }

        function simulateLogout() {
            UserAuth.logout();
        }

        function checkLoginStatus() {
            updateLoginDisplay();
        }

        function updateCartDisplay() {
            CartManager.updateCartBadge();
        }

        function clearCart() {
            CartManager.clearCart();
        }

        function viewCart() {
            const cart = CartManager.getCart();
            if (cart.length === 0) {
                alert('购物车为空');
            } else {
                const cartInfo = cart.map(item => 
                    `商品ID: ${item.productId}, 数量: ${item.quantity}`
                ).join('\n');
                alert(`购物车内容:\n${cartInfo}`);
            }
        }

        // 购物车按钮事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('.add-to-cart-btn')) {
                e.preventDefault();
                
                const button = e.target.closest('.add-to-cart-btn');
                const productId = button.getAttribute('data-product-id');
                
                if (productId) {
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
                    button.disabled = true;
                    
                    CartManager.addToCart(productId, 1)
                        .then(success => {
                            button.innerHTML = originalText;
                            button.disabled = false;
                            
                            if (success) {
                                button.style.transform = 'scale(0.95)';
                                button.style.background = '#28a745';
                                
                                setTimeout(() => {
                                    button.style.transform = '';
                                    button.style.background = '';
                                }, 300);
                            }
                        });
                }
            }
        });

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateLoginDisplay();
            CartManager.updateCartBadge();
        });
    </script>
</body>
</html>
