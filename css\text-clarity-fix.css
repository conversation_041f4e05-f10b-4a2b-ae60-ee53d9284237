/* 
 * 全局文字清晰修复CSS
 * 移除所有可能导致文字模糊的效果
 * 适用于整个网站的所有页面
 */

/* 全局文字清晰设置 */
* {
    /* 移除字体平滑，使用系统默认 */
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    
    /* 优化文字渲染 */
    text-rendering: optimizeSpeed !important;
    
    /* 移除所有阴影效果 */
    text-shadow: none !important;
    
    /* 移除所有滤镜效果 */
    filter: none !important;
    -webkit-filter: none !important;
    
    /* 移除背景模糊 */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 移除可能导致模糊的3D变换 */
*:not(.dropdown-arrow):not(.user-menu):not(.toast-message) {
    /* 移除3D变换 */
    transform: none !important;
    -webkit-transform: none !important;
    
    /* 移除透视效果 */
    perspective: none !important;
    -webkit-perspective: none !important;
    
    /* 移除背面可见性设置 */
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
    
    /* 移除will-change优化 */
    will-change: auto !important;
}

/* 特别处理文字元素 */
h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, label {
    /* 强制清晰文字渲染 */
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    
    /* 移除文字阴影 */
    text-shadow: none !important;
    
    /* 移除滤镜 */
    filter: none !important;
    -webkit-filter: none !important;
    
    /* 移除变换 */
    transform: none !important;
    -webkit-transform: none !important;
}

/* 商品卡片文字清晰 */
.product-card,
.product-card * {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    transform: none !important;
    -webkit-transform: none !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
}

/* 导航栏文字清晰 */
.navbar,
.navbar *,
.nav-link,
.nav-link * {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 分类导航文字清晰 */
.category-item,
.category-item *,
.quick-category-item,
.quick-category-item * {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 按钮文字清晰 */
button,
.btn,
.cta-button,
.add-to-cart,
.add-to-cart-btn {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 表单元素文字清晰 */
input,
textarea,
select,
label {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 价格文字特别清晰 */
.product-price,
.price,
.product-price-original,
.product-price-container {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    transform: none !important;
    -webkit-transform: none !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
    will-change: auto !important;
}

/* 移除悬停时的模糊效果 */
*:hover {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除焦点时的模糊效果 */
*:focus {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 移除激活时的模糊效果 */
*:active {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 特殊情况：保留必要的动画但移除模糊 */
.dropdown-arrow {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

.user-menu {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

.toast-message {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 响应式设备优化 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
    * {
        -webkit-font-smoothing: auto !important;
        -moz-osx-font-smoothing: auto !important;
    }
}

@media screen and (min-resolution: 192dpi) {
    * {
        -webkit-font-smoothing: auto !important;
        -moz-osx-font-smoothing: auto !important;
    }
}

/* 高DPI屏幕优化 */
@media screen and (min-resolution: 2dppx) {
    * {
        -webkit-font-smoothing: auto !important;
        -moz-osx-font-smoothing: auto !important;
        text-rendering: optimizeSpeed !important;
    }
}

/* 移动设备优化 */
@media (max-width: 768px) {
    * {
        -webkit-font-smoothing: auto !important;
        -moz-osx-font-smoothing: auto !important;
        text-rendering: optimizeSpeed !important;
        text-shadow: none !important;
        filter: none !important;
        -webkit-filter: none !important;
    }
}

/* 打印时的文字清晰 */
@media print {
    * {
        -webkit-font-smoothing: auto !important;
        -moz-osx-font-smoothing: auto !important;
        text-rendering: optimizeSpeed !important;
        text-shadow: none !important;
        filter: none !important;
        -webkit-filter: none !important;
        transform: none !important;
        -webkit-transform: none !important;
    }
}
