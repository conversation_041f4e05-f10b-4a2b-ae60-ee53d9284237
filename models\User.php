<?php
/**
 * 用户模型类
 * 处理用户相关的数据库操作
 */

class User {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * 用户注册
     */
    public function register($data) {
        // 检查用户名是否已存在
        if ($this->usernameExists($data['username'])) {
            return ['success' => false, 'message' => '用户名已存在'];
        }
        
        // 检查邮箱是否已存在
        if ($this->emailExists($data['email'])) {
            return ['success' => false, 'message' => '邮箱已被注册'];
        }
        
        // 密码加密
        $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO users (username, email, password_hash, first_name, last_name, phone) 
                VALUES (:username, :email, :password_hash, :first_name, :last_name, :phone)";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':password_hash', $password_hash);
            $stmt->bindParam(':first_name', $data['first_name']);
            $stmt->bindParam(':last_name', $data['last_name']);
            $stmt->bindParam(':phone', $data['phone']);
            
            if ($stmt->execute()) {
                $user_id = $this->db->lastInsertId();
                return ['success' => true, 'user_id' => $user_id, 'message' => '注册成功'];
            } else {
                return ['success' => false, 'message' => '注册失败，请重试'];
            }
        } catch (PDOException $e) {
            logError("用户注册失败: " . $e->getMessage());
            return ['success' => false, 'message' => '注册失败，请重试'];
        }
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password) {
        $sql = "SELECT * FROM users WHERE (username = :username OR email = :username) AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // 更新最后登录时间
            $this->updateLastLogin($user['id']);
            
            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_data'] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'avatar' => $user['avatar']
            ];
            
            return ['success' => true, 'user' => $user, 'message' => '登录成功'];
        } else {
            return ['success' => false, 'message' => '用户名或密码错误'];
        }
    }
    
    /**
     * 用户登出
     */
    public function logout() {
        session_destroy();
        return ['success' => true, 'message' => '已安全退出'];
    }
    
    /**
     * 根据ID获取用户信息
     */
    public function getUserById($id) {
        $sql = "SELECT * FROM users WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * 更新用户信息
     */
    public function updateUser($id, $data) {
        $fields = [];
        $params = ['id' => $id];
        
        $allowed_fields = ['first_name', 'last_name', 'phone', 'avatar'];
        
        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return ['success' => false, 'message' => '没有要更新的数据'];
        }
        
        $sql = "UPDATE users SET " . implode(', ', $fields) . " WHERE id = :id";
        
        try {
            $stmt = $this->db->prepare($sql);
            if ($stmt->execute($params)) {
                // 更新会话数据
                if (isset($_SESSION['user_data'])) {
                    foreach ($allowed_fields as $field) {
                        if (isset($data[$field])) {
                            $_SESSION['user_data'][$field] = $data[$field];
                        }
                    }
                }
                return ['success' => true, 'message' => '信息更新成功'];
            } else {
                return ['success' => false, 'message' => '更新失败，请重试'];
            }
        } catch (PDOException $e) {
            logError("用户信息更新失败: " . $e->getMessage());
            return ['success' => false, 'message' => '更新失败，请重试'];
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword($user_id, $old_password, $new_password) {
        // 验证旧密码
        $user = $this->getUserById($user_id);
        if (!$user || !password_verify($old_password, $user['password_hash'])) {
            return ['success' => false, 'message' => '原密码错误'];
        }
        
        // 更新密码
        $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $sql = "UPDATE users SET password_hash = :password_hash WHERE id = :id";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':password_hash', $new_password_hash);
            $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => '密码修改成功'];
            } else {
                return ['success' => false, 'message' => '密码修改失败，请重试'];
            }
        } catch (PDOException $e) {
            logError("密码修改失败: " . $e->getMessage());
            return ['success' => false, 'message' => '密码修改失败，请重试'];
        }
    }
    
    /**
     * 检查用户名是否存在
     */
    public function usernameExists($username) {
        $sql = "SELECT COUNT(*) FROM users WHERE username = :username";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 检查邮箱是否存在
     */
    public function emailExists($email) {
        $sql = "SELECT COUNT(*) FROM users WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 更新最后登录时间
     */
    private function updateLastLogin($user_id) {
        $sql = "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
    }
    
    /**
     * 获取用户地址
     */
    public function getUserAddresses($user_id) {
        $sql = "SELECT * FROM addresses WHERE user_id = :user_id ORDER BY is_default DESC, created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 添加用户地址
     */
    public function addAddress($user_id, $data) {
        // 如果设置为默认地址，先取消其他默认地址
        if ($data['is_default']) {
            $this->clearDefaultAddresses($user_id);
        }
        
        $sql = "INSERT INTO addresses (user_id, type, first_name, last_name, company, 
                address_line_1, address_line_2, city, state, postal_code, country, phone, is_default) 
                VALUES (:user_id, :type, :first_name, :last_name, :company, 
                :address_line_1, :address_line_2, :city, :state, :postal_code, :country, :phone, :is_default)";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':first_name', $data['first_name']);
            $stmt->bindParam(':last_name', $data['last_name']);
            $stmt->bindParam(':company', $data['company']);
            $stmt->bindParam(':address_line_1', $data['address_line_1']);
            $stmt->bindParam(':address_line_2', $data['address_line_2']);
            $stmt->bindParam(':city', $data['city']);
            $stmt->bindParam(':state', $data['state']);
            $stmt->bindParam(':postal_code', $data['postal_code']);
            $stmt->bindParam(':country', $data['country']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':is_default', $data['is_default'], PDO::PARAM_BOOL);
            
            if ($stmt->execute()) {
                return ['success' => true, 'address_id' => $this->db->lastInsertId(), 'message' => '地址添加成功'];
            } else {
                return ['success' => false, 'message' => '地址添加失败，请重试'];
            }
        } catch (PDOException $e) {
            logError("地址添加失败: " . $e->getMessage());
            return ['success' => false, 'message' => '地址添加失败，请重试'];
        }
    }
    
    /**
     * 更新用户地址
     */
    public function updateAddress($address_id, $user_id, $data) {
        // 验证地址是否属于该用户
        if (!$this->addressBelongsToUser($address_id, $user_id)) {
            return ['success' => false, 'message' => '无权限操作此地址'];
        }
        
        // 如果设置为默认地址，先取消其他默认地址
        if ($data['is_default']) {
            $this->clearDefaultAddresses($user_id);
        }
        
        $sql = "UPDATE addresses SET type = :type, first_name = :first_name, last_name = :last_name, 
                company = :company, address_line_1 = :address_line_1, address_line_2 = :address_line_2, 
                city = :city, state = :state, postal_code = :postal_code, country = :country, 
                phone = :phone, is_default = :is_default 
                WHERE id = :id AND user_id = :user_id";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $address_id, PDO::PARAM_INT);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':first_name', $data['first_name']);
            $stmt->bindParam(':last_name', $data['last_name']);
            $stmt->bindParam(':company', $data['company']);
            $stmt->bindParam(':address_line_1', $data['address_line_1']);
            $stmt->bindParam(':address_line_2', $data['address_line_2']);
            $stmt->bindParam(':city', $data['city']);
            $stmt->bindParam(':state', $data['state']);
            $stmt->bindParam(':postal_code', $data['postal_code']);
            $stmt->bindParam(':country', $data['country']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':is_default', $data['is_default'], PDO::PARAM_BOOL);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => '地址更新成功'];
            } else {
                return ['success' => false, 'message' => '地址更新失败，请重试'];
            }
        } catch (PDOException $e) {
            logError("地址更新失败: " . $e->getMessage());
            return ['success' => false, 'message' => '地址更新失败，请重试'];
        }
    }
    
    /**
     * 删除用户地址
     */
    public function deleteAddress($address_id, $user_id) {
        // 验证地址是否属于该用户
        if (!$this->addressBelongsToUser($address_id, $user_id)) {
            return ['success' => false, 'message' => '无权限操作此地址'];
        }
        
        $sql = "DELETE FROM addresses WHERE id = :id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $address_id, PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => '地址删除成功'];
        } else {
            return ['success' => false, 'message' => '地址删除失败，请重试'];
        }
    }
    
    /**
     * 清除用户的默认地址
     */
    private function clearDefaultAddresses($user_id) {
        $sql = "UPDATE addresses SET is_default = 0 WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
    }
    
    /**
     * 验证地址是否属于用户
     */
    private function addressBelongsToUser($address_id, $user_id) {
        $sql = "SELECT COUNT(*) FROM addresses WHERE id = :id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $address_id, PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }
}
?>
