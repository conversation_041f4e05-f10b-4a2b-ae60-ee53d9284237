/**
 * PHP页面布局样式 - COSPlay购物网站
 * 专门为php目录下的页面提供统一的布局样式
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 颜色主题 */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* 中性色 */
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* 边框 */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-width: 1px;
    --border-color: #dee2e6;

    /* 阴影 */
    --box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --box-shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.12);
    --box-shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.15);

    /* 过渡动画 */
    --transition: 0.3s ease;

    /* 布局 */
    --container-max-width: 1200px;
}

/* ===== 基础重置 ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Nunito', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-100);
}

/* ===== 导航栏样式 ===== */
.main-header {
    background: linear-gradient(135deg, var(--white) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
}

.main-nav {
    padding: var(--navbar-padding, 16px 0);
}

.main-nav .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
}

.nav-brand {
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.nav-brand:hover {
    transform: scale(1.05);
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), #6366f1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-accent {
    background: linear-gradient(135deg, #6366f1, var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-tagline {
    font-size: 0.8rem;
    color: var(--gray-600);
    font-weight: 500;
}

.nav-search {
    flex: 1;
    max-width: 400px;
}

.search-form {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 50px;
    border: 2px solid rgba(99, 102, 241, 0.2);
    border-radius: var(--border-radius-lg);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-50%) scale(1.05);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--gray-700);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
}

.nav-link:hover {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-link i {
    font-size: 1.1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-700);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

/* ===== 容器布局 ===== */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* ===== 主要内容区域 ===== */
#main-content {
    min-height: calc(100vh - 200px);
    /* 减去导航栏和footer的大概高度 */
    background-color: var(--gray-100);
    position: relative;
    z-index: 1;
    padding-bottom: var(--spacing-xxl);
}

/* 主要内容区域内的容器 */
#main-content .container {
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
}

/* 页面内容区域 */
.page-content {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin: var(--spacing-lg) 0;
    overflow: hidden;
}

/* 内容区域内边距 */
.content-wrapper {
    padding: var(--spacing-xl);
}

/* 主要内容区域的标题 */
#main-content h1,
#main-content h2,
#main-content h3 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
}

/* 主要内容区域的段落 */
#main-content p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

/* 主要内容区域的链接 */
#main-content a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

#main-content a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== 结算页面样式 ===== */
.checkout-page {
    padding: var(--spacing-xl) 0;
    min-height: 100vh;
}

.checkout-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.checkout-title {
    font-size: 2.5rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.checkout-steps {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-lg);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 100%;
    width: var(--spacing-xl);
    height: 2px;
    background: var(--gray-300);
    z-index: 1;
}

.step.completed::after,
.step.active::after {
    background: var(--primary-color);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-300);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.step.completed .step-number,
.step.active .step-number {
    background: var(--primary-color);
}

.step-text {
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 500;
}

.step.active .step-text {
    color: var(--primary-color);
}

/* ===== 结算表单布局 ===== */
.checkout-form {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xxl);
    align-items: start;
}

.checkout-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.checkout-section {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--border-color);
}

.section-title {
    font-size: 1.25rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

/* ===== 地址选择样式 ===== */
.address-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.address-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.address-item:hover {
    border-color: var(--primary-light);
    background: rgba(0, 123, 255, 0.02);
}

.address-item:has(input:checked) {
    border-color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
}

.address-item input[type="radio"] {
    margin: 0;
}

.address-content {
    flex: 1;
}

.address-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.recipient-name {
    font-weight: 600;
    color: var(--gray-900);
}

.recipient-phone {
    color: var(--gray-600);
}

.default-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.address-detail {
    color: var(--gray-700);
    line-height: 1.5;
}

.address-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.edit-address,
.add-address-btn {
    background: none;
    border: var(--border-width) solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
}

.edit-address:hover,
.add-address-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* ===== 商品清单样式 ===== */
.order-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.order-item {
    display: grid;
    grid-template-columns: 80px 1fr auto auto auto;
    gap: var(--spacing-md);
    align-items: center;
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--gray-50);
}

.item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-name {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.item-origin {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
}

.item-specs {
    display: flex;
    gap: var(--spacing-sm);
}

.spec {
    background: var(--gray-200);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    color: var(--gray-700);
}

.item-price,
.item-quantity,
.item-total {
    font-weight: 600;
    color: var(--gray-900);
    text-align: right;
}

.item-total {
    color: var(--primary-color);
}

/* ===== 配送和支付选项样式 ===== */
.shipping-options,
.payment-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.shipping-option,
.payment-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.shipping-option:hover,
.payment-option:hover {
    border-color: var(--primary-light);
    background: rgba(0, 123, 255, 0.02);
}

.shipping-option:has(input:checked),
.payment-option:has(input:checked) {
    border-color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
}

.shipping-info,
.payment-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.shipping-name,
.payment-name {
    font-weight: 600;
    color: var(--gray-900);
}

.shipping-time {
    color: var(--gray-600);
    font-size: 0.9rem;
}

.shipping-price {
    font-weight: 600;
    color: var(--primary-color);
}

.payment-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* ===== 优惠券样式 ===== */
.coupon-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.coupon-input {
    display: flex;
    gap: var(--spacing-md);
}

.coupon-code {
    flex: 1;
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.apply-coupon-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.apply-coupon-btn:hover {
    background: var(--primary-dark);
}

.coupon-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.coupon-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.coupon-item:hover {
    border-color: var(--primary-color);
}

.coupon-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.coupon-amount {
    background: var(--danger-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-weight: 600;
}

.coupon-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.coupon-name {
    font-weight: 600;
    color: var(--gray-900);
}

.coupon-condition {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* ===== 订单备注样式 ===== */
.note-textarea {
    width: 100%;
    min-height: 100px;
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
}

.note-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== 订单摘要样式 ===== */
.checkout-sidebar {
    position: sticky;
    top: var(--spacing-lg);
}

.order-summary {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
}

.summary-title {
    font-size: 1.25rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--border-color);
}

.summary-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-label {
    color: var(--gray-700);
}

.summary-value {
    font-weight: 600;
    color: var(--gray-900);
}

.summary-row.discount .summary-value {
    color: var(--success-color);
}

.summary-row.total {
    font-size: 1.1rem;
    padding-top: var(--spacing-md);
    border-top: var(--border-width) solid var(--border-color);
}

.summary-row.total .summary-label,
.summary-row.total .summary-value {
    font-weight: 700;
    color: var(--gray-900);
}

.summary-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-md) 0;
}

.checkout-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.back-to-cart-btn,
.submit-order-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
}

.back-to-cart-btn {
    background: var(--gray-200);
    color: var(--gray-700);
}

.back-to-cart-btn:hover {
    background: var(--gray-300);
    color: var(--gray-800);
}

.submit-order-btn {
    background: var(--primary-color);
    color: var(--white);
}

.submit-order-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.security-notice {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-600);
    font-size: 0.9rem;
    text-align: center;
    justify-content: center;
}

/* ===== 通用按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #c82333;
}

/* ===== 表单控件样式 ===== */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
select,
textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .checkout-form {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .checkout-sidebar {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .checkout-title {
        font-size: 2rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .checkout-steps {
        gap: var(--spacing-lg);
        flex-wrap: wrap;
    }

    .step:not(:last-child)::after {
        display: none;
    }

    .checkout-section {
        padding: var(--spacing-lg);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .address-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .order-item {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        text-align: center;
    }

    .item-image {
        justify-self: center;
    }

    .shipping-option,
    .payment-option {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .coupon-input {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .checkout-page {
        padding: var(--spacing-lg) 0;
    }

    .checkout-title {
        font-size: 1.75rem;
    }

    .checkout-steps {
        gap: var(--spacing-md);
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .step-text {
        font-size: 0.8rem;
    }

    .checkout-section {
        padding: var(--spacing-md);
    }

    .order-summary {
        padding: var(--spacing-lg);
    }
}

/* ===== 购物车页面样式 ===== */
.cart-page {
    padding: var(--spacing-xl) 0;
    min-height: 100vh;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--border-color);
}

.cart-title {
    font-size: 2rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
}

.cart-summary {
    color: var(--gray-600);
}

.cart-count strong {
    color: var(--primary-color);
}

.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xxl);
    align-items: start;
}

/* ===== 购物车商品列表 ===== */
.cart-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.cart-item {
    display: grid;
    grid-template-columns: 120px 1fr auto auto auto auto;
    gap: var(--spacing-lg);
    align-items: center;
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
    transition: var(--transition);
}

.cart-item:hover {
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-light);
}

.cart-item .item-image {
    width: 120px;
    height: 160px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.cart-item .item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item .item-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.cart-item .item-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.cart-item .item-origin {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
}

.cart-item .item-specs {
    display: flex;
    gap: var(--spacing-sm);
}

.cart-item .spec {
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    color: var(--gray-700);
}

.cart-item .item-price {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: right;
}

.current-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.original-price {
    color: var(--gray-500);
    text-decoration: line-through;
    font-size: 0.9rem;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
}

.qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: var(--transition);
}

.qty-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.qty-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: 600;
    color: var(--gray-900);
}

.item-total {
    text-align: right;
}

.total-price {
    font-weight: 700;
    color: var(--gray-900);
    font-size: 1.1rem;
}

.item-actions {
    display: flex;
    justify-content: center;
}

.remove-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--danger-color);
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* ===== 空购物车样式 ===== */
.empty-cart {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    text-align: center;
    box-shadow: var(--box-shadow);
}

.empty-cart-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.empty-cart i {
    font-size: 4rem;
    color: var(--gray-400);
}

.empty-cart h3 {
    color: var(--gray-700);
    margin: 0;
}

.empty-cart p {
    color: var(--gray-600);
    margin: 0;
}

/* ===== 购物车侧边栏 ===== */
.cart-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    position: sticky;
    top: var(--spacing-lg);
}

.cart-summary-card,
.coupon-card,
.recommended-products {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.continue-shopping-btn,
.checkout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
}

.continue-shopping-btn {
    background: var(--gray-200);
    color: var(--gray-700);
}

.continue-shopping-btn:hover {
    background: var(--gray-300);
}

.checkout-btn {
    background: var(--primary-color);
    color: var(--white);
}

.checkout-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.free-shipping {
    color: var(--success-color);
    font-weight: 600;
}

/* ===== 优惠券卡片 ===== */
.coupon-title,
.recommended-title {
    font-size: 1.1rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.coupon-input {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.coupon-code {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.apply-coupon-btn,
.use-coupon-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
}

.apply-coupon-btn:hover,
.use-coupon-btn:hover {
    background: var(--primary-dark);
}

.coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: var(--border-width) solid var(--border-color);
}

.coupon-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.coupon-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.9rem;
}

.coupon-desc {
    color: var(--gray-600);
    font-size: 0.8rem;
}

/* ===== 推荐商品 ===== */
.recommended-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.recommended-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: var(--border-width) solid var(--border-color);
    transition: var(--transition);
}

.recommended-item:hover {
    border-color: var(--primary-color);
    background: rgba(0, 123, 255, 0.02);
}

.recommended-item img {
    width: 60px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
}

.recommended-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.recommended-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.recommended-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.add-recommended-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.add-recommended-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* ===== 首页专用样式 ===== */
.homepage {
    background-color: var(--gray-100);
}

/* Hero区域样式 */
.hero {
    background: linear-gradient(135deg, #f5f7fa 0%, #e5e9f2 100%);
    padding: 100px 0;
    border-radius: 0 0 20px 20px;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    width: 100%;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(255, 64, 129, 0.3) 0%, rgba(123, 31, 162, 0.1) 100%);
    border-radius: 50%;
    filter: blur(40px);
    z-index: 0;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: var(--container-max-width);
    height: 100%;
}

.hero-content {
    flex: 0 0 55%;
    animation: fadeInUp 0.8s ease-out;
    padding: 30px 0;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 800;
    margin-bottom: 20px;
    color: var(--gray-900);
    line-height: 1.2;
    background: linear-gradient(90deg, var(--gray-900) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.4rem;
    color: var(--gray-600);
    margin-bottom: 40px;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 16px 38px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1.2rem;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 123, 255, 0.4);
    color: var(--white);
    text-decoration: none;
}

.cta-button:hover::before {
    left: 100%;
}

.hero-image {
    flex: 0 0 40%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: floatAnimation 3s ease-in-out infinite;
}

.hero-image i {
    font-size: 12rem;
    color: var(--primary-color);
    opacity: 0.8;
    filter: drop-shadow(0 10px 20px rgba(0, 123, 255, 0.4));
}

/* 分类导航样式 */
.category-nav {
    background-color: var(--white);
    border-bottom: var(--border-width) solid var(--border-color);
    padding: var(--spacing-lg) 0;
    box-shadow: var(--box-shadow-sm);
    margin-top: -30px;
    margin-bottom: 30px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-top: none;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    position: relative;
    z-index: 20;
}

.category-list {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    min-width: 100px;
}

.category-item:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
    transform: translateY(-2px);
    text-decoration: none;
}

.category-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
}

.category-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

/* 商品网格样式 */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.product-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
    border: var(--border-width) solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    text-decoration: none;
    color: inherit;
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.sale {
    background: var(--danger-color);
    left: auto;
    right: var(--spacing-md);
}

.product-badge.new {
    background: var(--success-color);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.product-origin {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
}

.product-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.product-price-original {
    color: var(--gray-500);
    text-decoration: line-through;
    font-size: 1rem;
    margin-left: var(--spacing-sm);
}

.add-to-cart {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.add-to-cart:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* 信息区域样式 */
.info-section {
    padding: 60px 0;
}

.info-section:nth-child(even) {
    background: var(--gray-100);
}

.info-section h2 {
    font-size: 2rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.info-section h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.info-section p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
}

.info-section ul {
    list-style: none;
    padding: 0;
}

.info-section ul li {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xs);
}

.info-section ul li a {
    color: var(--gray-600);
    text-decoration: none;
    transition: var(--transition);
}

.info-section ul li a:hover {
    color: var(--primary-color);
}

/* 表格样式 */
.info-section table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow-sm);
}

.info-section table th {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
}

.info-section table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.info-section table tr:last-child td {
    border-bottom: none;
}

.info-section table tr:nth-child(even) {
    background: var(--gray-50);
}

/* 支付方式卡片 */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.payment-method {
    text-align: center;
    padding: var(--spacing-xl);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--white);
    transition: var(--transition);
}

.payment-method:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.payment-method i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.payment-method h4 {
    margin-bottom: var(--spacing-sm);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatAnimation {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-15px);
    }
}

/* ===== 首页响应式样式 ===== */
@media (max-width: 1024px) {
    .hero {
        padding: 80px 0;
        min-height: 450px;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .hero-image i {
        font-size: 10rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 60px 0;
        min-height: 400px;
    }

    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        flex: 0 0 100%;
        margin-bottom: 40px;
        padding: 20px 0;
    }

    .hero-image {
        flex: 0 0 100%;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 30px;
    }

    .hero-image i {
        font-size: 8rem;
    }

    .category-nav {
        margin-top: 0;
        padding-top: 15px;
    }

    .category-list {
        gap: var(--spacing-lg);
    }

    .category-item {
        min-width: 80px;
        padding: var(--spacing-sm);
    }

    .category-icon {
        font-size: 1.5rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .section {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .section-title {
        font-size: 1.75rem;
    }

    .info-section {
        padding: 40px 0;
    }

    .info-section h2 {
        font-size: 1.75rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 40px 0;
        min-height: 350px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 25px;
    }

    .hero-image i {
        font-size: 6rem;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 1.1rem;
    }

    .category-list {
        gap: var(--spacing-md);
    }

    .category-item {
        min-width: 70px;
        padding: var(--spacing-xs);
    }

    .category-icon {
        font-size: 1.3rem;
    }

    .category-name {
        font-size: 0.8rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: var(--spacing-sm);
    }

    .product-image-container {
        height: 200px;
    }

    .product-info {
        padding: var(--spacing-md);
    }

    .product-name {
        font-size: 1rem;
    }

    .product-price {
        font-size: 1.1rem;
    }

    .add-to-cart {
        width: 36px;
        height: 36px;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .info-section {
        padding: 30px 0;
    }

    .info-section h2 {
        font-size: 1.5rem;
    }

    .info-section h4 {
        font-size: 1.1rem;
    }
}

/* ===== 关于页面样式 ===== */
.about-page {
    background-color: var(--gray-100);
}

/* 关于页面Hero区域 */
.about-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 400px;
    height: 400px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(60px);
}

.about-hero .container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 0 0 60%;
}

.about-hero .hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.about-hero .hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-decoration {
    flex: 0 0 35%;
    position: relative;
    height: 300px;
}

.floating-icon {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-icon i {
    font-size: 2rem;
    color: var(--white);
}

.floating-icon:nth-child(1) {
    top: 20%;
    left: 20%;
    animation: float1 3s ease-in-out infinite;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 30%;
    animation: float2 4s ease-in-out infinite;
}

.floating-icon:nth-child(3) {
    bottom: 20%;
    left: 50%;
    animation: float3 3.5s ease-in-out infinite;
}

/* 公司介绍区域 */
.company-intro {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.intro-text .section-title {
    font-size: 2.5rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
}

.intro-paragraph {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: var(--spacing-lg);
}

.intro-image {
    position: relative;
}

.intro-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

/* 核心价值区域 */
.core-values {
    padding: var(--spacing-xxl) 0;
    background: var(--gray-100);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.value-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.value-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.value-icon i {
    font-size: 2rem;
    color: var(--white);
}

.value-title {
    font-size: 1.3rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.value-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 团队介绍区域 */
.team-section {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-xxl);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.team-member {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: var(--border-width) solid var(--border-color);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.member-avatar {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-name {
    font-size: 1.3rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.member-position {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.member-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 发展历程区域 */
.timeline-section {
    padding: var(--spacing-xxl) 0;
    background: var(--gray-100);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: var(--spacing-xxl) auto 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xxl);
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-date {
    flex: 0 0 120px;
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.timeline-content {
    flex: 1;
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin: 0 var(--spacing-xl);
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border: 10px solid transparent;
    border-right-color: var(--white);
}

.timeline-item:nth-child(even) .timeline-content::before {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    border: 10px solid transparent;
    border-left-color: var(--white);
}

.timeline-title {
    font-size: 1.3rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.timeline-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 服务承诺区域 */
.service-promise {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.promise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.promise-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    border: var(--border-width) solid var(--border-color);
}

.promise-item:hover {
    background: var(--white);
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
    border-color: var(--primary-color);
}

.promise-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--success-color), #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.promise-icon i {
    font-size: 2rem;
    color: var(--white);
}

.promise-title {
    font-size: 1.3rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.promise-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 联系我们区域 */
.contact-section {
    padding: var(--spacing-xxl) 0;
    background: var(--gray-100);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: var(--border-width) solid var(--border-color);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--info-color), #138496);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.contact-details h3 {
    font-size: 1.2rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.contact-details p {
    color: var(--gray-600);
    font-size: 1.1rem;
    margin: 0;
}

/* 关于页面动画效果 */
@keyframes float1 {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes float2 {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    50% {
        transform: translateY(-15px) rotate(-180deg);
    }
}

@keyframes float3 {

    0%,
    100% {
        transform: translateY(0) rotate(0deg);
    }

    50% {
        transform: translateY(-25px) rotate(90deg);
    }
}

/* 关于页面响应式样式 */
@media (max-width: 1024px) {
    .about-hero .hero-title {
        font-size: 2.5rem;
    }

    .intro-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .intro-text .section-title {
        font-size: 2rem;
    }

    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .team-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .about-hero {
        padding: 60px 0;
    }

    .about-hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        flex: 0 0 100%;
        margin-bottom: var(--spacing-xl);
    }

    .hero-decoration {
        flex: 0 0 100%;
        height: 200px;
    }

    .about-hero .hero-title {
        font-size: 2rem;
    }

    .about-hero .hero-subtitle {
        font-size: 1.1rem;
    }

    .floating-icon {
        width: 60px;
        height: 60px;
    }

    .floating-icon i {
        font-size: 1.5rem;
    }

    .intro-text .section-title {
        font-size: 1.75rem;
    }

    .intro-paragraph {
        font-size: 1rem;
    }

    .intro-image img {
        height: 300px;
    }

    .timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: column !important;
        align-items: flex-start;
        padding-left: 60px;
    }

    .timeline-date {
        position: absolute;
        left: 0;
        top: 0;
        flex: none;
        width: 60px;
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }

    .timeline-content {
        margin: 0;
        margin-top: var(--spacing-md);
    }

    .timeline-content::before {
        display: none;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .contact-icon {
        width: 50px;
        height: 50px;
    }

    .contact-icon i {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .about-hero .hero-title {
        font-size: 1.75rem;
    }

    .about-hero .hero-subtitle {
        font-size: 1rem;
    }

    .hero-decoration {
        height: 150px;
    }

    .floating-icon {
        width: 50px;
        height: 50px;
    }

    .floating-icon i {
        font-size: 1.2rem;
    }

    .intro-text .section-title {
        font-size: 1.5rem;
    }

    .values-grid,
    .team-grid,
    .promise-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .value-item,
    .team-member,
    .promise-item {
        padding: var(--spacing-lg);
    }

    .value-icon,
    .promise-icon {
        width: 60px;
        height: 60px;
    }

    .value-icon i,
    .promise-icon i {
        font-size: 1.5rem;
    }

    .member-avatar {
        width: 100px;
        height: 100px;
    }

    .timeline-date {
        width: 50px;
        font-size: 0.8rem;
    }

    .timeline-item {
        padding-left: 50px;
    }
}

/* ===== 地址管理页面样式 ===== */
.address-page {
    background-color: var(--gray-100);
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xxl);
    padding-bottom: var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--border-color);
}

.address-title {
    font-size: 2rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
}

.add-address-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition);
}

.add-address-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* 地址列表样式 */
.address-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.address-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
    transition: var(--transition);
}

.address-card:hover {
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-light);
}

.address-header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.address-recipient {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.recipient-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-900);
}

.recipient-phone {
    color: var(--gray-600);
    font-size: 1rem;
}

.default-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.address-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn {
    background: none;
    border: var(--border-width) solid var(--gray-300);
    color: var(--gray-600);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    transition: var(--transition);
}

.action-btn.edit-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
}

.action-btn.delete-btn:hover {
    border-color: var(--danger-color);
    color: var(--danger-color);
    background: rgba(220, 53, 69, 0.05);
}

/* 地址内容样式 */
.address-content {
    margin-bottom: var(--spacing-lg);
}

.address-detail {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.address-detail i {
    color: var(--primary-color);
    margin-top: 2px;
    flex-shrink: 0;
}

.address-text {
    color: var(--gray-700);
    line-height: 1.6;
    font-size: 1rem;
}

.address-postcode {
    color: var(--gray-500);
    font-size: 0.9rem;
    margin-left: var(--spacing-lg);
}

/* 地址底部样式 */
.address-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.address-tags {
    display: flex;
    gap: var(--spacing-sm);
}

.address-tag {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.address-tag.home {
    background: var(--success-color);
    color: var(--white);
}

.address-tag.company {
    background: var(--info-color);
    color: var(--white);
}

.address-tag.school {
    background: var(--warning-color);
    color: var(--gray-800);
}

.address-operations {
    display: flex;
    gap: var(--spacing-sm);
}

.operation-btn {
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.operation-btn:hover:not(:disabled) {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
}

.operation-btn:disabled {
    color: var(--primary-color);
    cursor: not-allowed;
}

.operation-btn:disabled i {
    color: var(--primary-color);
}

/* 地址限制提示 */
.address-limit-notice {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--info-color);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

/* 地址表单模态框样式 */
.address-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-label {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.9rem;
}

.required {
    color: var(--danger-color);
}

.form-input,
.form-textarea,
.region-select {
    padding: var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.region-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
}

/* 地区选择器样式 */
.region-selector {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-md);
}

.region-select {
    background: var(--white);
    cursor: pointer;
}

/* 标签选择器样式 */
.tag-selector {
    display: flex;
    gap: var(--spacing-md);
}

.tag-option {
    cursor: pointer;
}

.tag-option input[type="radio"] {
    display: none;
}

.tag-label {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
}

.tag-option input[type="radio"]:checked+.tag-label {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.tag-label.home {
    color: var(--success-color);
}

.tag-label.company {
    color: var(--info-color);
}

.tag-label.school {
    color: var(--warning-color);
}

.tag-option input[type="radio"]:checked+.tag-label.home,
.tag-option input[type="radio"]:checked+.tag-label.company,
.tag-option input[type="radio"]:checked+.tag-label.school {
    color: var(--white);
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
}

.checkmark {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    border: var(--border-width) solid var(--border-color);
    border-radius: 3px;
    transition: var(--transition);
}

.checkbox:checked+.checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: var(--border-width) solid var(--border-color);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

/* 地址管理页面响应式样式 */
@media (max-width: 768px) {
    .address-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .address-title {
        font-size: 1.75rem;
    }

    .address-header-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .address-recipient {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .address-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .address-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .region-selector {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .tag-selector {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .form-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .btn-secondary,
    .btn-primary {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .address-page {
        padding: var(--spacing-lg) 0;
    }

    .address-title {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .add-address-btn {
        width: 100%;
        justify-content: center;
    }

    .address-card {
        padding: var(--spacing-lg);
    }

    .recipient-name {
        font-size: 1.1rem;
    }

    .action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .address-text {
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
}

/* ===== 导航栏现代化优化样式 ===== */
/* 重置和基础样式 */
.navbar {
    background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
    border-bottom: 1px solid rgba(0, 123, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: var(--navbar-height, 80px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: var(--navbar-padding, 16px 0);
    gap: var(--spacing-lg);
    position: relative;
}

/* Logo现代化样式 */
.logo {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), #6c5ce7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
}

.logo::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -8px;
    right: -8px;
    bottom: -4px;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(108, 92, 231, 0.1));
    border-radius: 12px;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.logo:hover {
    transform: translateY(-2px) scale(1.05);
    text-decoration: none;
    filter: drop-shadow(0 4px 12px rgba(0, 123, 255, 0.3));
}

.logo:hover::before {
    opacity: 1;
}

.logo span {
    background: linear-gradient(135deg, var(--danger-color), #e84393);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* 搜索框增强样式 */
.search-container {
    position: relative;
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-lg);
}

.search-container form {
    display: flex;
    position: relative;
    width: 100%;
}

.search-input {
    flex: 1;
    padding: 12px 50px 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--gray-50);
}

.search-input:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    background: var(--white);
}

.search-input::placeholder {
    color: var(--gray-500);
}

.search-btn {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-50%) scale(1.1);
}

/* 搜索结果下拉 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.search-results.show {
    display: block;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--gray-100);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.search-result-info {
    flex: 1;
}

.search-result-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.search-result-price {
    color: var(--primary-color);
    font-weight: 600;
}

/* 导航链接增强样式 */
.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid transparent;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(108, 92, 231, 0.1));
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-link:hover {
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    text-decoration: none;
    border-color: rgba(0, 123, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link.active {
    color: var(--white);
    background: linear-gradient(135deg, var(--primary-color), #6c5ce7);
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    border-color: transparent;
}

.nav-link.active::before {
    opacity: 0;
}

.nav-link-icon {
    font-size: 1.2rem;
    transition: var(--transition);
}

.nav-link:hover .nav-link-icon {
    transform: scale(1.1);
}

.nav-link-text {
    font-weight: inherit;
    white-space: nowrap;
}

/* 购物车数量徽章 */
.nav-link-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--danger-color), #e74c3c);
    color: var(--white);
    font-size: 11px;
    font-weight: 700;
    padding: 3px 7px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    animation: pulse 2s infinite;
}

/* 用户下拉菜单增强样式 */
.user-dropdown {
    position: relative;
    display: inline-block;
}

.user-dropdown-toggle {
    background: none;
    border: none;
    color: var(--gray-700);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    font-size: 1rem;
    font-weight: 500;
}

.user-dropdown-toggle:hover {
    background: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.user-dropdown.active .user-dropdown-toggle {
    background: rgba(0, 123, 255, 0.15);
    color: var(--primary-color);
}

/* 下拉箭头动画 */
.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 0.9rem;
}

.user-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 用户头像 */
.nav-user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
    transition: var(--transition);
}

.user-dropdown-toggle:hover .nav-user-avatar {
    transform: scale(1.1);
    border-color: var(--primary-dark);
}

/* 用户下拉菜单 */
.user-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xl);
    min-width: 220px;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.user-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--white);
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* 菜单项 */
.user-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    transition: var(--transition);
    border-bottom: var(--border-width) solid var(--gray-100);
}

.user-menu-item:last-child {
    border-bottom: none;
}

.user-menu-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
    text-decoration: none;
    padding-left: calc(var(--spacing-lg) + 4px);
}

.user-menu-item i {
    width: 18px;
    text-align: center;
    color: var(--gray-500);
    transition: var(--transition);
}

.user-menu-item:hover i {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* 分割线 */
.user-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-sm) 0;
}

/* 移动端菜单按钮 */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.menu-toggle:hover {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.1);
    transform: scale(1.1);
}

.menu-toggle.active {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.15);
}

/* 移动端导航菜单 */
.mobile-nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--white);
    border-bottom: var(--border-width) solid var(--border-color);
    box-shadow: var(--box-shadow-lg);
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-nav.show {
    transform: translateY(0);
}

.mobile-nav-content {
    padding: var(--spacing-lg);
}

.mobile-nav-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.mobile-nav-link:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    text-decoration: none;
}

.mobile-nav-link.active {
    background: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

/* 导航栏响应式样式 */
@media (max-width: 1024px) {
    .search-container {
        max-width: 400px;
        margin: 0 var(--spacing-md);
    }

    .nav-links {
        gap: var(--spacing-sm);
    }

    .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .logo {
        font-size: 1.6rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        height: 70px;
    }

    .navbar-container {
        padding: 12px 0;
        gap: var(--spacing-md);
    }

    .logo {
        font-size: 1.4rem;
    }

    .search-container {
        max-width: 250px;
        margin: 0 var(--spacing-sm);
    }

    .search-input {
        padding: 10px 45px 10px 14px;
        font-size: 0.9rem;
    }

    .search-btn {
        width: 36px;
        height: 36px;
        padding: 8px;
    }

    .nav-links {
        gap: var(--spacing-xs);
    }

    .nav-link-text {
        display: none;
    }

    .nav-link {
        padding: var(--spacing-xs);
        min-width: 44px;
        justify-content: center;
    }

    .nav-link-icon {
        font-size: 1.1rem;
    }

    .menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 44px;
        min-height: 44px;
    }

    .user-dropdown-toggle .nav-link-text {
        display: none;
    }

    .user-menu {
        position: fixed;
        top: 60px;
        right: var(--spacing-md);
        left: var(--spacing-md);
        min-width: auto;
    }

    .user-menu::before {
        right: 30px;
    }
}

@media (max-width: 480px) {
    .navbar {
        height: 60px;
    }

    .navbar-container {
        padding: 10px 0;
        gap: var(--spacing-sm);
    }

    .logo {
        font-size: 1.2rem;
    }

    .search-container {
        max-width: 180px;
        margin: 0;
    }

    .search-input {
        padding: 8px 40px 8px 12px;
        font-size: 0.85rem;
    }

    .search-btn {
        width: 32px;
        height: 32px;
        padding: 6px;
    }

    .nav-link {
        padding: 6px;
        min-width: 40px;
    }

    .nav-link-icon {
        font-size: 1rem;
    }

    .menu-toggle {
        min-width: 40px;
        min-height: 40px;
        font-size: 1.3rem;
    }

    .user-menu {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
    }

    .mobile-nav {
        top: 60px;
    }

    .mobile-nav-content {
        padding: var(--spacing-md);
    }

    .mobile-nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }
}

/* 导航栏滚动效果 */
.navbar.scrolled {
    box-shadow: var(--box-shadow-lg);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* 搜索框焦点状态增强 */
.search-container.focused .search-input {
    border-radius: 25px 25px 0 0;
}

.search-container.focused .search-results {
    display: block;
}

/* 无障碍增强 */
.nav-link:focus,
.user-dropdown-toggle:focus,
.menu-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 键盘导航支持 */
.nav-link:focus-visible,
.user-dropdown-toggle:focus-visible,
.menu-toggle:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.1);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .navbar {
        border-bottom-width: 2px;
    }

    .nav-link:hover,
    .user-dropdown-toggle:hover {
        background: var(--gray-200);
    }

    .nav-link.active {
        background: var(--primary-color);
        color: var(--white);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {

    .navbar,
    .nav-link,
    .user-dropdown-toggle,
    .dropdown-arrow,
    .user-menu,
    .mobile-nav,
    .search-btn {
        transition: none;
    }

    .nav-link:hover,
    .user-dropdown-toggle:hover {
        transform: none;
    }

    .nav-link-count {
        animation: none;
    }
}

/* ===== 服务政策底部栏样式 ===== */
.service-policies-section {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--white) 100%);
    padding: var(--spacing-xxl) 0;
    border-top: var(--border-width) solid var(--border-color);
    position: relative;
}

.service-policies-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--info-color), var(--warning-color));
}

/* 政策头部 */
.policies-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.policies-title {
    font-size: 2.5rem;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.policies-title i {
    color: var(--primary-color);
    font-size: 2.2rem;
}

.policies-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: 500;
}

/* 政策网格 */
.policies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

/* 政策卡片 */
.policy-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.policy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.policy-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-light);
}

.policy-card:hover::before {
    transform: scaleX(1);
}

/* 政策头部 */
.policy-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--border-color);
}

.policy-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.policy-header h3 {
    font-size: 1.4rem;
    color: var(--gray-900);
    margin: 0;
    font-weight: 600;
}

/* 政策内容 */
.policy-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.policy-item {
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.policy-item:hover {
    background: var(--white);
    border-left-color: var(--primary-dark);
    transform: translateX(4px);
}

.policy-item h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.1rem;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.policy-item h4 i {
    color: var(--primary-color);
    font-size: 1rem;
}

.policy-item p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
    font-size: 0.95rem;
}

/* 网站地图特殊样式 */
.sitemap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.sitemap-column h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1rem;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.sitemap-column h4 i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.sitemap-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.sitemap-links li a {
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.9rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: block;
}

.sitemap-links li a:hover {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.1);
    text-decoration: none;
    transform: translateX(4px);
}

/* 快速链接栏 */
.quick-links-bar {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
}

.quick-links-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.quick-link-group h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.1rem;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.quick-link-group h4 i {
    color: var(--primary-color);
    font-size: 1rem;
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.quick-link {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--gray-100);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    border: var(--border-width) solid transparent;
}

.quick-link:hover {
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 安全徽章 */
.security-badges {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.security-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: var(--white);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
}

.security-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.security-badge i {
    font-size: 1rem;
}

/* 服务政策底部栏响应式样式 */
@media (max-width: 1024px) {
    .policies-title {
        font-size: 2.2rem;
    }

    .policies-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .quick-links-content {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .service-policies-section {
        padding: var(--spacing-xl) 0;
    }

    .policies-title {
        font-size: 2rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .policies-title i {
        font-size: 2rem;
    }

    .policies-subtitle {
        font-size: 1.1rem;
    }

    .policies-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .policy-card {
        padding: var(--spacing-lg);
    }

    .policy-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .policy-icon {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }

    .sitemap-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .quick-links-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .quick-links {
        justify-content: center;
    }

    .security-badges {
        align-items: center;
    }
}

@media (max-width: 480px) {
    .policies-title {
        font-size: 1.75rem;
    }

    .policies-subtitle {
        font-size: 1rem;
    }

    .policy-card {
        padding: var(--spacing-md);
    }

    .policy-header h3 {
        font-size: 1.2rem;
    }

    .policy-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .policy-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .policy-item h4 {
        font-size: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .quick-links-bar {
        padding: var(--spacing-lg);
    }

    .quick-link-group h4 {
        font-size: 1rem;
        text-align: center;
    }

    .quick-links {
        flex-direction: column;
        align-items: center;
    }

    .quick-link {
        width: 100%;
        text-align: center;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .security-badge {
        justify-content: center;
        padding: var(--spacing-sm);
        font-size: 0.85rem;
    }
}

/* ===== 信息展示区域优化样式 ===== */
.info-showcase-section {
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    padding: var(--spacing-xxl) 0;
    position: relative;
}

.info-showcase-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--info-color));
}

.info-showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.info-showcase-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: var(--border-width) solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.info-showcase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.info-showcase-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-xl);
    border-color: var(--primary-light);
}

.info-showcase-card:hover::before {
    transform: scaleX(1);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: var(--border-width) solid var(--border-color);
}

.info-card-header i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.info-card-header h3 {
    font-size: 1.4rem;
    color: var(--gray-900);
    margin: 0;
    font-weight: 600;
}

.info-card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 联系我们卡片样式 */
.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-item:hover {
    background: var(--white);
    transform: translateX(4px);
    box-shadow: var(--box-shadow-sm);
}

.contact-item i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--info-color), #17a2b8);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-item div {
    flex: 1;
}

.contact-item strong {
    display: block;
    color: var(--gray-800);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
}

.contact-item p {
    color: var(--gray-600);
    margin: var(--spacing-xs) 0;
    font-size: 1rem;
}

.contact-item small {
    color: var(--gray-500);
    font-size: 0.9rem;
}

/* 帮助中心卡片样式 */
.help-category {
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--success-color);
    transition: var(--transition);
}

.help-category:hover {
    background: var(--white);
    border-left-color: var(--success-dark);
    transform: translateX(4px);
}

.help-category h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.1rem;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.help-category h4 i {
    color: var(--success-color);
    font-size: 1rem;
}

.help-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.help-category li {
    color: var(--gray-600);
    font-size: 0.95rem;
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.help-category li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* ===== Footer Main 样式增强 ===== */
.footer {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--info-color), var(--warning-color));
}

.footer-main {
    padding: var(--spacing-xxl) 0;
    position: relative;
}

.footer-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
    align-items: flex-start;
}

/* Footer Brand Column */
.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.footer-logo {
    margin-bottom: var(--spacing-md);
}

.logo-text {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
}

.logo-accent {
    color: var(--primary-color);
}

.logo-tagline {
    font-size: 1rem;
    color: var(--gray-300);
    margin-top: var(--spacing-xs);
    font-weight: 500;
}

.footer-description {
    color: var(--gray-300);
    line-height: 1.6;
    font-size: 1rem;
    margin: 0;
}

/* Social Links */
.footer-social {
    display: flex;
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.footer-social-link {
    position: relative;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    text-decoration: none;
}

.footer-social-link[data-platform="weibo"]:hover {
    background: linear-gradient(135deg, #e6162d, #ff4757);
    box-shadow: 0 8px 25px rgba(230, 22, 45, 0.4);
}

.footer-social-link[data-platform="wechat"]:hover {
    background: linear-gradient(135deg, #07c160, #2ed573);
    box-shadow: 0 8px 25px rgba(7, 193, 96, 0.4);
}

.footer-social-link[data-platform="qq"]:hover {
    background: linear-gradient(135deg, #12b7f5, #3742fa);
    box-shadow: 0 8px 25px rgba(18, 183, 245, 0.4);
}

.footer-social-link[data-platform="bilibili"]:hover {
    background: linear-gradient(135deg, #fb7299, #ff6b9d);
    box-shadow: 0 8px 25px rgba(251, 114, 153, 0.4);
}

.footer-social-link[data-platform="tiktok"]:hover {
    background: linear-gradient(135deg, #000000, #333333);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.footer-social-link i {
    font-size: 1.5rem;
    transition: var(--transition);
}

.footer-social-link:hover i {
    transform: scale(1.2);
}

.social-tooltip {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 10;
}

.footer-social-link:hover .social-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* App Downloads */
.footer-apps {
    margin-top: var(--spacing-lg);
}

.apps-title {
    color: var(--white);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.app-downloads {
    display: flex;
    gap: var(--spacing-md);
}

.app-download-link {
    display: block;
    transition: var(--transition);
}

.app-download-link:hover {
    transform: translateY(-3px);
}

.app-badge {
    height: 40px;
    width: auto;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.app-badge:hover {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Footer Columns */
.footer-column {
    display: flex;
    flex-direction: column;
}

.footer-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--white);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--white);
}

.footer-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-link a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-300);
    text-decoration: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.footer-link a:hover {
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    text-decoration: none;
}

.footer-link a i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
    transition: var(--transition);
}

.footer-link a:hover i {
    color: var(--white);
    transform: scale(1.2);
}

/* Footer Contact */
.footer-contact {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.footer-contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
    transition: var(--transition);
}

.footer-contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.footer-contact-icon {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.contact-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.contact-content strong {
    color: var(--white);
    font-size: 1rem;
    font-weight: 600;
}

.contact-content span {
    color: var(--gray-300);
    font-size: 0.9rem;
}

/* QR Codes */
.footer-qr {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.qr-title {
    color: var(--white);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.qr-codes {
    display: flex;
    gap: var(--spacing-lg);
}

.qr-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.qr-placeholder {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--primary-color);
    transition: var(--transition);
}

.qr-placeholder:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.qr-placeholder i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.qr-item span {
    color: var(--gray-300);
    font-size: 0.8rem;
    text-align: center;
}

/* Footer Bottom */
.footer-bottom {
    background: linear-gradient(135deg, var(--gray-900) 0%, #1a1a2e 100%);
    padding: var(--spacing-xl) 0;
    border-top: 2px solid rgba(99, 102, 241, 0.3);
    margin-top: var(--spacing-xl);
    position: relative;
}

.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.footer-bottom-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.footer-copyright {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.copyright-text {
    color: var(--gray-300);
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.5;
}

.footer-copyright p:first-child {
    font-weight: 600;
    color: var(--white);
}

.copyright-text a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.copyright-text a:hover {
    color: var(--white);
    text-decoration: underline;
}

.company-info {
    color: var(--gray-500);
    font-size: 0.8rem;
    margin: 0;
}

/* Footer Bottom Links */
.footer-bottom-links {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: flex-end;
}

.bottom-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-400);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.bottom-link:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.bottom-link i {
    font-size: 0.8rem;
}

/* 删除了客服系统和返回顶部按钮样式，保持页面简洁 */

/* Footer 响应式样式 */
@media (max-width: 1024px) {
    .footer-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .footer-social {
        gap: var(--spacing-sm);
    }

    .footer-social-link {
        width: 45px;
        height: 45px;
    }

    .footer-social-link i {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .footer-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .footer-social {
        justify-content: center;
    }

    .app-downloads {
        justify-content: center;
    }

    .footer-bottom-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .footer-certifications {
        justify-content: center;
        flex-wrap: wrap;
    }

    .qr-codes {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .footer-main {
        padding: var(--spacing-xl) 0;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .logo-text {
        font-size: 1.75rem;
    }

    .footer-title {
        font-size: 1.2rem;
    }

    .footer-contact {
        gap: var(--spacing-md);
    }

    .footer-contact-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .footer-social {
        gap: var(--spacing-sm);
    }

    .footer-social-link {
        width: 40px;
        height: 40px;
    }

    .footer-social-link i {
        font-size: 1.2rem;
    }

    .app-downloads {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .footer-bottom {
        padding: var(--spacing-md) 0;
    }

    .footer-bottom-links {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .bottom-link {
        justify-content: center;
    }
}

/* ===== Category Page 增强样式 ===== */

/* 分类内容布局 */
.category-content {
    display: block;
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
    min-height: calc(100vh - 400px);
}

/* 侧边栏布局 - 现在使用固定定位 */

/* 商品区域布局 */
.category-products {
    margin-left: 320px;
    width: calc(100% - 320px);
    min-height: calc(100vh - 200px);
}

/* 商品工具栏 */
.products-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.toolbar-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.products-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.products-count {
    color: var(--primary-color);
    font-weight: 600;
}

.products-filter-tags {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.view-switcher {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 2px;
}

.view-btn {
    padding: var(--spacing-sm);
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.view-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.sort-dropdown {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-label {
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 500;
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .category-sidebar {
        width: 240px;
        left: 15px;
    }

    .category-products {
        margin-left: 270px;
        width: calc(100% - 270px);
    }

    /* 商品网格响应式 */
    .product-grid-view {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .category-sidebar {
        position: static;
        width: 100%;
        height: auto;
        margin-bottom: var(--spacing-lg);
        left: auto;
        top: auto;
    }

    .category-products {
        margin-left: 0;
        width: 100%;
    }

    .category-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .category-sidebar.collapsed {
        display: none;
    }

    /* 商品网格响应式 */
    .product-grid-view {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .category-sidebar {
        padding: var(--spacing-lg);
    }

    /* 商品网格响应式 */
    .product-grid-view {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* Category Banner 区域美化 */
.category-banner {
    background: linear-gradient(135deg,
            rgba(99, 102, 241, 0.1) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(236, 72, 153, 0.1) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xxl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.category-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(99,102,241,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.banner-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.banner-title {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.banner-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    font-weight: 500;
}

.banner-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.banner-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-icon {
    position: absolute;
    color: rgba(99, 102, 241, 0.2);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-icon:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    min-width: 120px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 600;
}

/* 快速分类美化 */
.quick-categories {
    margin: var(--spacing-xl) 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.quick-category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.quick-category-item {
    background: linear-gradient(135deg, var(--white) 0%, rgba(248, 250, 252, 0.8) 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.quick-category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-category-item:hover::before {
    transform: scaleX(1);
}

.quick-category-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15);
    border-color: var(--primary-color);
}

.quick-category-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
    display: block;
}

.quick-category-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.quick-category-count {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
    background: rgba(99, 102, 241, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    display: inline-block;
}

/* 分类页面控制栏 */
.category-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.view-toggle {
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius);
    border: var(--border-width) solid var(--border-color);
    overflow: hidden;
}

.view-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
}

.view-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.view-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.view-btn i {
    font-size: 1rem;
}

/* 商品容器 */
.products-container {
    margin-top: var(--spacing-lg);
}

/* 商品网格美化 */
.product-grid-view {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
}

/* 新的商品卡片样式 */
.product-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(99, 102, 241, 0.1);
    transition: var(--transition);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(99, 102, 241, 0.2);
}

.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    z-index: 10;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-badge.new {
    background: linear-gradient(135deg, #10b981, #059669);
}

.product-badge.hot {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.product-badge.sale {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.product-image-wrapper {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--gray-50);
}

.product-image-link {
    display: block;
    width: 100%;
    height: 100%;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-quick-actions {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    opacity: 0;
    transform: translateX(10px);
    transition: var(--transition);
}

.product-card:hover .product-quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.product-content {
    padding: var(--spacing-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.product-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-sm);
}

.product-category {
    font-size: 0.8rem;
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.rating-stars i {
    font-size: 0.8rem;
    color: #fbbf24;
}

.rating-count {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.product-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.product-title a {
    text-decoration: none;
    color: var(--gray-800);
    transition: var(--transition);
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-price-section {
    margin-top: auto;
    padding-top: var(--spacing-sm);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.product-actions {
    margin-top: var(--spacing-sm);
}

.add-to-cart-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), #6366f1);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.add-to-cart-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), #5855eb);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.product-card {
    background: linear-gradient(135deg, var(--white) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(99, 102, 241, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 24px 48px rgba(99, 102, 241, 0.2);
    border-color: var(--primary-color);
}

.product-img-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(168, 85, 247, 0.05));
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--white);
    z-index: 2;
}

.product-badge.new {
    background: linear-gradient(135deg, var(--success-color), #10b981);
}

.product-badge.hot {
    background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.product-badge.sale {
    background: linear-gradient(135deg, var(--danger-color), #ef4444);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-origin {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.product-stars {
    display: flex;
    gap: 2px;
}

.product-star {
    color: #fbbf24;
    font-size: 0.9rem;
}

.product-rating-count {
    font-size: 0.8rem;
    color: var(--gray-500);
    font-weight: 500;
}

.product-price {
    font-size: 1.25rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.product-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.product-btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.product-cart-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.product-cart-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--accent-dark));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.product-wishlist-btn {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(99, 102, 241, 0.2);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}

.product-wishlist-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* 列表视图样式 */
.product-grid-view.list-view {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.product-grid-view.list-view .product-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: var(--spacing-lg);
}

.product-grid-view.list-view .product-card:hover {
    transform: translateY(-4px);
}

.product-grid-view.list-view .product-img-container {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    margin-right: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
}

.product-grid-view.list-view .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: 0;
}

.product-grid-view.list-view .product-actions {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-left: var(--spacing-lg);
    padding: 0;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: var(--spacing-xxl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    text-decoration: none;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.pagination-number:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    text-decoration: none;
}

.pagination-number.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-dots {
    color: var(--gray-500);
    font-weight: bold;
    padding: 0 var(--spacing-xs);
}

.pagination-info {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* 筛选面板增强 */
.category-sidebar {
    background: linear-gradient(135deg, var(--white) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(99, 102, 241, 0.1);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 120px;
    left: 20px;
    width: 280px;
    height: calc(100vh - 140px);
    overflow-y: auto;
    z-index: 100;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.sidebar-title {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sidebar-title i {
    color: var(--primary-color);
}

.filter-toggle {
    background: rgba(99, 102, 241, 0.1);
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-toggle:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.filter-toggle.collapsed i {
    transform: rotate(180deg);
}

.filter-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.6);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(99, 102, 241, 0.05);
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-title i {
    color: var(--primary-color);
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-item {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.filter-item:hover {
    background: rgba(99, 102, 241, 0.05);
}

.filter-checkbox {
    margin-right: var(--spacing-sm);
    accent-color: var(--primary-color);
}

.filter-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-700);
}

.filter-count {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 2px 8px;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.filter-reset-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: var(--spacing-lg);
}

.filter-reset-btn:hover {
    background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.category-sidebar.collapsed .filter-section {
    display: none;
}

.category-sidebar.collapsed .filter-reset-btn {
    display: none;
}

/* 价格滑块增强 */
.price-slider {
    margin: var(--spacing-md) 0;
}

.price-range {
    position: relative;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    margin: var(--spacing-md) 0;
    cursor: pointer;
}

.price-progress {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 3px;
    width: 100%;
    transition: width 0.3s ease;
}

.price-values {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 600;
}

.price-range {
    position: relative;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    cursor: pointer;
    margin: var(--spacing-md) 0;
}

.price-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 3px;
    width: 100%;
    transition: width 0.3s ease;
}

.price-values {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* 主要内容区域响应式设计 */
@media (max-width: 1024px) {
    #main-content {
        min-height: calc(100vh - 180px);
    }

    #main-content .container {
        padding-top: var(--spacing-md);
        padding-bottom: var(--spacing-md);
    }

    .content-wrapper {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    #main-content {
        min-height: calc(100vh - 160px);
    }

    #main-content .container {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
        padding-top: var(--spacing-sm);
        padding-bottom: var(--spacing-sm);
    }

    .page-content {
        margin: var(--spacing-md) 0;
        border-radius: var(--border-radius);
    }

    .content-wrapper {
        padding: var(--spacing-md);
    }

    /* Category页面响应式优化 */
    .category-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .view-toggle {
        align-self: flex-start;
    }

    .pagination-wrapper {
        flex-direction: column;
        text-align: center;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .product-grid-view.list-view .product-card {
        flex-direction: column;
        text-align: center;
    }

    .product-grid-view.list-view .product-img-container {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
        align-self: center;
    }

    .product-grid-view.list-view .product-actions {
        margin-left: 0;
        margin-top: var(--spacing-md);
        flex-direction: row;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    #main-content {
        min-height: calc(100vh - 140px);
    }

    .content-wrapper {
        padding: var(--spacing-sm);
    }

    #main-content h1 {
        font-size: 1.75rem;
    }

    #main-content h2 {
        font-size: 1.5rem;
    }

    #main-content h3 {
        font-size: 1.25rem;
    }
}