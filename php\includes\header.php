<!-- 导航栏样式已移至 css/layout-styles.css -->

<!-- 用户下拉菜单脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 查找所有用户下拉菜单按钮
    const dropdownToggles = document.querySelectorAll('.user-dropdown-toggle');
    
    // 为每个下拉按钮添加点击事件
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 获取菜单元素和下拉容器
            const dropdown = this.closest('.user-dropdown');
            const menu = dropdown.querySelector('.user-menu');
            
            // 切换当前菜单的显示状态
            const isActive = dropdown.classList.contains('active');
            
            // 先关闭所有其他菜单
            document.querySelectorAll('.user-dropdown').forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('active');
                    const otherMenu = otherDropdown.querySelector('.user-menu');
                    if (otherMenu) {
                        otherMenu.classList.remove('show');
                    }
                }
            });
            
            // 切换当前菜单状态
            if (isActive) {
                dropdown.classList.remove('active');
                menu.classList.remove('show');
            } else {
                dropdown.classList.add('active');
                menu.classList.add('show');
            }
        });
    });
    
    // 点击页面其他地方关闭菜单
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.user-dropdown')) {
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });
    
    // 键盘支持 - ESC键关闭菜单
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });
});
</script> 