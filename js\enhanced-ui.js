/**
 * 增强用户界面交互
 * 提供更好的用户体验和交互效果
 */

class EnhancedUI {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupSmoothScrolling();
        this.setupImageZoom();
        this.setupTooltips();
        this.setupLoadingStates();
        this.setupKeyboardNavigation();
        this.setupOfflineDetection();
        this.setupPerformanceMonitoring();
        this.setupFlashMessages();
    }

    /**
     * 图片懒加载
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;

                        // 创建新图片对象预加载
                        const newImg = new Image();
                        newImg.onload = () => {
                            img.src = img.dataset.src || img.src;
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        };
                        newImg.onerror = () => {
                            img.src = 'images/placeholder.jpg'; // 备用图片
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        };
                        newImg.src = img.dataset.src || img.src;
                    }
                });
            }, {
                rootMargin: '50px'
            });

            document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * 平滑滚动
     */
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * 图片放大功能
     */
    setupImageZoom() {
        document.querySelectorAll('.product-image, .main-image img').forEach(img => {
            img.addEventListener('click', (e) => {
                this.showImageModal(e.target.src, e.target.alt);
            });
        });
    }

    /**
     * 显示图片模态框
     */
    showImageModal(src, alt) {
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="image-modal-backdrop"></div>
            <div class="image-modal-content">
                <img src="${src}" alt="${alt}" class="image-modal-img">
                <button class="image-modal-close" aria-label="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        // 关闭模态框
        const closeModal = () => {
            document.body.removeChild(modal);
            document.body.style.overflow = '';
        };

        modal.querySelector('.image-modal-close').addEventListener('click', closeModal);
        modal.querySelector('.image-modal-backdrop').addEventListener('click', closeModal);

        // ESC键关闭
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);

        // 添加显示动画
        setTimeout(() => modal.classList.add('show'), 10);
    }

    /**
     * 工具提示
     */
    setupTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });

            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
        });
    }

    showTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-popup';
        tooltip.textContent = element.dataset.tooltip;
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';

        element._tooltip = tooltip;
        setTimeout(() => tooltip.classList.add('show'), 10);
    }

    hideTooltip(element) {
        if (element._tooltip) {
            element._tooltip.remove();
            delete element._tooltip;
        }
    }

    /**
     * 加载状态管理
     */
    setupLoadingStates() {
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', () => {
            const loader = document.querySelector('.loading');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 300);
            }
        });

        // 为表单提交添加加载状态
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                }
            });
        });
    }

    /**
     * 键盘导航支持
     */
    setupKeyboardNavigation() {
        // 商品卡片键盘导航
        document.querySelectorAll('.product-card').forEach((card, index) => {
            card.setAttribute('tabindex', '0');
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
            });
        });

        // 购物车快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+C 打开购物车
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                window.location.href = 'cart.php';
            }

            // Ctrl+Shift+S 打开搜索
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }

    /**
     * 离线检测
     */
    setupOfflineDetection() {
        const updateOnlineStatus = () => {
            const isOnline = navigator.onLine;
            const statusIndicator = document.querySelector('.online-status') || this.createOnlineStatusIndicator();

            statusIndicator.className = `online-status ${isOnline ? 'online' : 'offline'}`;
            statusIndicator.textContent = isOnline ? '在线' : '离线';

            if (!isOnline) {
                this.showNotification('网络连接已断开，部分功能可能受限', 'warning');
            }
        };

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus();
    }

    createOnlineStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'online-status';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(indicator);
        return indicator;
    }

    /**
     * 性能监控
     */
    setupPerformanceMonitoring() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    const loadTime = perfData.loadEventEnd - perfData.fetchStart;

                    // 如果加载时间超过3秒，显示提示
                    if (loadTime > 3000) {
                        console.warn('页面加载时间较长:', loadTime + 'ms');
                    }

                    // 发送性能数据到服务器（可选）
                    if (window.location.search.includes('debug=1')) {
                        console.log('页面性能数据:', {
                            loadTime: loadTime,
                            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                            firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                        });
                    }
                }, 0);
            });
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" aria-label="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 绑定关闭事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });

        // 自动隐藏
        setTimeout(() => {
            if (notification.parentNode) {
                this.hideNotification(notification);
            }
        }, 5000);
    }

    hideNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Flash消息处理
     */
    setupFlashMessages() {
        // 自动隐藏Flash消息
        document.querySelectorAll('.flash-message').forEach(message => {
            // 绑定关闭按钮
            const closeBtn = message.querySelector('.flash-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.hideFlashMessage(message);
                });
            }

            // 自动隐藏（成功消息3秒，错误消息5秒）
            const isError = message.classList.contains('flash-error');
            const delay = isError ? 5000 : 3000;

            setTimeout(() => {
                if (message.parentNode) {
                    this.hideFlashMessage(message);
                }
            }, delay);
        });
    }

    hideFlashMessage(message) {
        message.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 300);
    }

    /**
     * 添加到收藏夹
     */
    addToFavorites() {
        try {
            if (window.external && window.external.AddFavorite) {
                window.external.AddFavorite(location.href, document.title);
            } else if (window.sidebar && window.sidebar.addPanel) {
                window.sidebar.addPanel(document.title, location.href, '');
            } else {
                this.showNotification('请使用 Ctrl+D 添加到收藏夹', 'info');
            }
        } catch (e) {
            this.showNotification('请使用 Ctrl+D 添加到收藏夹', 'info');
        }
    }
}

// 初始化增强UI
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedUI = new EnhancedUI();
});

// 导出给其他脚本使用
window.EnhancedUI = EnhancedUI;
