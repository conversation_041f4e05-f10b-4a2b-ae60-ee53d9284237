/**
 * 购物车状态管理系统
 * 提供完整的购物车功能，包括添加、删除、更新、持久化存储等
 */

class CartManager {
    constructor() {
        this.cart = this.loadCart();
        this.wishlist = this.loadWishlist();
        this.init();
    }

    /**
     * 初始化购物车管理器
     */
    init() {
        this.updateCartUI();
        this.updateWishlistUI();
        this.bindEvents();

        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === 'cosplay_cart') {
                this.cart = this.loadCart();
                this.updateCartUI();
            } else if (e.key === 'cosplay_wishlist') {
                this.wishlist = this.loadWishlist();
                this.updateWishlistUI();
            }
        });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加到购物车按钮
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-to-cart, .product-cart-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const button = e.target.closest('.add-to-cart, .product-cart-btn');
                const productData = this.extractProductData(button);

                if (productData) {
                    this.addToCart(productData, button);
                }
            }

            // 心愿单按钮
            if (e.target.closest('.wishlist-btn, .product-wishlist-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const button = e.target.closest('.wishlist-btn, .product-wishlist-btn');
                const productData = this.extractProductData(button);

                if (productData) {
                    this.toggleWishlist(productData, button);
                }
            }
        });
    }

    /**
     * 从按钮元素提取商品数据
     */
    extractProductData(button) {
        const productCard = button.closest('.product-card, .product-detail, .recommend-card');
        if (!productCard) return null;

        const img = productCard.querySelector('img');
        const title = productCard.querySelector('.product-name, .product-title, .recommend-title');
        const price = productCard.querySelector('.product-price, .price-current');

        // 从onclick属性或data属性获取ID
        let productId = button.getAttribute('onclick')?.match(/\d+/)?.[0];
        if (!productId) {
            productId = button.dataset.productId || Date.now().toString();
        }

        return {
            id: productId,
            name: title?.textContent?.trim() || '未知商品',
            price: this.parsePrice(price?.textContent || '¥0'),
            image: img?.src || '',
            quantity: 1,
            specs: this.extractSpecs(productCard)
        };
    }

    /**
     * 提取商品规格信息
     */
    extractSpecs(productCard) {
        const specs = {};
        const specElements = productCard.querySelectorAll('.spec-item, .product-spec');

        specElements.forEach(spec => {
            const text = spec.textContent.trim();
            if (text.includes(':')) {
                const [key, value] = text.split(':');
                specs[key.trim()] = value.trim();
            }
        });

        return specs;
    }

    /**
     * 解析价格字符串
     */
    parsePrice(priceStr) {
        const match = priceStr.match(/[\d,]+\.?\d*/);
        return match ? parseFloat(match[0].replace(',', '')) : 0;
    }

    /**
     * 添加商品到购物车
     */
    addToCart(productData, button = null) {
        const existingItem = this.cart.find(item =>
            item.id === productData.id &&
            JSON.stringify(item.specs) === JSON.stringify(productData.specs)
        );

        if (existingItem) {
            existingItem.quantity += productData.quantity;
        } else {
            this.cart.push({
                ...productData,
                addedAt: new Date().toISOString()
            });
        }

        this.saveCart();
        this.updateCartUI();
        this.showAddToCartAnimation(button);
        this.showNotification(`${productData.name} 已添加到购物车`, 'success');

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('cartUpdated', {
            detail: { action: 'add', product: productData, cart: this.cart }
        }));
    }

    /**
     * 从购物车移除商品
     */
    removeFromCart(productId, specs = {}) {
        const index = this.cart.findIndex(item =>
            item.id === productId &&
            JSON.stringify(item.specs) === JSON.stringify(specs)
        );

        if (index > -1) {
            const removedItem = this.cart.splice(index, 1)[0];
            this.saveCart();
            this.updateCartUI();
            this.showNotification(`${removedItem.name} 已从购物车移除`, 'info');

            window.dispatchEvent(new CustomEvent('cartUpdated', {
                detail: { action: 'remove', product: removedItem, cart: this.cart }
            }));
        }
    }

    /**
     * 更新商品数量
     */
    updateQuantity(productId, quantity, specs = {}) {
        const item = this.cart.find(item =>
            item.id === productId &&
            JSON.stringify(item.specs) === JSON.stringify(specs)
        );

        if (item) {
            if (quantity <= 0) {
                this.removeFromCart(productId, specs);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartUI();

                window.dispatchEvent(new CustomEvent('cartUpdated', {
                    detail: { action: 'update', product: item, cart: this.cart }
                }));
            }
        }
    }

    /**
     * 清空购物车
     */
    clearCart() {
        this.cart = [];
        this.saveCart();
        this.updateCartUI();
        this.showNotification('购物车已清空', 'info');

        window.dispatchEvent(new CustomEvent('cartUpdated', {
            detail: { action: 'clear', cart: this.cart }
        }));
    }

    /**
     * 切换心愿单状态
     */
    toggleWishlist(productData, button = null) {
        const index = this.wishlist.findIndex(item => item.id === productData.id);

        if (index > -1) {
            this.wishlist.splice(index, 1);
            this.showNotification(`${productData.name} 已从心愿单移除`, 'info');
            if (button) this.updateWishlistButton(button, false);
        } else {
            this.wishlist.push({
                ...productData,
                addedAt: new Date().toISOString()
            });
            this.showNotification(`${productData.name} 已添加到心愿单`, 'success');
            if (button) this.updateWishlistButton(button, true);
        }

        this.saveWishlist();
        this.updateWishlistUI();

        window.dispatchEvent(new CustomEvent('wishlistUpdated', {
            detail: { product: productData, wishlist: this.wishlist }
        }));
    }

    /**
     * 更新心愿单按钮状态
     */
    updateWishlistButton(button, isInWishlist) {
        const icon = button.querySelector('i');
        if (icon) {
            if (isInWishlist) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                button.classList.add('active');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                button.classList.remove('active');
            }
        }
    }

    /**
     * 显示添加到购物车动画
     */
    showAddToCartAnimation(button) {
        if (!button) return;

        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> 已添加';
        button.style.background = '#4ecdc4';
        button.disabled = true;

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.style.background = '';
            button.disabled = false;
        }, 2000);
    }

    /**
     * 更新购物车UI
     */
    updateCartUI() {
        // 更新购物车数量徽章
        const cartCounts = document.querySelectorAll('.nav-link-count, .cart-count');
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);

        cartCounts.forEach(count => {
            count.textContent = totalItems;
            count.style.display = totalItems > 0 ? 'inline' : 'none';
        });

        // 更新购物车页面
        if (window.location.pathname.includes('cart.php')) {
            this.renderCartPage();
        }
    }

    /**
     * 更新心愿单UI
     */
    updateWishlistUI() {
        const wishlistCounts = document.querySelectorAll('.wishlist-count');
        wishlistCounts.forEach(count => {
            count.textContent = this.wishlist.length;
        });

        // 更新心愿单按钮状态
        document.querySelectorAll('.wishlist-btn, .product-wishlist-btn').forEach(button => {
            const productData = this.extractProductData(button);
            if (productData) {
                const isInWishlist = this.wishlist.some(item => item.id === productData.id);
                this.updateWishlistButton(button, isInWishlist);
            }
        });
    }

    /**
     * 渲染购物车页面
     */
    renderCartPage() {
        const cartItemsContainer = document.querySelector('.cart-items');
        if (!cartItemsContainer) return;

        if (this.cart.length === 0) {
            cartItemsContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart empty-cart-icon"></i>
                    <h3>您的购物车是空的</h3>
                    <p>快去挑选心仪的商品吧！</p>
                    <a href="category.php" class="btn-primary">去购物</a>
                </div>
            `;

            // 隐藏购物车摘要
            const cartSummary = document.querySelector('.cart-summary');
            if (cartSummary) {
                cartSummary.style.display = 'none';
            }
            return;
        }

        // 显示购物车摘要
        const cartSummary = document.querySelector('.cart-summary');
        if (cartSummary) {
            cartSummary.style.display = 'block';
        }

        // 渲染购物车商品
        cartItemsContainer.innerHTML = this.cart.map(item => this.renderCartItem(item)).join('');

        // 更新总价
        this.updateCartSummary();
    }

    /**
     * 渲染单个购物车商品
     */
    renderCartItem(item) {
        const specsHtml = Object.entries(item.specs).map(([key, value]) =>
            `<span class="spec-item">${key}: ${value}</span>`
        ).join('');

        return `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="cart-item-checkbox">
                    <input type="checkbox" checked>
                </div>
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="cart-item-details">
                    <h3 class="cart-item-title">${item.name}</h3>
                    <div class="cart-item-specs">${specsHtml}</div>
                    <div class="cart-item-price">¥${item.price}</div>
                </div>
                <div class="cart-quantity-control">
                    <button class="quantity-btn minus" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                    <input type="number" class="cart-quantity-input" value="${item.quantity}" min="1"
                           onchange="cartManager.updateQuantity('${item.id}', parseInt(this.value))">
                    <button class="quantity-btn plus" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                </div>
                <div class="cart-item-subtotal">¥${(item.price * item.quantity).toFixed(2)}</div>
                <button class="cart-item-remove" onclick="cartManager.removeFromCart('${item.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    /**
     * 更新购物车汇总
     */
    updateCartSummary() {
        const checkedItems = document.querySelectorAll('.cart-item-checkbox input:checked');
        const summaryTotal = document.querySelector('.summary-total');
        const summaryCount = document.querySelector('.summary-count');

        let total = 0;
        let count = 0;

        checkedItems.forEach(checkbox => {
            const cartItem = checkbox.closest('.cart-item');
            const subtotalElement = cartItem.querySelector('.cart-item-subtotal');
            if (subtotalElement) {
                total += parseFloat(subtotalElement.textContent.replace('¥', ''));
                count++;
            }
        });

        if (summaryTotal) summaryTotal.textContent = `¥${total.toFixed(2)}`;
        if (summaryCount) summaryCount.textContent = `已选择 ${count} 件商品`;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    /**
     * 保存购物车到本地存储
     */
    saveCart() {
        localStorage.setItem('cosplay_cart', JSON.stringify(this.cart));
    }

    /**
     * 从本地存储加载购物车
     */
    loadCart() {
        try {
            return JSON.parse(localStorage.getItem('cosplay_cart')) || [];
        } catch {
            return [];
        }
    }

    /**
     * 保存心愿单到本地存储
     */
    saveWishlist() {
        localStorage.setItem('cosplay_wishlist', JSON.stringify(this.wishlist));
    }

    /**
     * 从本地存储加载心愿单
     */
    loadWishlist() {
        try {
            return JSON.parse(localStorage.getItem('cosplay_wishlist')) || [];
        } catch {
            return [];
        }
    }

    /**
     * 获取购物车商品数量
     */
    getCartItemCount() {
        return this.cart.reduce((sum, item) => sum + item.quantity, 0);
    }

    /**
     * 获取购物车总价
     */
    getCartTotal() {
        return this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    }

    /**
     * 获取心愿单商品数量
     */
    getWishlistItemCount() {
        return this.wishlist.length;
    }
}

// 全局购物车管理器实例
let cartManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    cartManager = new CartManager();
    window.cartManager = cartManager; // 全局访问
});
