/* ===== 导航栏统一样式配置 ===== */
/* 这个文件统一管理所有导航栏相关的样式变量和规则 */

:root {
    /* 导航栏尺寸变量 */
    --navbar-height-desktop: 80px;
    --navbar-height-tablet: 70px;
    --navbar-height-mobile: 60px;
    
    /* 导航栏内边距变量 */
    --navbar-padding-desktop: 16px 0;
    --navbar-padding-tablet: 12px 0;
    --navbar-padding-mobile: 10px 0;
    
    /* 导航栏颜色变量 */
    --navbar-bg: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    --navbar-border: 1px solid rgba(0, 123, 255, 0.1);
    --navbar-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    
    /* 导航栏元素间距 */
    --navbar-gap-desktop: 24px;
    --navbar-gap-tablet: 16px;
    --navbar-gap-mobile: 12px;
}

/* ===== 基础导航栏样式 ===== */
.navbar {
    background: var(--navbar-bg);
    border-bottom: var(--navbar-border);
    box-shadow: var(--navbar-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: var(--navbar-height-desktop);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: var(--navbar-padding-desktop);
    gap: var(--navbar-gap-desktop);
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    box-sizing: border-box;
}

/* ===== 响应式导航栏样式 ===== */
/* 平板端样式 (≤ 768px) */
@media (max-width: 768px) {
    .navbar {
        height: var(--navbar-height-tablet);
    }

    .navbar-container {
        padding: var(--navbar-padding-tablet);
        gap: var(--navbar-gap-tablet);
    }
}

/* 手机端样式 (≤ 480px) */
@media (max-width: 480px) {
    .navbar {
        height: var(--navbar-height-mobile);
    }

    .navbar-container {
        padding: var(--navbar-padding-mobile);
        gap: var(--navbar-gap-mobile);
    }
}

/* ===== 导航栏元素样式 ===== */
.logo {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: #007bff;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.logo span {
    color: #dc3545;
}

.logo:hover {
    transform: scale(1.05);
    text-decoration: none;
}

/* 搜索容器 */
.search-container {
    flex: 1;
    max-width: 500px;
    margin: 0 20px;
    position: relative;
}

.search-container form {
    position: relative;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 14px;
    background: #ffffff;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-btn {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #0056b3;
    transform: translateY(-50%) scale(1.05);
}

/* 导航链接 */
.nav-links {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 12px;
    color: #495057;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.nav-link:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    text-decoration: none;
    transform: translateY(-1px);
}

.nav-link.active {
    background: rgba(0, 123, 255, 0.15);
    color: #007bff;
}

.nav-link-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.nav-link-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

/* 用户下拉菜单 */
.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.user-dropdown-toggle:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.user-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* ===== 响应式导航元素调整 ===== */
@media (max-width: 1024px) {
    .search-container {
        max-width: 400px;
        margin: 0 16px;
    }
    
    .logo {
        font-size: 1.6rem;
    }
}

@media (max-width: 768px) {
    .search-container {
        max-width: 250px;
        margin: 0 12px;
    }
    
    .search-input {
        padding: 10px 45px 10px 14px;
        font-size: 13px;
    }
    
    .search-btn {
        width: 36px;
        height: 36px;
    }
    
    .logo {
        font-size: 1.4rem;
    }
    
    .nav-link {
        padding: 8px 10px;
    }
    
    .nav-link-text {
        display: none;
    }
}

@media (max-width: 480px) {
    .search-container {
        max-width: 180px;
        margin: 0 8px;
    }
    
    .search-input {
        padding: 8px 40px 8px 12px;
        font-size: 12px;
    }
    
    .search-btn {
        width: 32px;
        height: 32px;
    }
    
    .logo {
        font-size: 1.2rem;
    }
    
    .nav-link {
        padding: 6px 8px;
        min-width: 40px;
        justify-content: center;
    }
    
    .nav-link-icon {
        font-size: 14px;
    }
}

/* ===== 导航栏滚动效果 ===== */
.navbar.scrolled {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* ===== 无障碍支持 ===== */
.nav-link:focus,
.user-dropdown-toggle:focus,
.search-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
    .navbar {
        border-bottom-width: 2px;
    }
    
    .nav-link:hover,
    .user-dropdown-toggle:hover {
        background: #e9ecef;
    }
    
    .nav-link.active {
        background: #007bff;
        color: white;
    }
}

/* ===== 减少动画模式支持 ===== */
@media (prefers-reduced-motion: reduce) {
    .navbar,
    .nav-link,
    .user-dropdown-toggle,
    .dropdown-arrow,
    .search-btn,
    .logo {
        transition: none;
    }
    
    .nav-link:hover,
    .user-dropdown-toggle:hover,
    .logo:hover {
        transform: none;
    }
}
