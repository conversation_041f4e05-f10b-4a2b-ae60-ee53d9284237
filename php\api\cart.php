<?php
/**
 * 购物车 API 接口
 * 处理购物车相关的 AJAX 请求
 */

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

// 检查请求方法
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// 响应函数
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ]);
    exit;
}

// 检查用户登录状态
function checkLogin() {
    return isset($_SESSION['user']) && isset($_SESSION['user_id']);
}

// 获取购物车数据
function getCart() {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    return $_SESSION['cart'];
}

// 保存购物车数据
function saveCart($cart) {
    $_SESSION['cart'] = $cart;
}

// 获取购物车商品数量
function getCartCount() {
    $cart = getCart();
    $count = 0;
    foreach ($cart as $item) {
        $count += $item['quantity'];
    }
    return $count;
}

try {
    switch ($action) {
        case 'add':
            // 添加商品到购物车
            if (!checkLogin()) {
                sendResponse(false, '请先登录后再添加商品到购物车', ['redirect' => 'login.php']);
            }
            
            $productId = $_POST['product_id'] ?? '';
            $quantity = (int)($_POST['quantity'] ?? 1);
            
            if (empty($productId)) {
                sendResponse(false, '商品ID不能为空');
            }
            
            if ($quantity <= 0) {
                sendResponse(false, '商品数量必须大于0');
            }
            
            $cart = getCart();
            
            // 检查商品是否已存在
            $found = false;
            foreach ($cart as &$item) {
                if ($item['product_id'] == $productId) {
                    $item['quantity'] += $quantity;
                    $found = true;
                    break;
                }
            }
            
            // 如果商品不存在，添加新商品
            if (!$found) {
                $cart[] = [
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'added_at' => time()
                ];
            }
            
            saveCart($cart);
            
            sendResponse(true, '商品已添加到购物车', [
                'cart_count' => getCartCount(),
                'product_id' => $productId,
                'quantity' => $quantity
            ]);
            break;
            
        case 'remove':
            // 从购物车移除商品
            if (!checkLogin()) {
                sendResponse(false, '请先登录');
            }
            
            $productId = $_POST['product_id'] ?? '';
            
            if (empty($productId)) {
                sendResponse(false, '商品ID不能为空');
            }
            
            $cart = getCart();
            $cart = array_filter($cart, function($item) use ($productId) {
                return $item['product_id'] != $productId;
            });
            
            saveCart(array_values($cart));
            
            sendResponse(true, '商品已从购物车移除', [
                'cart_count' => getCartCount(),
                'product_id' => $productId
            ]);
            break;
            
        case 'update':
            // 更新购物车商品数量
            if (!checkLogin()) {
                sendResponse(false, '请先登录');
            }
            
            $productId = $_POST['product_id'] ?? '';
            $quantity = (int)($_POST['quantity'] ?? 0);
            
            if (empty($productId)) {
                sendResponse(false, '商品ID不能为空');
            }
            
            $cart = getCart();
            
            if ($quantity <= 0) {
                // 数量为0或负数，移除商品
                $cart = array_filter($cart, function($item) use ($productId) {
                    return $item['product_id'] != $productId;
                });
            } else {
                // 更新数量
                foreach ($cart as &$item) {
                    if ($item['product_id'] == $productId) {
                        $item['quantity'] = $quantity;
                        break;
                    }
                }
            }
            
            saveCart(array_values($cart));
            
            sendResponse(true, '购物车已更新', [
                'cart_count' => getCartCount(),
                'product_id' => $productId,
                'quantity' => $quantity
            ]);
            break;
            
        case 'get':
            // 获取购物车内容
            if (!checkLogin()) {
                sendResponse(false, '请先登录');
            }
            
            $cart = getCart();
            
            sendResponse(true, '获取购物车成功', [
                'cart' => $cart,
                'cart_count' => getCartCount()
            ]);
            break;
            
        case 'clear':
            // 清空购物车
            if (!checkLogin()) {
                sendResponse(false, '请先登录');
            }
            
            $_SESSION['cart'] = [];
            
            sendResponse(true, '购物车已清空', [
                'cart_count' => 0
            ]);
            break;
            
        case 'count':
            // 获取购物车商品数量
            if (!checkLogin()) {
                sendResponse(true, '未登录', ['cart_count' => 0]);
            }
            
            sendResponse(true, '获取购物车数量成功', [
                'cart_count' => getCartCount()
            ]);
            break;
            
        default:
            sendResponse(false, '无效的操作');
    }
    
} catch (Exception $e) {
    error_log('购物车API错误: ' . $e->getMessage());
    sendResponse(false, '服务器内部错误，请稍后重试');
}
?>
