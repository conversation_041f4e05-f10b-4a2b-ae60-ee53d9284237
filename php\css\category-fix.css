/**
 * 分类页面布局优化样式
 * 修复category-content中category-products和category-sidebar之间的布局问题
 */

/* 分类内容区域布局优化 */
.category-content {
    display: flex;
    gap: 25px;
    position: relative;
    margin-top: 30px;
    align-items: flex-start;
}

/* 侧边栏样式优化 */
.category-sidebar {
    width: 280px;
    flex-shrink: 0;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    position: sticky;
    top: 90px;
    z-index: 10;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    transition: all 0.3s ease;
}

/* 侧边栏标题样式 */
.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 18px;
    border-bottom: 2px solid #e9ecef;
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.sidebar-title i {
    color: #007bff;
    font-size: 1.1rem;
}

/* 筛选部分样式优化 */
.filter-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.filter-title i {
    color: #007bff;
    font-size: 1rem;
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.filter-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    cursor: pointer;
    padding: 10px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.filter-label:hover {
    background-color: rgba(0, 123, 255, 0.05);
    border-color: rgba(0, 123, 255, 0.2);
}

.filter-checkbox:checked+.filter-label {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
    font-weight: 600;
}

.filter-name {
    color: #2c3e50;
    font-weight: 500;
    font-size: 0.95rem;
}

.filter-checkbox:checked+.filter-label .filter-name {
    color: #007bff;
    font-weight: 600;
}

.filter-count {
    font-size: 0.8rem;
    color: #6c757d;
    background-color: #e9ecef;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.filter-checkbox:checked+.filter-label .filter-count {
    background-color: #007bff;
    color: #ffffff;
}

/* 价格滑块样式优化 */
.price-slider {
    margin-top: 15px;
}

.price-range {
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    position: relative;
    margin-bottom: 10px;
}

.price-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: #007bff;
    border-radius: 3px;
    width: 100%;
}

.price-values {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 14px;
    color: #666;
}

/* 重置按钮样式 */
.filter-reset-btn {
    width: 100%;
    padding: 10px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    color: #555;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.filter-reset-btn:hover {
    background-color: #e6e6e6;
    color: #333;
}

/* 商品列表区域样式优化 */
.category-products {
    flex: 1;
    min-width: 0;
    /* 防止flex子项溢出 */
}

/* 商品工具栏样式优化 */
.products-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px 25px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #e9ecef;
}

.toolbar-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.products-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.products-count {
    color: #007bff;
    font-weight: 600;
    font-size: 1rem;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.view-switcher {
    display: flex;
    gap: 8px;
    background: #f8f9fa;
    padding: 4px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.sort-dropdown {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-label {
    color: #495057;
    font-weight: 600;
    font-size: 1rem;
}

/* 商品网格样式优化 */
.product-grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

/* 分页导航样式优化 */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 15px 20px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .category-content {
        gap: 20px;
    }

    .category-sidebar {
        width: 250px;
    }

    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
}

@media (max-width: 768px) {
    .category-content {
        flex-direction: column;
    }

    .category-sidebar {
        width: 100%;
        position: relative;
        top: 0;
        max-height: none;
        margin-bottom: 20px;
        overflow: visible;
    }

    .filter-toggle {
        display: block;
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #007bff;
    }

    /* 折叠状态 */
    .category-sidebar.collapsed .filter-section,
    .category-sidebar.collapsed .filter-reset-btn {
        display: none;
    }

    .category-sidebar.collapsed {
        padding-bottom: 0;
    }

    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .products-toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 15px;
    }

    .pagination {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .category-sidebar {
        padding: 15px;
    }

    .sidebar-header {
        margin-bottom: 10px;
        padding-bottom: 10px;
    }

    .sidebar-title {
        font-size: 16px;
    }

    .filter-title {
        font-size: 14px;
    }

    .products-title {
        font-size: 16px;
    }

    .toolbar-right {
        flex-wrap: wrap;
        gap: 10px;
    }
}