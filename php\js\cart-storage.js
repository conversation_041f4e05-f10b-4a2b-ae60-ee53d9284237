// 购物车本地存储管理器
class CartStorageManager {
    constructor() {
        this.storageKey = 'cosplay_shopping_cart';
        this.deletedKey = 'cosplay_deleted_items';
        this.wishlistKey = 'cosplay_wishlist';
        this.initializeDefaultData();
    }
    
    // 初始化默认数据（仅在第一次访问时）
    initializeDefaultData() {
        if (!this.hasCartData()) {
            const defaultItems = [
                {
                    id: '1001_M_紫色',
                    product_id: 1001,
                    name: '原神 - 刻晴星霜华裳',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=200&fit=crop',
                    price: 599.00,
                    sale_price: 499.00,
                    quantity: 2,
                    size: 'M',
                    color: '紫色',
                    sku: 'GI-KQ-001'
                },
                {
                    id: '1002_L_白色',
                    product_id: 1002,
                    name: '英雄联盟 - 阿狸',
                    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=150&h=200&fit=crop',
                    price: 649.00,
                    sale_price: null,
                    quantity: 1,
                    size: 'L',
                    color: '白色',
                    sku: 'LOL-AH-001'
                }
            ];
            this.saveCartData(defaultItems);
        }
    }
    
    // 检查是否有购物车数据
    hasCartData() {
        return localStorage.getItem(this.storageKey) !== null;
    }
    
    // 获取购物车数据
    getCartData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : [];
        } catch (e) {
            console.error('Error parsing cart data:', e);
            return [];
        }
    }
    
    // 保存购物车数据
    saveCartData(cartData) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(cartData));
            return true;
        } catch (e) {
            console.error('Error saving cart data:', e);
            return false;
        }
    }
    
    // 获取已删除商品数据
    getDeletedItems() {
        try {
            const data = localStorage.getItem(this.deletedKey);
            return data ? JSON.parse(data) : {};
        } catch (e) {
            console.error('Error parsing deleted items:', e);
            return {};
        }
    }
    
    // 保存已删除商品数据
    saveDeletedItems(deletedData) {
        try {
            localStorage.setItem(this.deletedKey, JSON.stringify(deletedData));
            return true;
        } catch (e) {
            console.error('Error saving deleted items:', e);
            return false;
        }
    }
    
    // 获取心愿单数据
    getWishlistData() {
        try {
            const data = localStorage.getItem(this.wishlistKey);
            return data ? JSON.parse(data) : [];
        } catch (e) {
            console.error('Error parsing wishlist data:', e);
            return [];
        }
    }
    
    // 保存心愿单数据
    saveWishlistData(wishlistData) {
        try {
            localStorage.setItem(this.wishlistKey, JSON.stringify(wishlistData));
            return true;
        } catch (e) {
            console.error('Error saving wishlist data:', e);
            return false;
        }
    }
    
    // 添加商品到购物车
    addItem(item) {
        const cartData = this.getCartData();
        const existingIndex = cartData.findIndex(cartItem => cartItem.id === item.id);
        
        if (existingIndex >= 0) {
            cartData[existingIndex].quantity += item.quantity || 1;
        } else {
            cartData.push({
                ...item,
                quantity: item.quantity || 1
            });
        }
        
        return this.saveCartData(cartData);
    }
    
    // 更新商品数量
    updateQuantity(itemId, quantity) {
        const cartData = this.getCartData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
            if (quantity <= 0) {
                cartData.splice(itemIndex, 1);
            } else {
                cartData[itemIndex].quantity = quantity;
            }
            return this.saveCartData(cartData);
        }
        return false;
    }
    
    // 软删除商品
    softRemoveItem(itemId) {
        const cartData = this.getCartData();
        const deletedItems = this.getDeletedItems();
        const itemIndex = cartData.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
            const item = cartData[itemIndex];
            deletedItems[itemId] = {
                ...item,
                deleted_at: Date.now()
            };
            
            cartData.splice(itemIndex, 1);
            this.saveCartData(cartData);
            this.saveDeletedItems(deletedItems);
            return true;
        }
        return false;
    }
    
    // 恢复已删除的商品
    restoreItem(itemId) {
        const deletedItems = this.getDeletedItems();
        
        if (deletedItems[itemId]) {
            const item = { ...deletedItems[itemId] };
            delete item.deleted_at;
            
            this.addItem(item);
            delete deletedItems[itemId];
            this.saveDeletedItems(deletedItems);
            return true;
        }
        return false;
    }
    
    // 永久删除商品
    removeItem(itemId) {
        const cartData = this.getCartData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
            cartData.splice(itemIndex, 1);
            return this.saveCartData(cartData);
        }
        return false;
    }
    
    // 移动到心愿单
    moveToWishlist(itemId) {
        const cartData = this.getCartData();
        const wishlistData = this.getWishlistData();
        const itemIndex = cartData.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
            const item = cartData[itemIndex];
            wishlistData.push({
                ...item,
                added_at: Date.now()
            });
            
            cartData.splice(itemIndex, 1);
            this.saveCartData(cartData);
            this.saveWishlistData(wishlistData);
            return true;
        }
        return false;
    }
    
    // 清空购物车
    clearCart() {
        return this.saveCartData([]);
    }
    
    // 获取商品总数
    getTotalItems() {
        const cartData = this.getCartData();
        return cartData.reduce((total, item) => total + item.quantity, 0);
    }
    
    // 计算小计
    getSubtotal() {
        const cartData = this.getCartData();
        return cartData.reduce((total, item) => {
            const price = item.sale_price || item.price;
            return total + (price * item.quantity);
        }, 0);
    }
    
    // 清理过期的已删除商品（超过5分钟）
    cleanupExpiredItems() {
        const deletedItems = this.getDeletedItems();
        const currentTime = Date.now();
        const fiveMinutes = 5 * 60 * 1000; // 5分钟
        
        let hasChanges = false;
        for (const itemId in deletedItems) {
            if (deletedItems[itemId].deleted_at && 
                (currentTime - deletedItems[itemId].deleted_at) > fiveMinutes) {
                delete deletedItems[itemId];
                hasChanges = true;
            }
        }
        
        if (hasChanges) {
            this.saveDeletedItems(deletedItems);
        }
    }
    
    // 同步数据到服务器
    async syncToServer() {
        const cartData = this.getCartData();
        
        try {
            const response = await fetch('cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'sync_cart',
                    cart_data: JSON.stringify(cartData)
                })
            });
            
            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('Error syncing to server:', error);
            return false;
        }
    }
}

// 全局购物车管理器实例
window.cartStorage = new CartStorageManager();
