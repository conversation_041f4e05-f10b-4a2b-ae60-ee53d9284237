<?php
/**
 * COSPlay购物网站 - 订单管理页面
 * 显示用户的所有订单信息
 */

// 引入配置文件
require_once 'config/database.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// 获取当前用户信息
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// 获取用户订单（使用模拟数据）
$orders = getMockOrders($user_id);

// 计算统计数据
$total_orders = count($orders);
$pending_orders = count(array_filter($orders, function($order) { return $order['status'] === 'pending'; }));
$processing_orders = count(array_filter($orders, function($order) { return $order['status'] === 'processing'; }));
$completed_orders = count(array_filter($orders, function($order) { return $order['status'] === 'delivered'; }));

// 页面信息设置
$page_title = '我的订单 - COSPlay购物网站';
$page_description = '查看和管理您的所有订单信息';

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.orders-title {
    color: #212529 !important;
}

.stat-label {
    color: #6c757d !important;
}

.stat-number {
    color: #212529 !important;
}

.order-number {
    color: #212529 !important;
}

.order-date {
    color: #6c757d !important;
}

.item-name {
    color: #212529 !important;
}

.item-origin {
    color: #6c757d !important;
}

.item-quantity {
    color: #495057 !important;
}

.item-price {
    color: #007bff !important;
}

.total-label {
    color: #495057 !important;
}

.total-amount {
    color: #212529 !important;
}

.total-note {
    color: #6c757d !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.btn-primary, .btn-secondary, .btn-danger {
    color: #ffffff !important;
}

.filter-tab {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

.filter-tab.active {
    color: #007bff !important;
    background-color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保订单卡片背景和文字可见 */
.order-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.orders-header, .orders-filters {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* 确保表单文字可见 */
.search-input, .date-select {
    color: #495057 !important;
    background-color: #ffffff !important;
}

/* 确保空状态文字可见 */
.empty-orders {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.empty-orders h3 {
    color: #212529 !important;
}

.empty-orders p {
    color: #495057 !important;
}

/* 确保分页文字可见 */
.page-btn, .page-number {
    color: #495057 !important;
    background-color: #ffffff !important;
}

.page-number.active {
    color: #ffffff !important;
    background-color: #007bff !important;
}
</style>

<div class="orders-page">
    <div class="container">
        <div class="orders-header">
            <h1 class="orders-title">
                <i class="fas fa-shopping-bag"></i>
                我的订单
            </h1>
            <div class="orders-stats">
                <div class="stat-item">
                    <span class="stat-number"><?php echo $total_orders; ?></span>
                    <span class="stat-label">总订单</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $pending_orders; ?></span>
                    <span class="stat-label">待付款</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $processing_orders; ?></span>
                    <span class="stat-label">处理中</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $completed_orders; ?></span>
                    <span class="stat-label">已完成</span>
                </div>
            </div>
        </div>

        <div class="orders-filters">
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">
                    <i class="fas fa-list"></i>
                    全部订单
                </button>
                <button class="filter-tab" data-status="pending">
                    <i class="fas fa-clock"></i>
                    待付款
                </button>
                <button class="filter-tab" data-status="processing">
                    <i class="fas fa-cog"></i>
                    处理中
                </button>
                <button class="filter-tab" data-status="shipped">
                    <i class="fas fa-truck"></i>
                    已发货
                </button>
                <button class="filter-tab" data-status="delivered">
                    <i class="fas fa-check-circle"></i>
                    已送达
                </button>
                <button class="filter-tab" data-status="cancelled">
                    <i class="fas fa-times-circle"></i>
                    已取消
                </button>
            </div>

            <div class="search-filters">
                <div class="search-box">
                    <input type="text" placeholder="搜索订单号或商品名称" class="search-input">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="date-filter">
                    <select class="date-select">
                        <option value="">全部时间</option>
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近3个月</option>
                        <option value="365">最近一年</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="orders-list">
            <?php if (empty($orders)): ?>
                <div class="empty-orders">
                    <div class="empty-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <h3>暂无订单</h3>
                    <p>您还没有任何订单，快去选购心仪的商品吧！</p>
                    <a href="category.php" class="btn-primary">
                        <i class="fas fa-shopping-cart"></i>
                        开始购物
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <div class="order-card" data-status="<?php echo $order['status']; ?>">
                        <div class="order-header">
                            <div class="order-info">
                                <span class="order-number">订单号: <?php echo escape($order['id']); ?></span>
                                <span class="order-date"><?php echo formatDate($order['created_at']); ?></span>
                            </div>
                            <div class="order-status">
                                <span class="status-badge <?php echo $order['status']; ?>">
                                    <?php echo getOrderStatusName($order['status']); ?>
                                </span>
                            </div>
                        </div>

                        <div class="order-items">
                            <?php foreach ($order['items'] as $item): ?>
                                <div class="order-item">
                                    <div class="item-image">
                                        <img src="<?php echo escape($item['image']); ?>" alt="<?php echo escape($item['name']); ?>">
                                    </div>
                                    <div class="item-details">
                                        <h4 class="item-name"><?php echo escape($item['name']); ?></h4>
                                        <p class="item-origin"><?php echo escape($item['origin']); ?></p>
                                        <div class="item-specs">
                                            <?php foreach ($item['specs'] as $spec): ?>
                                                <span class="spec"><?php echo escape($spec); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="item-quantity">数量: <?php echo $item['quantity']; ?></p>
                                    </div>
                                    <div class="item-price"><?php echo formatPrice($item['price']); ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="order-footer">
                            <div class="order-total">
                                <span class="total-label">订单总额:</span>
                                <span class="total-amount"><?php echo formatPrice($order['total_amount']); ?></span>
                                <span class="total-note">(含运费 <?php echo formatPrice($order['shipping_fee']); ?>)</span>
                            </div>
                            <div class="order-actions">
                                <button class="btn-secondary" onclick="viewOrderDetail('<?php echo $order['id']; ?>')">
                                    <i class="fas fa-eye"></i>
                                    查看详情
                                </button>
                                <?php if ($order['status'] === 'delivered'): ?>
                                    <button class="btn-primary" onclick="rateOrder('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-star"></i>
                                        评价商品
                                    </button>
                                    <button class="btn-secondary" onclick="buyAgain('<?php echo $order['items'][0]['product_id']; ?>')">
                                        <i class="fas fa-redo"></i>
                                        再次购买
                                    </button>
                                <?php elseif ($order['status'] === 'shipped'): ?>
                                    <button class="btn-primary" onclick="trackOrder('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-truck"></i>
                                        查看物流
                                    </button>
                                    <button class="btn-secondary" onclick="confirmReceipt('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-check"></i>
                                        确认收货
                                    </button>
                                <?php elseif ($order['status'] === 'processing'): ?>
                                    <button class="btn-danger" onclick="cancelOrder('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-times"></i>
                                        取消订单
                                    </button>
                                <?php elseif ($order['status'] === 'pending'): ?>
                                    <button class="btn-primary" onclick="payOrder('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-credit-card"></i>
                                        立即付款
                                    </button>
                                    <button class="btn-danger" onclick="cancelOrder('<?php echo $order['id']; ?>')">
                                        <i class="fas fa-times"></i>
                                        取消订单
                                    </button>
                                <?php endif; ?>
                            </div>
                            <?php if ($order['status'] === 'pending'): ?>
                                <div class="payment-countdown">
                                    <i class="fas fa-clock"></i>
                                    <span>剩余付款时间: <strong id="countdown-<?php echo $order['id']; ?>">23:45:32</strong></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

        </div>

        <!-- 分页 -->
        <div class="pagination">
            <button class="page-btn prev" disabled>
                <i class="fas fa-chevron-left"></i>
                上一页
            </button>
            <div class="page-numbers">
                <button class="page-number active">1</button>
                <button class="page-number">2</button>
                <button class="page-number">3</button>
                <span class="page-dots">...</span>
                <button class="page-number">10</button>
            </div>
            <button class="page-btn next">
                下一页
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 订单页面样式 -->
<style>
.empty-orders {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.empty-icon {
    font-size: 4rem;
    color: #e0e0e0;
    margin-bottom: 20px;
}

.empty-orders h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.empty-orders p {
    color: #666;
    margin-bottom: 30px;
    font-size: 1rem;
}

.btn-primary, .btn-secondary, .btn-danger {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.processing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.shipped {
    background: #d4edda;
    color: #155724;
}

.status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}
</style>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 订单状态筛选
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const status = this.dataset.status;
            
            // 更新标签状态
            document.querySelector('.filter-tab.active').classList.remove('active');
            this.classList.add('active');
            
            // 筛选订单
            filterOrders(status);
        });
    });

    // 搜索功能
    document.querySelector('.search-btn').addEventListener('click', function() {
        const searchTerm = document.querySelector('.search-input').value;
        searchOrders(searchTerm);
    });

    // 日期筛选
    document.querySelector('.date-select').addEventListener('change', function() {
        const days = this.value;
        filterOrdersByDate(days);
    });

    // 付款倒计时
    startCountdown('COS2024012204', 23 * 3600 + 45 * 60 + 32);
});

// 筛选订单
function filterOrders(status) {
    const orders = document.querySelectorAll('.order-card');
    
    orders.forEach(order => {
        if (status === 'all' || order.dataset.status === status) {
            order.style.display = 'block';
        } else {
            order.style.display = 'none';
        }
    });
}

// 搜索订单
function searchOrders(searchTerm) {
    const orders = document.querySelectorAll('.order-card');
    
    orders.forEach(order => {
        const orderNumber = order.querySelector('.order-number').textContent;
        const itemNames = Array.from(order.querySelectorAll('.item-name')).map(el => el.textContent);
        
        const matches = orderNumber.includes(searchTerm) || 
                       itemNames.some(name => name.includes(searchTerm));
        
        order.style.display = matches ? 'block' : 'none';
    });
}

// 按日期筛选订单
function filterOrdersByDate(days) {
    if (!days) return;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));
    
    const orders = document.querySelectorAll('.order-card');
    
    orders.forEach(order => {
        const dateText = order.querySelector('.order-date').textContent;
        const orderDate = new Date(dateText);
        
        order.style.display = orderDate >= cutoffDate ? 'block' : 'none';
    });
}

// 付款倒计时
function startCountdown(orderId, seconds) {
    const countdownEl = document.getElementById(`countdown-${orderId}`);
    if (!countdownEl) return;
    
    const timer = setInterval(() => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        countdownEl.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        
        if (seconds <= 0) {
            clearInterval(timer);
            countdownEl.textContent = '已超时';
            countdownEl.style.color = '#ef4444';
        }
        
        seconds--;
    }, 1000);
}

// 查看订单详情
function viewOrderDetail(orderNumber) {
    window.location.href = `order-detail.php?order=${orderNumber}`;
}

// 评价订单
function rateOrder(orderNumber) {
    window.location.href = `rate-order.php?order=${orderNumber}`;
}

// 再次购买
function buyAgain(productId) {
    window.location.href = `detail.php?id=${productId}`;
}

// 查看物流
function trackOrder(orderNumber) {
    window.location.href = `track-order.php?order=${orderNumber}`;
}

// 确认收货
function confirmReceipt(orderNumber) {
    if (confirm('确认已收到商品吗？确认后将无法申请退款。')) {
        // 这里应该调用后端API
        alert('确认收货成功！');
        location.reload();
    }
}

// 取消订单
function cancelOrder(orderNumber) {
    if (confirm('确定要取消这个订单吗？')) {
        // 这里应该调用后端API
        alert('订单已取消');
        location.reload();
    }
}

// 立即付款
function payOrder(orderNumber) {
    window.location.href = `payment.php?order=${orderNumber}`;
}
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
