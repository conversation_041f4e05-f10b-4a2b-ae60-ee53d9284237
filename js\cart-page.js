/**
 * 购物车页面专用JavaScript
 * 处理购物车页面的特殊交互和功能
 */

class CartPage {
    constructor() {
        this.selectedItems = new Set();
        this.coupons = [
            { code: 'NEW50', name: '新用户专享', desc: '满500减50', minAmount: 500, discount: 50 },
            { code: 'SAVE100', name: '满减优惠', desc: '满1000减100', minAmount: 1000, discount: 100 },
            { code: 'VIP20', name: 'VIP专享', desc: '全场8折', minAmount: 0, discount: 0.2, type: 'percentage' }
        ];
        this.appliedCoupon = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateCartDisplay();

        // 监听购物车更新事件
        window.addEventListener('cartUpdated', () => {
            this.updateCartDisplay();
        });
    }

    bindEvents() {
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('select-all-items');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // 商品选择
        document.addEventListener('change', (e) => {
            if (e.target.matches('.cart-item-checkbox input[type="checkbox"]')) {
                this.toggleItemSelection(e.target);
            }
        });

        // 数量控制
        document.addEventListener('click', (e) => {
            if (e.target.matches('.cart-quantity-btn.minus')) {
                this.decreaseQuantity(e.target);
            } else if (e.target.matches('.cart-quantity-btn.plus')) {
                this.increaseQuantity(e.target);
            }
        });

        // 数量输入
        document.addEventListener('change', (e) => {
            if (e.target.matches('.cart-quantity-input')) {
                this.updateQuantityFromInput(e.target);
            }
        });

        // 删除商品
        document.addEventListener('click', (e) => {
            if (e.target.closest('.cart-item-remove')) {
                this.removeItem(e.target.closest('.cart-item'));
            }
        });

        // 批量删除
        const batchDeleteBtn = document.querySelector('.batch-delete-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => {
                this.batchDelete();
            });
        }

        // 批量收藏
        const batchFavoriteBtn = document.querySelector('.batch-favorite-btn');
        if (batchFavoriteBtn) {
            batchFavoriteBtn.addEventListener('click', () => {
                this.batchAddToWishlist();
            });
        }

        // 清空购物车
        const clearCartBtn = document.querySelector('.clear-cart-btn');
        if (clearCartBtn) {
            clearCartBtn.addEventListener('click', () => {
                this.clearCart();
            });
        }

        // 优惠券相关
        this.bindCouponEvents();

        // 继续购物
        const continueShoppingBtn = document.querySelector('.continue-shopping-btn');
        if (continueShoppingBtn) {
            continueShoppingBtn.addEventListener('click', () => {
                window.location.href = 'category.php';
            });
        }

        // 推荐商品快速添加
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-add-btn')) {
                this.quickAddRecommended(e.target.closest('.recommended-item'));
            }
        });
    }

    bindCouponEvents() {
        // 优惠券展开/收起
        const couponHeader = document.querySelector('.coupon-header');
        if (couponHeader) {
            couponHeader.addEventListener('click', () => {
                this.toggleCouponSection();
            });
        }

        // 优惠券应用
        const couponForm = document.querySelector('.coupon-form');
        if (couponForm) {
            couponForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.applyCouponCode();
            });
        }

        // 可用优惠券使用
        document.addEventListener('click', (e) => {
            if (e.target.matches('.coupon-use')) {
                const couponItem = e.target.closest('.coupon-item');
                const couponCode = couponItem.dataset.code;
                this.applyCoupon(couponCode);
            }
        });
    }

    updateCartDisplay() {
        if (!window.cartManager) return;

        const cart = window.cartManager.cart;
        const cartItemsContainer = document.querySelector('.cart-items');

        if (cart.length === 0) {
            this.showEmptyCart();
            return;
        }

        // 渲染购物车商品
        cartItemsContainer.innerHTML = cart.map(item => this.renderCartItem(item)).join('');

        // 更新统计信息
        this.updateCartStats();
        this.updateSummary();
    }

    renderCartItem(item) {
        const specsHtml = Object.entries(item.specs || {}).map(([key, value]) =>
            `<span class="cart-item-variant">${key}: ${value}</span>`
        ).join('');

        return `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="cart-item-checkbox">
                    <input type="checkbox" id="item-${item.id}" checked>
                    <label for="item-${item.id}" aria-label="选择商品"></label>
                </div>
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name}" class="cart-item-img">
                </div>
                <div class="cart-item-details">
                    <h3 class="cart-item-title">
                        <a href="detail.php?id=${item.id}">${item.name}</a>
                    </h3>
                    <div class="cart-item-specs">
                        ${specsHtml}
                    </div>
                    <div class="cart-item-price-info">
                        <span class="cart-item-price">¥${item.price}</span>
                    </div>
                </div>
                <div class="cart-item-actions">
                    <div class="cart-quantity-control">
                        <button class="cart-quantity-btn minus" aria-label="减少数量">-</button>
                        <input type="number" class="cart-quantity-input" value="${item.quantity}" min="1" max="99" aria-label="商品数量">
                        <button class="cart-quantity-btn plus" aria-label="增加数量">+</button>
                    </div>
                    <div class="cart-item-subtotal">
                        <span class="subtotal-label">小计</span>
                        <span class="subtotal-price">¥${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                    <button class="cart-item-remove" aria-label="移除商品">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
        `;
    }

    showEmptyCart() {
        const cartItemsContainer = document.querySelector('.cart-items');
        cartItemsContainer.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart empty-cart-icon"></i>
                <h3>您的购物车是空的</h3>
                <p>快去挑选心仪的商品吧！</p>
                <a href="category.html" class="btn-primary">去购物</a>
            </div>
        `;

        // 隐藏结算区域
        const cartSummary = document.querySelector('.cart-summary');
        if (cartSummary) {
            cartSummary.style.display = 'none';
        }
    }

    updateCartStats() {
        const cart = window.cartManager.cart;
        const totalCount = cart.reduce((sum, item) => sum + item.quantity, 0);

        const cartTotalCountElement = document.querySelector('.cart-total-count');
        if (cartTotalCountElement) {
            cartTotalCountElement.textContent = totalCount;
        }
    }

    updateSummary() {
        const selectedCheckboxes = document.querySelectorAll('.cart-item-checkbox input:checked');
        const cart = window.cartManager.cart;

        let selectedCount = 0;
        let subtotal = 0;

        selectedCheckboxes.forEach(checkbox => {
            const cartItem = checkbox.closest('.cart-item');
            const productId = cartItem.dataset.productId;
            const item = cart.find(item => item.id === productId);

            if (item) {
                selectedCount += item.quantity;
                subtotal += item.price * item.quantity;
            }
        });

        // 更新选中商品数量
        const selectedCountElement = document.querySelector('.selected-count');
        if (selectedCountElement) {
            selectedCountElement.textContent = selectedCount;
        }

        // 更新商品总价
        const subtotalAmountElement = document.querySelector('.subtotal-amount');
        if (subtotalAmountElement) {
            subtotalAmountElement.textContent = `¥${subtotal.toFixed(2)}`;
        }

        // 计算运费
        const shippingFee = subtotal >= 299 ? 0 : 15;
        const shippingAmountElement = document.querySelector('.shipping-amount');
        const shippingStatusElement = document.querySelector('.shipping-status');

        if (shippingAmountElement && shippingStatusElement) {
            if (shippingFee === 0) {
                shippingAmountElement.textContent = '¥0';
                shippingStatusElement.textContent = '免费';
            } else {
                shippingAmountElement.textContent = `¥${shippingFee}`;
                shippingStatusElement.textContent = '';
            }
        }

        // 计算优惠金额
        let discountAmount = 0;
        if (this.appliedCoupon) {
            if (this.appliedCoupon.type === 'percentage') {
                discountAmount = subtotal * this.appliedCoupon.discount;
            } else {
                discountAmount = this.appliedCoupon.discount;
            }
        }

        // 更新总价
        const total = subtotal + shippingFee - discountAmount;
        const summaryTotalElement = document.querySelector('.summary-total');
        if (summaryTotalElement) {
            summaryTotalElement.textContent = `¥${total.toFixed(2)}`;
        }

        // 更新节省金额
        const savingsInfo = document.querySelector('.savings-info');
        if (savingsInfo && discountAmount > 0) {
            savingsInfo.textContent = `已为您节省 ¥${discountAmount.toFixed(2)}`;
            savingsInfo.style.display = 'block';
        } else if (savingsInfo) {
            savingsInfo.style.display = 'none';
        }

        // 更新批量操作按钮状态
        this.updateBatchButtons();
    }

    toggleSelectAll(checked) {
        const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox input[type="checkbox"]');
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateSummary();
    }

    toggleItemSelection(checkbox) {
        this.updateSummary();

        // 更新全选状态
        const selectAllCheckbox = document.getElementById('select-all-items');
        const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox input[type="checkbox"]');
        const checkedCount = document.querySelectorAll('.cart-item-checkbox input:checked').length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        }
    }

    decreaseQuantity(button) {
        const cartItem = button.closest('.cart-item');
        const quantityInput = cartItem.querySelector('.cart-quantity-input');
        const currentQuantity = parseInt(quantityInput.value);

        if (currentQuantity > 1) {
            const newQuantity = currentQuantity - 1;
            quantityInput.value = newQuantity;
            this.updateItemQuantity(cartItem, newQuantity);
        }
    }

    increaseQuantity(button) {
        const cartItem = button.closest('.cart-item');
        const quantityInput = cartItem.querySelector('.cart-quantity-input');
        const currentQuantity = parseInt(quantityInput.value);
        const maxQuantity = parseInt(quantityInput.max) || 99;

        if (currentQuantity < maxQuantity) {
            const newQuantity = currentQuantity + 1;
            quantityInput.value = newQuantity;
            this.updateItemQuantity(cartItem, newQuantity);
        }
    }

    updateQuantityFromInput(input) {
        const cartItem = input.closest('.cart-item');
        const newQuantity = parseInt(input.value);

        if (newQuantity >= 1) {
            this.updateItemQuantity(cartItem, newQuantity);
        } else {
            input.value = 1;
        }
    }

    updateItemQuantity(cartItem, quantity) {
        const productId = cartItem.dataset.productId;

        if (window.cartManager) {
            window.cartManager.updateQuantity(productId, quantity);
        }

        // 更新小计显示
        const cart = window.cartManager.cart;
        const item = cart.find(item => item.id === productId);
        if (item) {
            const subtotalElement = cartItem.querySelector('.subtotal-price');
            if (subtotalElement) {
                subtotalElement.textContent = `¥${(item.price * quantity).toFixed(2)}`;
            }
        }

        this.updateSummary();
    }

    removeItem(cartItem) {
        const productId = cartItem.dataset.productId;

        if (window.cartManager) {
            window.cartManager.removeFromCart(productId);
        }

        // 添加移除动画
        cartItem.classList.add('slide-out');
        setTimeout(() => {
            cartItem.remove();
            this.updateSummary();
        }, 300);
    }

    batchDelete() {
        const selectedCheckboxes = document.querySelectorAll('.cart-item-checkbox input:checked');

        if (selectedCheckboxes.length === 0) {
            window.cartManager?.showNotification('请先选择要删除的商品', 'info');
            return;
        }

        if (confirm(`确定要删除选中的 ${selectedCheckboxes.length} 件商品吗？`)) {
            selectedCheckboxes.forEach(checkbox => {
                const cartItem = checkbox.closest('.cart-item');
                const productId = cartItem.dataset.productId;
                window.cartManager?.removeFromCart(productId);
            });
        }
    }

    batchAddToWishlist() {
        const selectedCheckboxes = document.querySelectorAll('.cart-item-checkbox input:checked');

        if (selectedCheckboxes.length === 0) {
            window.cartManager?.showNotification('请先选择要收藏的商品', 'info');
            return;
        }

        selectedCheckboxes.forEach(checkbox => {
            const cartItem = checkbox.closest('.cart-item');
            const productId = cartItem.dataset.productId;
            const cart = window.cartManager.cart;
            const item = cart.find(item => item.id === productId);

            if (item) {
                window.cartManager?.toggleWishlist(item);
            }
        });

        window.cartManager?.showNotification(`已将 ${selectedCheckboxes.length} 件商品添加到心愿单`, 'success');
    }

    clearCart() {
        if (confirm('确定要清空购物车吗？')) {
            window.cartManager?.clearCart();
        }
    }

    updateBatchButtons() {
        const selectedCheckboxes = document.querySelectorAll('.cart-item-checkbox input:checked');
        const batchDeleteBtn = document.querySelector('.batch-delete-btn');

        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = selectedCheckboxes.length === 0;
        }
    }

    toggleCouponSection() {
        const couponHeader = document.querySelector('.coupon-header');
        const couponBody = document.querySelector('.coupon-body');
        const couponToggle = document.querySelector('.coupon-toggle');

        if (couponBody && couponToggle) {
            const isExpanded = couponHeader.getAttribute('aria-expanded') === 'true';

            couponHeader.setAttribute('aria-expanded', !isExpanded);
            couponBody.style.display = isExpanded ? 'none' : 'block';
            couponToggle.style.transform = isExpanded ? 'rotate(0deg)' : 'rotate(180deg)';
        }
    }

    applyCouponCode() {
        const couponInput = document.querySelector('.coupon-code');
        const code = couponInput.value.trim().toUpperCase();

        if (!code) {
            window.cartManager?.showNotification('请输入优惠码', 'error');
            return;
        }

        this.applyCoupon(code);
    }

    applyCoupon(code) {
        const coupon = this.coupons.find(c => c.code === code);

        if (!coupon) {
            window.cartManager?.showNotification('优惠码无效', 'error');
            return;
        }

        const subtotal = this.getSubtotal();

        if (coupon.minAmount && subtotal < coupon.minAmount) {
            window.cartManager?.showNotification(`订单金额需满 ¥${coupon.minAmount} 才能使用此优惠券`, 'error');
            return;
        }

        this.appliedCoupon = coupon;
        this.updateSummary();
        window.cartManager?.showNotification(`优惠券 "${coupon.name}" 已应用`, 'success');
    }

    getSubtotal() {
        const selectedCheckboxes = document.querySelectorAll('.cart-item-checkbox input:checked');
        const cart = window.cartManager.cart;
        let subtotal = 0;

        selectedCheckboxes.forEach(checkbox => {
            const cartItem = checkbox.closest('.cart-item');
            const productId = cartItem.dataset.productId;
            const item = cart.find(item => item.id === productId);

            if (item) {
                subtotal += item.price * item.quantity;
            }
        });

        return subtotal;
    }

    quickAddRecommended(recommendedItem) {
        const link = recommendedItem.querySelector('a');
        const href = link?.href;
        const productId = href?.match(/id=(\d+)/)?.[1];

        if (productId) {
            const productData = {
                id: productId,
                name: recommendedItem.querySelector('.recommended-name')?.textContent || '推荐商品',
                price: parseFloat(recommendedItem.querySelector('.recommended-price')?.textContent.replace('¥', '') || '0'),
                image: recommendedItem.querySelector('.recommended-img')?.src || '',
                quantity: 1,
                specs: {}
            };

            window.cartManager?.addToCart(productData);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待购物车管理器初始化完成
    setTimeout(() => {
        if (window.cartManager) {
            window.cartPage = new CartPage();
        }
    }, 100);
});
