/**
 * 统一卡片样式 - COSPlay购物网站
 * 包含商品卡片、订单卡片、用户卡片等所有卡片样式
 */

/* ===== CSS变量定义 ===== */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* ===== 基础卡片样式 ===== */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
    border-color: var(--primary-color);
}

/* ===== 商品卡片样式 ===== */
.product-card {
    background: white;
    border-radius: 16px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    border: 1px solid #f0f0f0;
    text-decoration: none;
    color: inherit;
    display: block;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
    text-decoration: none;
    color: inherit;
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
    background: #f8f9fa;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--primary-color, #007bff);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.product-badge.new {
    background: #28a745;
}

.product-badge.hot {
    background: #dc3545;
}

.product-badge.sale {
    background: #ffc107;
    color: #212529;
    top: 12px;
    left: auto;
    right: 12px;
}

.product-info {
    padding: 20px;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-origin {
    font-size: 0.9rem;
    color: #666;
    margin: 0 0 12px 0;
}

.product-price-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color, #007bff);
}

.product-price-original {
    font-size: 0.9rem;
    color: #999;
    text-decoration: line-through;
    margin-left: 8px;
}

.add-to-cart {
    background: var(--primary-color, #007bff);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.add-to-cart:hover {
    background: var(--primary-dark, #0056b3);
    transform: scale(1.1);
}

/* ===== 商品网格布局 ===== */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 30px;
}

/* ===== 订单卡片样式 ===== */
.order-card {
    background: white;
    border-radius: 12px;
    border: 2px solid #f8f9fa;
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.order-card:hover {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.order-header {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.order-number,
.order-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #6c757d;
}

.order-items {
    padding: 20px;
}

.order-item {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #333;
}

.item-origin {
    font-size: 0.9rem;
    color: #666;
    margin: 0 0 8px 0;
}

.item-specs {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;
}

.spec {
    font-size: 0.8rem;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 12px;
    color: #666;
}

.item-quantity {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.item-price {
    font-weight: 600;
    color: var(--primary-color, #007bff);
    font-size: 1.1rem;
}

.order-footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.order-total {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.total-label {
    color: #666;
}

.total-amount {
    font-weight: 700;
    color: #333;
    font-size: 1.2rem;
}

.total-note {
    color: #999;
    font-size: 0.9rem;
}

.order-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* ===== 用户卡片样式 ===== */
.user-profile-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
}

.profile-background {
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, #6f42c1 100%);
    position: relative;
    overflow: hidden;
}

.profile-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.profile-content {
    padding: 20px;
    position: relative;
    margin-top: -40px;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid white;
    overflow: hidden;
    margin: 0 auto 16px;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #666;
}

/* ===== 状态徽章样式 ===== */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.processing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.shipped {
    background: #d4edda;
    color: #155724;
}

.status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }

    .product-image-container {
        height: 240px;
    }

    .product-info {
        padding: 16px;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .item-image {
        width: 100%;
        height: 200px;
    }

    .order-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .product-card {
        border-radius: 12px;
    }

    .product-image-container {
        height: 200px;
    }
}