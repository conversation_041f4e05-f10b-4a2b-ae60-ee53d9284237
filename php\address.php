<?php
/**
 * COSPlay购物网站 - 收货地址管理页面
 * 管理用户的收货地址信息
 */

// 引入配置文件
require_once 'config/database.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php');
}

// 页面信息设置
$page_title = '收货地址管理 - COSPlay购物网站';
$page_description = '管理您的收货地址信息';

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.address-title {
    color: #212529 !important;
}

.modal-title {
    color: #212529 !important;
}

.recipient-name {
    color: #212529 !important;
}

.recipient-phone {
    color: #6c757d !important;
}

.address-text {
    color: #495057 !important;
}

.address-postcode {
    color: #6c757d !important;
}

.form-label {
    color: #212529 !important;
}

.required {
    color: #dc3545 !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.add-address-btn, .btn-primary {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.btn-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.action-btn.edit-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.action-btn.delete-btn {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.operation-btn {
    background-color: #f8f9fa !important;
    color: #495057 !important;
}

.operation-btn:disabled {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保表单文字可见 */
.form-input, .form-textarea, .region-select {
    color: #495057 !important;
    background-color: #ffffff !important;
}

/* 确保卡片背景和文字可见 */
.address-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* 确保标签可见 */
.address-tag {
    color: #ffffff !important;
}

.address-tag.home {
    background-color: #28a745 !important;
}

.address-tag.company {
    background-color: #007bff !important;
}

.address-tag.school {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.tag-label {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

.tag-option input:checked + .tag-label {
    color: #ffffff !important;
    background-color: #007bff !important;
}

/* 确保默认徽章可见 */
.default-badge {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* 确保提示信息可见 */
.address-limit-notice {
    color: #6c757d !important;
    background-color: #f8f9fa !important;
}

/* 确保复选框标签可见 */
.checkbox-label {
    color: #495057 !important;
}
</style>

<div class="address-page">
    <div class="container">
        <div class="address-header">
            <h1 class="address-title">
                <i class="fas fa-map-marker-alt"></i>
                收货地址管理
            </h1>
            <button class="add-address-btn" id="addAddressBtn">
                <i class="fas fa-plus"></i>
                添加新地址
            </button>
        </div>

        <div class="address-list">
            <!-- 地址1 - 默认地址 -->
            <div class="address-card" data-address-id="1">
                <div class="address-header-info">
                    <div class="address-recipient">
                        <span class="recipient-name">张三</span>
                        <span class="recipient-phone">138****8888</span>
                        <span class="default-badge">默认地址</span>
                    </div>
                    <div class="address-actions">
                        <button class="action-btn edit-btn" onclick="editAddress(1)">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteAddress(1)">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
                
                <div class="address-content">
                    <div class="address-detail">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="address-text">北京市朝阳区某某街道某某小区1号楼101室</span>
                    </div>
                    <div class="address-postcode">邮编: 100000</div>
                </div>
                
                <div class="address-footer">
                    <div class="address-tags">
                        <span class="address-tag home">家</span>
                    </div>
                    <div class="address-operations">
                        <button class="operation-btn" onclick="setDefaultAddress(1)" disabled>
                            <i class="fas fa-star"></i>
                            默认地址
                        </button>
                    </div>
                </div>
            </div>

            <!-- 地址2 -->
            <div class="address-card" data-address-id="2">
                <div class="address-header-info">
                    <div class="address-recipient">
                        <span class="recipient-name">李四</span>
                        <span class="recipient-phone">139****9999</span>
                    </div>
                    <div class="address-actions">
                        <button class="action-btn edit-btn" onclick="editAddress(2)">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteAddress(2)">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
                
                <div class="address-content">
                    <div class="address-detail">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="address-text">上海市浦东新区某某路某某号某某大厦2001室</span>
                    </div>
                    <div class="address-postcode">邮编: 200000</div>
                </div>
                
                <div class="address-footer">
                    <div class="address-tags">
                        <span class="address-tag company">公司</span>
                    </div>
                    <div class="address-operations">
                        <button class="operation-btn" onclick="setDefaultAddress(2)">
                            <i class="far fa-star"></i>
                            设为默认
                        </button>
                    </div>
                </div>
            </div>

            <!-- 地址3 -->
            <div class="address-card" data-address-id="3">
                <div class="address-header-info">
                    <div class="address-recipient">
                        <span class="recipient-name">王五</span>
                        <span class="recipient-phone">137****7777</span>
                    </div>
                    <div class="address-actions">
                        <button class="action-btn edit-btn" onclick="editAddress(3)">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteAddress(3)">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
                
                <div class="address-content">
                    <div class="address-detail">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="address-text">广州市天河区某某街道某某广场B座1502室</span>
                    </div>
                    <div class="address-postcode">邮编: 510000</div>
                </div>
                
                <div class="address-footer">
                    <div class="address-tags">
                        <span class="address-tag school">学校</span>
                    </div>
                    <div class="address-operations">
                        <button class="operation-btn" onclick="setDefaultAddress(3)">
                            <i class="far fa-star"></i>
                            设为默认
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地址限制提示 -->
        <div class="address-limit-notice">
            <i class="fas fa-info-circle"></i>
            <span>您最多可以添加10个收货地址</span>
        </div>
    </div>
</div>

<!-- 添加/编辑地址模态框 -->
<div class="modal" id="addressModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">添加收货地址</h3>
            <button class="modal-close" id="closeModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form class="address-form" id="addressForm">
            <input type="hidden" id="addressId" name="address_id">
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">收货人姓名 <span class="required">*</span></label>
                    <input type="text" id="recipientName" name="recipient_name" class="form-input" 
                           placeholder="请输入收货人姓名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">手机号码 <span class="required">*</span></label>
                    <input type="tel" id="recipientPhone" name="recipient_phone" class="form-input" 
                           placeholder="请输入手机号码" required>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">所在地区 <span class="required">*</span></label>
                <div class="region-selector">
                    <select id="province" name="province" class="region-select" required>
                        <option value="">请选择省份</option>
                        <option value="北京市">北京市</option>
                        <option value="上海市">上海市</option>
                        <option value="广东省">广东省</option>
                        <option value="浙江省">浙江省</option>
                        <option value="江苏省">江苏省</option>
                    </select>
                    <select id="city" name="city" class="region-select" required>
                        <option value="">请选择城市</option>
                    </select>
                    <select id="district" name="district" class="region-select" required>
                        <option value="">请选择区县</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">详细地址 <span class="required">*</span></label>
                <textarea id="detailAddress" name="detail_address" class="form-textarea" 
                          placeholder="请输入详细地址，如街道、门牌号、楼层等" required></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">邮政编码</label>
                    <input type="text" id="postcode" name="postcode" class="form-input" 
                           placeholder="请输入邮政编码">
                </div>
                <div class="form-group">
                    <label class="form-label">地址标签</label>
                    <div class="tag-selector">
                        <label class="tag-option">
                            <input type="radio" name="address_tag" value="home" checked>
                            <span class="tag-label home">家</span>
                        </label>
                        <label class="tag-option">
                            <input type="radio" name="address_tag" value="company">
                            <span class="tag-label company">公司</span>
                        </label>
                        <label class="tag-option">
                            <input type="radio" name="address_tag" value="school">
                            <span class="tag-label school">学校</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="isDefault" name="is_default" class="checkbox">
                    <span class="checkmark"></span>
                    设为默认地址
                </label>
            </div>

            <div class="form-actions">
                <button type="button" class="btn-secondary" id="cancelBtn">取消</button>
                <button type="submit" class="btn-primary">保存地址</button>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addressModal');
    const addBtn = document.getElementById('addAddressBtn');
    const closeBtn = document.getElementById('closeModal');
    const cancelBtn = document.getElementById('cancelBtn');
    const form = document.getElementById('addressForm');

    // 打开添加地址模态框
    addBtn.addEventListener('click', function() {
        openModal('add');
    });

    // 关闭模态框
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // 点击模态框外部关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // 表单提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        saveAddress();
    });

    // 地区联动
    setupRegionSelector();
});

// 打开模态框
function openModal(mode, addressId = null) {
    const modal = document.getElementById('addressModal');
    const title = document.getElementById('modalTitle');
    const form = document.getElementById('addressForm');
    
    if (mode === 'add') {
        title.textContent = '添加收货地址';
        form.reset();
        document.getElementById('addressId').value = '';
    } else if (mode === 'edit') {
        title.textContent = '编辑收货地址';
        loadAddressData(addressId);
    }
    
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('addressModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 编辑地址
function editAddress(addressId) {
    openModal('edit', addressId);
}

// 删除地址
function deleteAddress(addressId) {
    if (confirm('确定要删除这个地址吗？')) {
        // 这里应该调用后端API删除地址
        const addressCard = document.querySelector(`[data-address-id="${addressId}"]`);
        if (addressCard) {
            addressCard.remove();
        }
        alert('地址删除成功！');
    }
}

// 设置默认地址
function setDefaultAddress(addressId) {
    if (confirm('确定要设置为默认地址吗？')) {
        // 移除其他地址的默认标识
        document.querySelectorAll('.default-badge').forEach(badge => {
            badge.remove();
        });
        
        document.querySelectorAll('.operation-btn').forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="far fa-star"></i> 设为默认';
        });
        
        // 设置新的默认地址
        const targetCard = document.querySelector(`[data-address-id="${addressId}"]`);
        const recipientDiv = targetCard.querySelector('.address-recipient');
        const defaultBadge = document.createElement('span');
        defaultBadge.className = 'default-badge';
        defaultBadge.textContent = '默认地址';
        recipientDiv.appendChild(defaultBadge);
        
        const operationBtn = targetCard.querySelector('.operation-btn');
        operationBtn.disabled = true;
        operationBtn.innerHTML = '<i class="fas fa-star"></i> 默认地址';
        
        alert('默认地址设置成功！');
    }
}

// 加载地址数据（编辑时使用）
function loadAddressData(addressId) {
    // 这里应该从后端获取地址数据
    // 模拟数据
    const addressData = {
        1: {
            recipient_name: '张三',
            recipient_phone: '13888888888',
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail_address: '某某街道某某小区1号楼101室',
            postcode: '100000',
            address_tag: 'home',
            is_default: true
        },
        2: {
            recipient_name: '李四',
            recipient_phone: '13999999999',
            province: '上海市',
            city: '上海市',
            district: '浦东新区',
            detail_address: '某某路某某号某某大厦2001室',
            postcode: '200000',
            address_tag: 'company',
            is_default: false
        },
        3: {
            recipient_name: '王五',
            recipient_phone: '13777777777',
            province: '广东省',
            city: '广州市',
            district: '天河区',
            detail_address: '某某街道某某广场B座1502室',
            postcode: '510000',
            address_tag: 'school',
            is_default: false
        }
    };
    
    const data = addressData[addressId];
    if (data) {
        document.getElementById('addressId').value = addressId;
        document.getElementById('recipientName').value = data.recipient_name;
        document.getElementById('recipientPhone').value = data.recipient_phone;
        document.getElementById('province').value = data.province;
        document.getElementById('detailAddress').value = data.detail_address;
        document.getElementById('postcode').value = data.postcode;
        document.querySelector(`input[name="address_tag"][value="${data.address_tag}"]`).checked = true;
        document.getElementById('isDefault').checked = data.is_default;
        
        // 触发地区联动
        updateCityOptions(data.province, data.city);
        updateDistrictOptions(data.city, data.district);
    }
}

// 保存地址
function saveAddress() {
    const formData = new FormData(document.getElementById('addressForm'));
    const addressId = formData.get('address_id');
    
    // 这里应该调用后端API保存地址
    console.log('保存地址数据:', Object.fromEntries(formData));
    
    alert(addressId ? '地址修改成功！' : '地址添加成功！');
    closeModal();
    
    // 刷新页面或动态更新地址列表
    location.reload();
}

// 设置地区联动
function setupRegionSelector() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const districtSelect = document.getElementById('district');
    
    provinceSelect.addEventListener('change', function() {
        updateCityOptions(this.value);
    });
    
    citySelect.addEventListener('change', function() {
        updateDistrictOptions(this.value);
    });
}

// 更新城市选项
function updateCityOptions(province, selectedCity = '') {
    const citySelect = document.getElementById('city');
    const districtSelect = document.getElementById('district');
    
    // 清空城市和区县选项
    citySelect.innerHTML = '<option value="">请选择城市</option>';
    districtSelect.innerHTML = '<option value="">请选择区县</option>';
    
    // 模拟城市数据
    const cities = {
        '北京市': ['北京市'],
        '上海市': ['上海市'],
        '广东省': ['广州市', '深圳市', '珠海市', '东莞市'],
        '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市'],
        '江苏省': ['南京市', '苏州市', '无锡市', '常州市']
    };
    
    if (cities[province]) {
        cities[province].forEach(city => {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            if (city === selectedCity) {
                option.selected = true;
            }
            citySelect.appendChild(option);
        });
    }
}

// 更新区县选项
function updateDistrictOptions(city, selectedDistrict = '') {
    const districtSelect = document.getElementById('district');
    
    // 清空区县选项
    districtSelect.innerHTML = '<option value="">请选择区县</option>';
    
    // 模拟区县数据
    const districts = {
        '北京市': ['朝阳区', '海淀区', '西城区', '东城区', '丰台区'],
        '上海市': ['浦东新区', '黄浦区', '静安区', '徐汇区', '长宁区'],
        '广州市': ['天河区', '越秀区', '荔湾区', '海珠区', '白云区'],
        '深圳市': ['南山区', '福田区', '罗湖区', '宝安区', '龙岗区']
    };
    
    if (districts[city]) {
        districts[city].forEach(district => {
            const option = document.createElement('option');
            option.value = district;
            option.textContent = district;
            if (district === selectedDistrict) {
                option.selected = true;
            }
            districtSelect.appendChild(option);
        });
    }
}
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
