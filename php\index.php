<?php
/**
 * COSPlay购物网站 - 首页
 * 展示热门商品、分类导航和网站特色
 */

// 页面信息设置
$page_title = 'COSPlay购物网站 - 首页';
$page_description = 'COSPlay - 专业的角色扮演服装购物平台，提供高品质的动漫、游戏角色服装及配饰';

// 模拟数据 - 暂时不连接数据库
$homepage_data = [
    'featured_products' => [
        [
            'id' => 1001,
            'name' => '原神 - 刻晴星霜华裳',
            'slug' => 'genshin-keqing-costume',
            'price' => 599.00,
            'sale_price' => 499.00,
            'category_name' => '原神',
            'primary_image' => 'http://img.alicdn.com/img/i4/3681083810/O1CN01XHuYwz1e11vURSy3p_!!3681083810-0-alimamacc.jpg',
            'featured' => true
        ],
        [
            'id' => 1002,
            'name' => '英雄联盟 - 阿狸',
            'slug' => 'lol-ahri-costume',
            'price' => 649.00,
            'sale_price' => null,
            'category_name' => '英雄联盟',
            'primary_image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
            'featured' => true
        ],
        [
            'id' => 1003,
            'name' => '鬼灭之刃 - 炭治郎',
            'slug' => 'kimetsu-tanjiro-costume',
            'price' => 599.00,
            'sale_price' => null,
            'category_name' => '鬼灭之刃',
            'primary_image' => 'http://img.alicdn.com/img/i4/3681083810/O1CN01XHuYwz1e11vURSy3p_!!3681083810-0-alimamacc.jpg',
            'featured' => true
        ],
        [   
            'id' => 1004,
            'name' => '赛博朋克2077',
            'slug' => 'cyberpunk-2077-costume',
            'price' => 699.00,
            'sale_price' => 599.00,
            'category_name' => '赛博朋克',
            'primary_image' => 'http://img.alicdn.com/img/i4/3681083810/O1CN01XHuYwz1e11vURSy3p_!!3681083810-0-alimamacc.jpg',
            'featured' => true
        ]
    ],
    'latest_products' => [
        [
            'id' => 2001,
            'name' => '宝可梦 - 皮卡丘',
            'slug' => 'pokemon-pikachu-costume',
            'price' => 299.00,
            'sale_price' => null,
            'category_name' => '宝可梦',
            'primary_image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
            'featured' => false
        ],
        [
            'id' => 2002,
            'name' => '海贼王 - 路飞',
            'slug' => 'onepiece-luffy-costume',
            'price' => 399.00,
            'sale_price' => null,
            'category_name' => '海贼王',
            'primary_image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
            'featured' => false
        ],
        [
            'id' => 2003,
            'name' => '火影忍者 - 鸣人',
            'slug' => 'naruto-naruto-costume',
            'price' => 459.00,
            'sale_price' => 399.00,
            'category_name' => '火影忍者',
            'primary_image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
            'featured' => false
        ],
        [
            'id' => 2004,
            'name' => '进击的巨人 - 兵长',
            'slug' => 'aot-levi-costume',
            'price' => 549.00,
            'sale_price' => null,
            'category_name' => '进击的巨人',
            'primary_image' => 'http://img.alicdn.com/img/i2/29367245/O1CN01nRMsCq23OGWzgoPvB_!!4611686018427386829-0-saturn_solar.jpg',
            'featured' => false
        ]
    ],
    'generated_at' => time()
];

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 确保Font Awesome正确加载 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<!-- 确保文字可见的紧急修复 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.hero-title {
    color: #212529 !important;
}

.hero-subtitle {
    color: #6c757d !important;
}

.section-title {
    color: #212529 !important;
}

.category-name {
    color: #495057 !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.btn-primary {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.cta-button {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 修复商品卡片样式 */
.product-card {
    background: #ffffff !important;
    color: #212529 !important;
    border: 1px solid #e9ecef !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    overflow: hidden !important;
    position: relative !important;
}

.product-card:hover {
    color: #212529 !important;
    text-decoration: none !important;
    transform: none !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
    border-color: #007bff !important;
}

.product-info {
    background: #ffffff !important;
    color: #212529 !important;
    padding: 20px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    min-height: 140px !important;
    box-sizing: border-box !important;
    position: relative !important;
    z-index: 10 !important;
}

/* 修复商品信息区域文字颜色 */
.product-info h1, .product-info h2, .product-info h3, .product-info h4, .product-info h5, .product-info h6 {
    color: #212529 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-info p {
    color: #6c757d !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-name {
    color: #212529 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
    margin: 0 0 8px 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.product-origin {
    color: #6c757d !important;
    font-size: 0.9rem !important;
    margin: 0 0 12px 0 !important;
    font-weight: 500 !important;
    line-height: 1.3 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-price {
    color: #212529 !important;
    font-weight: 800 !important;
    font-size: 1.25rem !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-price-original {
    color: #8e9ba8 !important;
    text-decoration: line-through !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    margin: 0 0 0 8px !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-price-row {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-top: auto !important;
    padding-top: 8px !important;
    flex-shrink: 0 !important;
    gap: 8px !important;
}

.product-price-container {
    display: flex !important;
    align-items: baseline !important;
    gap: 6px !important;
    flex: 1 !important;
    min-width: 0 !important;
}

.product-badge {
    background-color: #007bff !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    position: absolute !important;
    top: 12px !important;
    left: 12px !important;
    z-index: 2 !important;
}

.product-badge.new {
    background-color: #28a745 !important;
}

.product-badge.hot {
    background-color: #dc3545 !important;
}

.product-badge.sale {
    background-color: #ffc107 !important;
    color: #212529 !important;
    top: 12px !important;
    left: auto !important;
    right: 12px !important;
}

.add-to-cart {
    background-color: #007bff !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
}

.add-to-cart:hover {
    background-color: #0056b3 !important;
    color: #ffffff !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
}

.add-to-cart i {
    color: #ffffff !important;
    font-size: 14px !important;
    line-height: 1 !important;
}

/* 修复Font Awesome图标显示问题 */
.fas, .far, .fab, .fal, .fad {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

.far {
    font-weight: 400 !important;
}

.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* 确保所有图标都可见 */
i[class*="fa-"] {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* 特别修复分类图标 */
.category-icon {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    color: #007bff !important;
}

/* 修复购物车图标 */
.add-to-cart i,
.fas.fa-shopping-cart {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    color: #ffffff !important;
}

/* 修复箭头图标 */
.fas.fa-arrow-right {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
}

/* 修复其他图标 */
.fas.fa-mask,
.fas.fa-box-open {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
}

/* 确保Hero区域文字可见 */
.hero-content {
    color: #212529 !important;
}

.hero-title {
    color: #212529 !important;
    font-weight: 700 !important;
    font-size: 2.5rem !important;
    margin-bottom: 16px !important;
}

.hero-subtitle {
    color: #6c757d !important;
    font-size: 1.2rem !important;
    margin-bottom: 24px !important;
}

.cta-button {
    background-color: #007bff !important;
    color: #ffffff !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    display: inline-block !important;
    transition: all 0.3s ease !important;
}

.cta-button:hover {
    background-color: #0056b3 !important;
    color: #ffffff !important;
    text-decoration: none !important;
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.3) !important;
}

/* 确保分类导航文字可见 */
.category-item {
    color: #495057 !important;
    text-decoration: none !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 20px !important;
    border-radius: 12px !important;
    background-color: #ffffff !important;
    border: 1px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.category-item:hover {
    color: #007bff !important;
    text-decoration: none !important;
    background-color: #f8f9fa !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15) !important;
}

.category-icon {
    color: #007bff !important;
    font-size: 2rem !important;
    margin-bottom: 8px !important;
}

.category-name {
    color: #495057 !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    text-align: center !important;
}

.category-item:hover .category-name {
    color: #007bff !important;
}

/* 确保区块标题文字可见 */
.section-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 24px !important;
}

.section-title {
    color: #212529 !important;
    font-weight: 700 !important;
    font-size: 1.8rem !important;
    margin: 0 !important;
}

.view-all {
    color: #007bff !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    transition: all 0.3s ease !important;
}

.view-all:hover {
    color: #0056b3 !important;
    text-decoration: none !important;
}

.view-all i {
    color: #007bff !important;
    font-size: 14px !important;
    margin-left: 4px !important;
}

.view-all:hover i {
    color: #0056b3 !important;
}

/* 确保空状态文字可见 */
.no-products {
    text-align: center !important;
    padding: 60px 20px !important;
    color: #6c757d !important;
    background-color: #f8f9fa !important;
    border-radius: 12px !important;
    border: 2px dashed #dee2e6 !important;
}

.no-products i {
    font-size: 3rem !important;
    color: #dee2e6 !important;
    margin-bottom: 16px !important;
    display: block !important;
}

.no-products p {
    color: #6c757d !important;
    font-size: 1.1rem !important;
    margin: 0 !important;
}

/* 响应式文字调整 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .section-title {
        font-size: 1.5rem !important;
    }

    .product-info {
        padding: 16px !important;
        min-height: 120px !important;
    }

    .product-name {
        font-size: 1rem !important;
        margin-bottom: 6px !important;
    }

    .product-origin {
        font-size: 0.85rem !important;
        margin-bottom: 10px !important;
    }

    .product-price {
        font-size: 1.1rem !important;
    }

    .product-price-original {
        font-size: 0.9rem !important;
    }

    .add-to-cart {
        width: 36px !important;
        height: 36px !important;
        min-width: 36px !important;
        min-height: 36px !important;
    }

    .add-to-cart i {
        font-size: 13px !important;
    }

    .category-name {
        font-size: 0.8rem !important;
    }

    .category-icon {
        font-size: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem !important;
    }

    .hero-subtitle {
        font-size: 0.9rem !important;
    }

    .section-title {
        font-size: 1.3rem !important;
    }

    .product-info {
        padding: 14px !important;
        min-height: 110px !important;
    }

    .product-name {
        font-size: 0.95rem !important;
        margin-bottom: 5px !important;
    }

    .product-origin {
        font-size: 0.8rem !important;
        margin-bottom: 8px !important;
    }

    .product-price {
        font-size: 1rem !important;
    }

    .product-price-original {
        font-size: 0.85rem !important;
    }

    .product-price-row {
        gap: 6px !important;
        padding-top: 6px !important;
    }

    .add-to-cart {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
    }

    .add-to-cart i {
        font-size: 12px !important;
    }

    .category-name {
        font-size: 0.75rem !important;
    }

    .category-icon {
        font-size: 1.3rem !important;
    }

    .view-all {
        font-size: 0.9rem !important;
    }
}

/* 确保所有链接文字可见 */
a {
    color: inherit !important;
    text-decoration: none !important;
}

a:hover {
    text-decoration: none !important;
}

/* 优化 product-card 链接的文字颜色 */
a.product-card {
    color: #212529 !important;
}

a.product-card:hover {
    color: #212529 !important;
}

/* 确保 product-card 内所有文字元素都有正确的颜色 */
.product-card,
.product-card *,
a.product-card,
a.product-card * {
    color: inherit !important;
}

/* 具体的文字元素颜色设置 */
.product-card .product-name,
.product-card h1,
.product-card h2,
.product-card h3,
.product-card h4,
.product-card h5,
.product-card h6 {
    color: #212529 !important;
}

.product-card .product-origin,
.product-card p {
    color: #6c757d !important;
}

.product-card .product-price,
.product-card span.product-price {
    color: #212529 !important;
}

.product-card .product-price-original,
.product-card span.product-price-original {
    color: #8e9ba8 !important;
}

a.category-item {
    color: #495057 !important;
}

a.category-item:hover {
    color: #007bff !important;
}

/* 全局文字清晰修复 - 移除所有模糊效果 */
* {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    text-shadow: none !important;
    filter: none !important;
    -webkit-filter: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 移除所有可能导致模糊的变换 */
*:not(.add-to-cart):not(.cta-button):not(.category-item):not(.view-all) {
    transform: none !important;
    -webkit-transform: none !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
    will-change: auto !important;
}

/* 文字清晰显示设置 */
.product-card h1, .product-card h2, .product-card h3, .product-card h4, .product-card h5, .product-card h6 {
    text-rendering: optimizeSpeed !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
}

/* 商品卡片所有文字清晰显示 */
.product-card * {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    font-smooth: never !important;
    -webkit-font-feature-settings: "kern" 0 !important;
}

/* 强制商品信息区域所有文字可见 - 最高优先级 */
.product-card .product-info {
    background-color: transparent !important;
}

.product-card .product-name {
    color: #212529 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-card .product-origin {
    color: #6c757d !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.product-card .product-price {
    color: #212529 !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 确保按钮文字可见 */
.product-card .add-to-cart,
.product-card .add-to-cart * {
    color: #ffffff !important;
    background-color: #007bff !important;
}

/* 防止价格文字模糊的专用规则 */
.product-price,
span.product-price,
.product-info .product-price,
.product-card .product-price {
    color: #212529 !important;
    font-weight: 800 !important;
    font-size: 1.25rem !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
    transform: none !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    will-change: auto !important;
    filter: none !important;
    -webkit-filter: none !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 防止整个商品卡片模糊 - 移除所有3D变换 */
.product-card {
    transform: none !important;
    -webkit-transform: none !important;
    backface-visibility: visible !important;
    -webkit-backface-visibility: visible !important;
    perspective: none !important;
    -webkit-perspective: none !important;
}

.product-card:hover {
    transform: none !important;
    -webkit-transform: none !important;
}

/* 商品网格布局 */
.product-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
    gap: 24px !important;
    margin-top: 30px !important;
}

.product-image-container {
    position: relative !important;
    width: 100% !important;
    height: 280px !important;
    overflow: hidden !important;
    background: #f8f9fa !important;
    border-radius: 16px 16px 0 0 !important;
}

.product-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s ease !important;
}

.product-card:hover .product-image {
    transform: none !important;
}

/* 响应式网格调整 */
@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
        gap: 20px !important;
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)) !important;
        gap: 16px !important;
    }

    .product-card {
        min-height: 380px !important;
    }

    .product-image-container {
        height: 240px !important;
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 12px !important;
    }

    .product-card {
        min-height: 340px !important;
    }

    .product-image-container {
        height: 200px !important;
    }
}

@media (max-width: 360px) {
    .product-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .product-card {
        min-height: 380px !important;
    }

    .product-image-container {
        height: 240px !important;
    }
}
</style>

<!-- 首页专用样式优化 -->
<style>
/* 导航栏和布局修复 */
.navbar {
    margin-bottom: 0;
    box-shadow: none;
    position: relative;
    z-index: 30;
}

header {
    position: relative;
    z-index: 100;
}

main {
    position: relative;
    z-index: 10;
    padding-top: 30px;
}

/* 统一container宽度 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

.navbar .container,
.category-nav .container,
.hero .container,
.section .container {
    width: 100%;
    max-width: 1200px;
}

/* 移动设备调整 */
@media (max-width: 768px) {
    .navbar {
        position: relative;
        z-index: 100;
    }

    main, .category-nav, .hero {
        margin-top: 0;
        padding-top: 15px;
    }

    .container {
        padding: 0 15px;
    }
}
</style>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">发现你的专属角色</h1>
            <p class="hero-subtitle">精选高品质COSplay服装，让每一次扮演都完美无缺</p>
            <a href="category.php?featured=true&source=hero" class="cta-button">开始购物</a>
        </div>
        <div class="hero-image">
            <i class="fas fa-mask"></i>
        </div>
    </div>
</section>

<!-- Category Navigation -->
<section class="category-nav">
    <div class="container">
        <div class="category-list">
            <a href="category.php?type=anime&source=nav" class="category-item">
                <i class="fas fa-tv category-icon"></i>
                <span class="category-name">动漫角色</span>
            </a>
            <a href="category.php?type=game&source=nav" class="category-item">
                <i class="fas fa-gamepad category-icon"></i>
                <span class="category-name">游戏角色</span>
            </a>
            <a href="category.php?type=movie&source=nav" class="category-item">
                <i class="fas fa-film category-icon"></i>
                <span class="category-name">电影角色</span>
            </a>
            <a href="category.php?type=original&source=nav" class="category-item">
                <i class="fas fa-star category-icon"></i>
                <span class="category-name">原创设计</span>
            </a>
            <a href="category.php?type=accessories&source=nav" class="category-item">
                <i class="fas fa-hat-wizard category-icon"></i>
                <span class="category-name">配饰道具</span>
            </a>
            <a href="category.php?type=custom&source=nav" class="category-item">
                <i class="fas fa-paint-brush category-icon"></i>
                <span class="category-name">定制服务</span>
            </a>
        </div>
    </div>
</section>

<!-- Hot Products Section -->
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">热门角色专区</h2>
            <a href="category.php?filter=hot&source=section" class="view-all">查看全部 <i class="fas fa-arrow-right"></i></a>
        </div>
        <div class="product-grid">
            <?php if (!empty($homepage_data['featured_products'])): ?>
                <?php foreach ($homepage_data['featured_products'] as $product): ?>
                    <a href="detail.php?id=<?php echo $product['id']; ?>&name=<?php echo urlencode($product['slug']); ?>&source=hot" class="product-card">
                        <div class="product-image-container">
                            <?php if ($product['featured']): ?>
                                <div class="product-badge">热门</div>
                            <?php endif; ?>
                            <?php if ($product['sale_price']): ?>
                                <div class="product-badge sale">特价</div>
                            <?php endif; ?>
                            <img src="<?php echo $product['primary_image'] ?: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop'; ?>"
                                alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-image" loading="lazy">
                        </div>
                        <div class="product-info">
                            <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                            <p class="product-origin"><?php echo htmlspecialchars($product['category_name'] ?: '未分类'); ?></p>
                            <div class="product-price-row">
                                <div class="product-price-container">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="product-price">¥<?php echo number_format($product['sale_price'], 2); ?></span>
                                        <span class="product-price-original">¥<?php echo number_format($product['price'], 2); ?></span>
                                    <?php else: ?>
                                        <span class="product-price">¥<?php echo number_format($product['price'], 2); ?></span>
                                    <?php endif; ?>
                                </div>
                                <button class="add-to-cart" data-product-id="<?php echo $product['id']; ?>" title="添加到购物车">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- 默认商品展示 -->
                <div class="no-products">
                    <i class="fas fa-box-open"></i>
                    <p>暂无热门商品</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- New Products Section -->
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">新品上市</h2>
            <a href="category.php?filter=new&source=section" class="view-all">查看全部 <i class="fas fa-arrow-right"></i></a>
        </div>
        <div class="product-grid">
            <?php if (!empty($homepage_data['latest_products'])): ?>
                <?php foreach ($homepage_data['latest_products'] as $product): ?>
                    <a href="detail.php?id=<?php echo $product['id']; ?>&name=<?php echo urlencode($product['slug']); ?>&source=new" class="product-card">
                        <div class="product-image-container">
                            <div class="product-badge new">新品</div>
                            <?php if ($product['sale_price']): ?>
                                <div class="product-badge sale">特价</div>
                            <?php endif; ?>
                            <img src="<?php echo $product['primary_image'] ?: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop'; ?>"
                                alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-image" loading="lazy">
                        </div>
                        <div class="product-info">
                            <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                            <p class="product-origin"><?php echo htmlspecialchars($product['category_name'] ?: '未分类'); ?></p>
                            <div class="product-price-row">
                                <div class="product-price-container">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="product-price">¥<?php echo number_format($product['sale_price'], 2); ?></span>
                                        <span class="product-price-original">¥<?php echo number_format($product['price'], 2); ?></span>
                                    <?php else: ?>
                                        <span class="product-price">¥<?php echo number_format($product['price'], 2); ?></span>
                                    <?php endif; ?>
                                </div>
                                <button class="add-to-cart" data-product-id="<?php echo $product['id']; ?>" title="添加到购物车">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- 默认商品展示 -->
                <div class="no-products">
                    <i class="fas fa-box-open"></i>
                    <p>暂无新品上市</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- JavaScript -->
<script src="js/cart-storage.js"></script>
<script src="js/cart-utils.js"></script>
<script src="js/main.js"></script>
<script>
// 首页增强功能
document.addEventListener('DOMContentLoaded', function () {
    // 搜索功能
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function (e) {
            e.preventDefault();
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm.trim()) {
                window.location.href = `category.php?search=${encodeURIComponent(searchTerm)}&source=search`;
            }
        });
    }

    // 图片懒加载优化
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // 修复商品卡片文字样式
    function fixProductCardTextStyles() {
        console.log('开始修复商品卡片文字样式...');

        // 修复所有商品卡片的基础颜色
        document.querySelectorAll('.product-card').forEach(card => {
            card.style.color = '#212529';

            // 修复标题
            const title = card.querySelector('.product-name, h3');
            if (title) {
                title.style.color = '#212529';
                title.style.fontWeight = '600';
                title.style.fontSize = '1.1rem';
                title.style.display = 'block';
                title.style.visibility = 'visible';
                title.style.opacity = '1';
            }

            // 修复分类/来源
            const origin = card.querySelector('.product-origin, p');
            if (origin) {
                origin.style.color = '#6c757d';
                origin.style.display = 'block';
                origin.style.visibility = 'visible';
                origin.style.opacity = '1';
            }

            // 修复商品信息区域
            const productInfo = card.querySelector('.product-info');
            if (productInfo) {
                productInfo.style.color = '#212529';
                productInfo.style.backgroundColor = '#ffffff';
            }
        });

        console.log('商品卡片文字样式修复完成');
    }

    // 修复商品价格显示问题
    function fixProductPrices() {
        console.log('开始修复商品价格显示...');

        // 修复所有价格元素
        document.querySelectorAll('.product-price').forEach(price => {
            price.style.color = '#212529';
            price.style.fontWeight = '800';
            price.style.fontSize = '1.25rem';
            price.style.display = 'inline-block';
            price.style.visibility = 'visible';
            price.style.opacity = '1';
        });

        // 修复原价元素
        document.querySelectorAll('.product-price-original').forEach(originalPrice => {
            originalPrice.style.color = '#8e9ba8';
            originalPrice.style.textDecoration = 'line-through';
            originalPrice.style.fontSize = '1rem';
            originalPrice.style.fontWeight = '500';
            originalPrice.style.display = 'inline-block';
        });

        // 确保商品名称和来源可见
        document.querySelectorAll('.product-name').forEach(name => {
            name.style.color = '#212529';
            name.style.visibility = 'visible';
            name.style.display = 'block';
        });

        document.querySelectorAll('.product-origin').forEach(origin => {
            origin.style.color = '#6c757d';
            origin.style.visibility = 'visible';
            origin.style.display = 'block';
        });

        console.log('商品价格显示修复完成');
    }

    // 修复Font Awesome图标显示问题
    function fixFontAwesomeIcons() {
        console.log('开始修复Font Awesome图标...');

        // 强制设置所有图标的字体
        document.querySelectorAll('i[class*="fa-"], .fas, .far, .fab').forEach(icon => {
            icon.style.fontFamily = '"Font Awesome 6 Free", "Font Awesome 5 Free"';
            icon.style.fontWeight = '900';
            icon.style.display = 'inline-block';
            icon.style.textRendering = 'auto';
            icon.style.webkitFontSmoothing = 'antialiased';
            icon.style.mozOsxFontSmoothing = 'grayscale';
        });

        // 特别处理分类图标
        document.querySelectorAll('.category-icon').forEach(icon => {
            icon.style.fontFamily = '"Font Awesome 6 Free"';
            icon.style.fontWeight = '900';
            icon.style.color = '#007bff';
            icon.style.fontSize = '2rem';
            icon.style.display = 'inline-block';
        });

        // 特别处理购物车图标
        document.querySelectorAll('.add-to-cart i, .fas.fa-shopping-cart').forEach(icon => {
            icon.style.fontFamily = '"Font Awesome 6 Free"';
            icon.style.fontWeight = '900';
            icon.style.color = '#ffffff';
            icon.style.display = 'inline-block';
        });

        console.log('Font Awesome图标修复完成');
    }

    // 页面加载完成后修复价格和图标
    fixProductPrices();
    fixFontAwesomeIcons();

    // 延迟再次修复，确保完全加载
    setTimeout(() => {
        fixProductPrices();
        fixFontAwesomeIcons();
    }, 500);

    // 商品卡片悬停效果 - 移除transform避免模糊
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.12)';
            this.style.borderColor = '#007bff';
        });

        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
            this.style.borderColor = '';
        });
    });

    // 性能监控（仅在调试模式下）
    if (window.location.search.includes('debug=1')) {
        console.log('首页加载完成');
        console.log('缓存数据生成时间:', new Date(<?php echo $homepage_data['generated_at'] * 1000; ?>));

        // 监控页面性能
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('页面性能数据:', {
                        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                        totalTime: perfData.loadEventEnd - perfData.fetchStart
                    });
                }, 0);
            });
        }
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    // 可以发送错误报告到服务器
});

// 网络状态监控
window.addEventListener('online', function() {
    console.log('网络已连接');
    if (window.cartManager) {
        window.cartManager.syncWithServer();
    }
});

window.addEventListener('offline', function() {
    console.log('网络已断开');
    // 显示离线提示
    if (window.cartManager) {
        window.cartManager.showNotification('当前处于离线状态，部分功能可能受限', 'warning');
    }
});
</script>

<!-- 重复的信息区域已删除，统一使用下方的服务政策底部栏 -->

<!-- 统一的服务政策底部栏 -->
<!-- 重复的服务保障与政策区域已删除，统一使用footer中的信息 -->

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
