/* 
 * COSPlay购物网站 - 分类页面响应式布局优化
 * 基于不同设备屏幕大小优化分类页面的显示效果
 */

/* 全局响应式调整 */
.category-page {
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 分类横幅响应式优化 */
.category-banner {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    position: relative;
    overflow: hidden;
}

.banner-content {
    flex: 1;
    min-width: 300px;
    z-index: 2;
}

.banner-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.banner-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.banner-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.banner-cta {
    margin-top: 1.5rem;
}

.banner-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #fff;
    color: #764ba2;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.banner-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.banner-decoration {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 40%;
    z-index: 1;
    overflow: hidden;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.2);
    animation: float 6s infinite ease-in-out;
}

.floating-icon:nth-child(1) {
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 40%;
    animation-delay: 2s;
}

.floating-icon:nth-child(3) {
    top: 30%;
    right: 60%;
    animation-delay: 4s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0) rotate(0);
    }

    50% {
        transform: translateY(-20px) rotate(10deg);
    }
}

/* 快速分类导航优化 */
.quick-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.quick-category-item {
    flex: 1;
    min-width: 140px;
    max-width: 180px;
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.quick-category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 分类内容布局优化 */
.category-content {
    display: flex;
    gap: 1.5rem;
}

/* 侧边栏优化 */
.category-sidebar {
    width: 260px;
    flex-shrink: 0;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 1.25rem;
    position: sticky;
    top: 20px;
    align-self: flex-start;
    height: fit-content;
    transition: all 0.3s ease;
}

/* 主内容区优化 */
.category-products {
    flex: 1;
    min-width: 0;
    padding: 0;
}

/* 商品网格优化 */
.product-grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
}

/* 商品卡片优化 */
.product-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 3;
    color: #fff;
}

.product-badge.new {
    background-color: #4CAF50;
}

.product-badge.hot {
    background-color: #FF5722;
}

.product-badge.sale {
    background-color: #2196F3;
}

.product-image-wrapper {
    position: relative;
    padding-top: 100%;
    /* 1:1 宽高比 */
    overflow: hidden;
    background-color: #f8f9fa;
}

.product-image-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-image:hover {
    transform: scale(1.05);
}

.product-quick-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 3;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
}

.product-card:hover .product-quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #fff;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    color: #666;
}

.quick-action-btn:hover {
    background-color: #764ba2;
    color: #fff;
    transform: scale(1.1);
}

.product-content {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.product-category {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.rating-stars {
    display: flex;
    color: #ffc107;
    font-size: 0.85rem;
}

.rating-count {
    font-size: 0.75rem;
    color: #6c757d;
}

.product-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0.5rem 0;
    line-height: 1.4;
}

.product-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.2s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title a:hover {
    color: #764ba2;
}

.product-price-section {
    margin: 0.75rem 0;
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #764ba2;
}

.product-actions {
    margin-top: auto;
    padding-top: 0.75rem;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 4px;
    background-color: #764ba2;
    color: #fff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
    background-color: #5e3b82;
}

/* 列表视图优化 */
.product-grid-view.list-view {
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.product-grid-view.list-view .product-card {
    display: grid;
    grid-template-columns: 220px 1fr;
    height: auto;
}

.product-grid-view.list-view .product-image-wrapper {
    padding-top: 0;
    height: 100%;
}

.product-grid-view.list-view .product-content {
    display: grid;
    grid-template-rows: auto auto 1fr auto;
    padding: 1rem;
}

.product-grid-view.list-view .product-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.product-grid-view.list-view .product-title a {
    -webkit-line-clamp: 1;
}

.product-grid-view.list-view .product-meta {
    margin-bottom: 1rem;
}

.product-grid-view.list-view .product-actions {
    max-width: 200px;
}

.product-grid-view.list-view .product-badge {
    left: 20px;
    top: 20px;
}

.product-grid-view.list-view .product-quick-actions {
    top: 20px;
    right: auto;
    left: 200px;
    flex-direction: row;
}

/* 分页导航优化 */
.pagination-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 筛选器折叠按钮 */
.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.filter-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #6c757d;
}

/* 商品工具栏优化 */
.products-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

/* 筛选标签优化 */
.products-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* 移动端响应式样式 */
@media (max-width: 991px) {
    .category-content {
        flex-direction: column;
        gap: 1rem;
    }

    .category-sidebar {
        width: 100%;
        position: relative;
        top: 0;
        margin-bottom: 1rem;
    }

    .filter-toggle {
        display: block;
    }

    .sidebar-content {
        overflow: hidden;
        max-height: 1000px;
        /* 默认展开 */
        transition: max-height 0.3s ease;
    }

    .category-sidebar.collapsed .sidebar-content {
        max-height: 0;
    }

    .category-sidebar.collapsed .filter-toggle i {
        transform: rotate(180deg);
    }

    .product-grid-view.list-view .product-card {
        grid-template-columns: 1fr;
    }

    .product-grid-view.list-view .product-image-wrapper {
        padding-top: 75%;
        /* 4:3 比例 */
    }

    .product-grid-view.list-view .product-quick-actions {
        top: 10px;
        right: 10px;
        left: auto;
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .category-banner {
        padding: 1.5rem;
    }

    .banner-decoration {
        display: none;
    }

    .quick-categories {
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 0.75rem;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination-info {
        order: -1;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .quick-category-item {
        min-width: 100px;
        padding: 0.75rem;
    }

    .toolbar-left,
    .toolbar-right {
        width: 100%;
    }

    .toolbar-right {
        margin-top: 1rem;
        justify-content: space-between;
    }

    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
        gap: 0.5rem;
    }

    .pagination-numbers .pagination-number:not(.active):not(:first-child):not(:last-child) {
        display: none;
    }

    .pagination-btn span {
        display: none;
    }
}