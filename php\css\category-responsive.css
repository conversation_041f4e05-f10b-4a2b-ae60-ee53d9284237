/**
 * 分类页面响应式样式
 * 优化不同设备上的显示效果和文字颜色
 */

/* ===== 商品卡片优化 ===== */
.product-card {
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.product-image-wrapper {
    position: relative;
    overflow: hidden;
    height: 280px;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.08);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 700;
    color: #ffffff;
    z-index: 3;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.product-badge.new {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.product-badge.hot {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

.product-badge.sale {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.product-quick-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.product-card:hover .product-quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.95);
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-action-btn:hover {
    background: #007bff;
    color: #ffffff;
    transform: scale(1.1);
}

.product-content {
    padding: 20px;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.product-category {
    font-size: 0.9rem;
    color: #007bff;
    font-weight: 600;
    background: rgba(0, 123, 255, 0.1);
    padding: 4px 10px;
    border-radius: 15px;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    font-size: 0.9rem;
    color: #ffc107;
}

.rating-count {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.product-title {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.4;
}

.product-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-title a:hover {
    color: #007bff;
}

.product-price-section {
    margin-bottom: 15px;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 800;
    color: #007bff;
    text-shadow: 0 1px 2px rgba(0,123,255,0.2);
}

.product-actions {
    margin-top: 15px;
}

.add-to-cart-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.add-to-cart-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

/* ===== 侧边栏文字颜色优化 ===== */
.sidebar-title {
    color: #2c3e50;
    font-weight: 700;
}

.filter-title {
    color: #495057;
    font-weight: 600;
}

.filter-name {
    color: #2c3e50;
    font-weight: 500;
}

.filter-count {
    background: #e9ecef;
    color: #495057;
    font-weight: 600;
}

.filter-checkbox:checked + .filter-label .filter-name {
    color: #007bff;
    font-weight: 600;
}

.filter-checkbox:checked + .filter-label .filter-count {
    background: #007bff;
    color: #ffffff;
}

/* ===== 工具栏文字优化 ===== */
.products-title {
    color: #2c3e50;
    font-weight: 700;
}

.products-count {
    color: #007bff;
    font-weight: 600;
}

.sort-label {
    color: #495057;
    font-weight: 600;
}

.sort-select {
    color: #2c3e50;
    font-weight: 500;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 8px 12px;
    background: #ffffff;
}

.sort-select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* ===== 视图切换按钮优化 ===== */
.view-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    background: #ffffff;
    color: #6c757d;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    border-color: #007bff;
    color: #007bff;
    background: rgba(0,123,255,0.05);
}

.view-btn.active {
    border-color: #007bff;
    background: #007bff;
    color: #ffffff;
}

/* ===== 分页导航优化 ===== */
.pagination-btn,
.pagination-number {
    color: #495057;
    font-weight: 600;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pagination-btn:hover,
.pagination-number:hover {
    color: #007bff;
    background: rgba(0,123,255,0.1);
    text-decoration: none;
}

.pagination-number.active {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
}

.pagination-info span {
    color: #6c757d;
    font-weight: 500;
}

/* ===== 响应式优化 ===== */
@media (max-width: 1200px) {
    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 18px;
    }
    
    .product-image-wrapper {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .product-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 15px;
    }
    
    .product-image-wrapper {
        height: 220px;
    }
    
    .product-content {
        padding: 15px;
    }
    
    .product-title {
        font-size: 1rem;
    }
    
    .product-price {
        font-size: 1.2rem;
    }
    
    .add-to-cart-btn {
        padding: 10px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .product-grid-view {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .product-image-wrapper {
        height: 180px;
    }
    
    .product-content {
        padding: 12px;
    }
    
    .product-title {
        font-size: 0.95rem;
        margin-bottom: 10px;
    }
    
    .product-price {
        font-size: 1.1rem;
    }
    
    .add-to-cart-btn {
        padding: 8px;
        font-size: 0.85rem;
    }
    
    .quick-action-btn {
        width: 35px;
        height: 35px;
    }
    
    .product-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

@media (max-width: 400px) {
    .product-grid-view {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .product-image-wrapper {
        height: 200px;
    }
}
