<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Card 文字样式最终测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        body {
            color: #333 !important;
            background-color: #f8f9fa !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            box-sizing: border-box;
        }

        .section-title {
            color: #212529 !important;
            font-weight: 700 !important;
            font-size: 1.8rem !important;
            margin: 0 0 24px 0 !important;
        }

        /* 优化后的商品卡片样式 */
        .product-card {
            background: #ffffff !important;
            color: #212529 !important;
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            overflow: hidden !important;
            position: relative !important;
        }

        .product-card:hover {
            color: #212529 !important;
            text-decoration: none !important;
            transform: none !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        .product-info {
            background: #ffffff !important;
            color: #212529 !important;
            padding: 20px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: space-between !important;
            min-height: 140px !important;
            box-sizing: border-box !important;
            position: relative !important;
            z-index: 10 !important;
        }

        /* 优化商品信息区域文字颜色继承 */
        .product-info * {
            color: inherit !important;
        }

        /* 确保 h3 标题可见 - 修复为深色 */
        .product-info h1, .product-info h2, .product-info h3, .product-info h4, .product-info h5, .product-info h6 {
            color: #212529 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-info p {
            color: #6c757d !important;
        }

        .product-info span {
            color: inherit !important;
        }

        .product-info div {
            color: inherit !important;
        }

        .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin: 0 0 8px 0 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            position: relative !important;
            z-index: 15 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            filter: none !important;
            transform: none !important;
        }

        /* 强制 h3 标签为深色可见 */
        .product-info h3.product-name {
            color: #212529 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-origin {
            color: #6c757d !important;
            font-size: 0.9rem !important;
            margin: 0 0 12px 0 !important;
            font-weight: 500 !important;
            line-height: 1.3 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            position: relative !important;
            z-index: 12 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 强制 p 标签为灰色可见 */
        .product-info p.product-origin {
            color: #6c757d !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 价格容器和原价样式修复 */
        .product-price-container {
            display: flex !important;
            align-items: baseline !important;
            gap: 8px !important;
            flex: 1 !important;
            min-width: 0 !important;
        }

        .product-price-original,
        span.product-price-original {
            color: #8e9ba8 !important;
            text-decoration: line-through !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            margin: 0 !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background-color: transparent !important;
        }

        .product-price-row {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-top: auto !important;
            padding-top: 8px !important;
            gap: 8px !important;
        }

        /* 统一的价格显示规则 - 最高优先级 */
        .product-price,
        span.product-price,
        .product-info .product-price,
        .product-card .product-price,
        .product-price-container .product-price {
            color: #212529 !important;
            font-weight: 800 !important;
            font-size: 1.25rem !important;
            line-height: 1.2 !important;
            margin: 0 !important;
            padding: 0 !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background-color: transparent !important;
            border: none !important;
            text-decoration: none !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            transform: none !important;
            filter: none !important;
            -webkit-filter: none !important;
            text-shadow: none !important;
            box-shadow: none !important;
        }

        /* 确保 product-card 内所有文字元素都有正确的颜色 */
        .product-card,
        .product-card *,
        a.product-card,
        a.product-card * {
            color: inherit !important;
        }

        /* 具体的文字元素颜色设置 */
        .product-card .product-name,
        .product-card h1,
        .product-card h2, 
        .product-card h3,
        .product-card h4,
        .product-card h5,
        .product-card h6 {
            color: #212529 !important;
        }

        .product-card .product-origin,
        .product-card p {
            color: #6c757d !important;
        }

        .product-card .product-price,
        .product-card span.product-price {
            color: #212529 !important;
        }

        .product-card .product-price-original,
        .product-card span.product-price-original {
            color: #8e9ba8 !important;
        }

        .product-badge {
            background-color: #007bff !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 6px 12px !important;
            border-radius: 20px !important;
            position: absolute !important;
            top: 12px !important;
            left: 12px !important;
            z-index: 2 !important;
        }

        .product-badge.sale {
            background-color: #ffc107 !important;
            color: #212529 !important;
            top: 12px !important;
            left: auto !important;
            right: 12px !important;
        }

        .add-to-cart {
            background-color: #007bff !important;
            color: #ffffff !important;
            border: none !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            flex-shrink: 0 !important;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
            position: relative !important;
            z-index: 30 !important;
        }

        .add-to-cart:hover {
            background-color: #0056b3 !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
        }

        .add-to-cart i {
            color: #ffffff !important;
            font-size: 14px !important;
            line-height: 1 !important;
            position: relative !important;
            z-index: 35 !important;
        }

        /* 商品网格布局 */
        .product-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
            gap: 24px !important;
            margin-top: 30px !important;
        }

        .product-image-container {
            position: relative !important;
            width: 100% !important;
            height: 280px !important;
            overflow: hidden !important;
            background: #f8f9fa !important;
            z-index: 1 !important;
        }

        .product-image {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: transform 0.3s ease !important;
            position: relative !important;
            z-index: 2 !important;
        }

        .product-card:hover .product-image {
            transform: none !important;
        }

        /* 防止整个商品卡片模糊 - 移除所有3D变换 */
        .product-card {
            transform: none !important;
            -webkit-transform: none !important;
            backface-visibility: visible !important;
            -webkit-backface-visibility: visible !important;
            perspective: none !important;
            -webkit-perspective: none !important;
        }

        .product-card:hover {
            transform: none !important;
            -webkit-transform: none !important;
        }

        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .test-info h3 {
            color: #0066cc;
            margin-top: 0;
        }

        .check-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="section-title">Product Card 文字样式最终测试</h1>

        <div class="test-info">
            <h3>✅ 修复完成的问题</h3>
            <ul>
                <li><strong>H3 标题可见性：</strong>设置为深色 (#212529)，确保在白色背景上可见</li>
                <li><strong>Product-card 基础颜色：</strong>从白色文字改为深色文字</li>
                <li><strong>价格显示：</strong>统一价格颜色为深色，原价为灰色删除线</li>
                <li><strong>分类文字：</strong>设置为中等灰色 (#6c757d)</li>
                <li><strong>文字继承：</strong>优化颜色继承规则，避免冲突</li>
                <li><strong>JavaScript 修复：</strong>添加动态修复函数确保样式生效</li>
            </ul>
        </div>

        <div class="product-grid">
            <!-- 测试商品卡片 1 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge">热门</div>
                    <div class="product-badge sale">特价</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="原神 - 刻晴星霜华裳" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                    <p class="product-origin">原神</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥499.00</span>
                            <span class="product-price-original">¥599.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>

            <!-- 测试商品卡片 2 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="英雄联盟 - 阿狸" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">英雄联盟 - 阿狸</h3>
                    <p class="product-origin">英雄联盟</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥649.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>
        </div>

        <div class="check-results" id="check-results">
            <h3>自动检查结果</h3>
            <div id="results-content">检查中...</div>
        </div>
    </div>

    <script>
        // 修复商品卡片文字样式
        function fixProductCardTextStyles() {
            console.log('开始修复商品卡片文字样式...');

            // 修复所有商品卡片的基础颜色
            document.querySelectorAll('.product-card').forEach(card => {
                card.style.color = '#212529';
                
                // 修复标题
                const title = card.querySelector('.product-name, h3');
                if (title) {
                    title.style.color = '#212529';
                    title.style.fontWeight = '600';
                    title.style.fontSize = '1.1rem';
                    title.style.display = 'block';
                    title.style.visibility = 'visible';
                    title.style.opacity = '1';
                }
                
                // 修复分类/来源
                const origin = card.querySelector('.product-origin, p');
                if (origin) {
                    origin.style.color = '#6c757d';
                    origin.style.display = 'block';
                    origin.style.visibility = 'visible';
                    origin.style.opacity = '1';
                }
                
                // 修复商品信息区域
                const productInfo = card.querySelector('.product-info');
                if (productInfo) {
                    productInfo.style.color = '#212529';
                    productInfo.style.backgroundColor = '#ffffff';
                }
            });

            console.log('商品卡片文字样式修复完成');
        }

        // 修复商品价格显示问题
        function fixProductPrices() {
            console.log('开始修复商品价格显示...');

            // 修复所有价格元素
            document.querySelectorAll('.product-price, span.product-price').forEach(price => {
                price.style.color = '#212529';
                price.style.fontWeight = '800';
                price.style.fontSize = '1.25rem';
                price.style.display = 'inline-block';
                price.style.visibility = 'visible';
                price.style.opacity = '1';
                price.style.backgroundColor = 'transparent';
                price.style.textDecoration = 'none';
                price.style.border = 'none';
                price.style.margin = '0';
                price.style.padding = '0';
            });

            // 修复原价元素
            document.querySelectorAll('.product-price-original, span.product-price-original').forEach(originalPrice => {
                originalPrice.style.color = '#8e9ba8';
                originalPrice.style.textDecoration = 'line-through';
                originalPrice.style.fontSize = '1rem';
                originalPrice.style.fontWeight = '500';
                originalPrice.style.display = 'inline-block';
                originalPrice.style.visibility = 'visible';
                originalPrice.style.opacity = '1';
                originalPrice.style.backgroundColor = 'transparent';
                originalPrice.style.margin = '0';
                originalPrice.style.padding = '0';
            });

            console.log('商品价格显示修复完成');
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== Product Card 文字样式最终测试 ===');
            
            // 应用修复
            fixProductCardTextStyles();
            fixProductPrices();
            
            // 延迟再次修复
            setTimeout(() => {
                fixProductCardTextStyles();
                fixProductPrices();
            }, 500);
            
            // 检查修复结果
            setTimeout(() => {
                const h3Elements = document.querySelectorAll('h3.product-name');
                const priceElements = document.querySelectorAll('.product-price');
                const originElements = document.querySelectorAll('.product-origin');
                
                let results = [];
                
                // 检查 H3 标题
                h3Elements.forEach((h3, index) => {
                    const styles = window.getComputedStyle(h3);
                    const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden' && styles.opacity !== '0';
                    results.push(`✅ H3 标题 ${index + 1}: "${h3.textContent}" - ${isVisible ? '可见' : '不可见'} (颜色: ${styles.color})`);
                });
                
                // 检查价格
                priceElements.forEach((price, index) => {
                    const styles = window.getComputedStyle(price);
                    const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden' && styles.opacity !== '0';
                    results.push(`✅ 价格 ${index + 1}: "${price.textContent}" - ${isVisible ? '可见' : '不可见'} (颜色: ${styles.color})`);
                });
                
                // 检查分类
                originElements.forEach((origin, index) => {
                    const styles = window.getComputedStyle(origin);
                    const isVisible = styles.display !== 'none' && styles.visibility !== 'hidden' && styles.opacity !== '0';
                    results.push(`✅ 分类 ${index + 1}: "${origin.textContent}" - ${isVisible ? '可见' : '不可见'} (颜色: ${styles.color})`);
                });
                
                // 显示结果
                document.getElementById('results-content').innerHTML = results.map(result => 
                    `<div style="margin: 5px 0; padding: 8px; background: #d4edda; border-radius: 4px; color: #155724;">${result}</div>`
                ).join('');
                
                console.log('检查结果:', results);
            }, 1000);
        });
    </script>
</body>
</html>
