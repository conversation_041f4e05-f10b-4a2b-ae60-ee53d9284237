/**
 * COSplay服装购物网站主JavaScript文件
 * 包含所有核心动画和交互功能
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function () {
    // 初始化所有功能
    initLogoAnimation();
    initSearchDropdown();
    initUserDropdown();
    initCarousel();
    initHoverCards();
    initWaterfallLazyLoad();
    initStickyNavigation();
    init360Viewer();
    initAddToCartAnimation();
    initCartItemRemove();
    initQuantityControls();
    initTabSwitching();
    initResponsiveMenu();
    initAccessibility();

    // 新增交互功能
    initCategoryFilters();
    initProductInteractions();
    initCartInteractions();
    initCheckoutInteractions();
    initUserCenterInteractions();
    initOrderManagement();
    initAddressManagement();
    initLoginInteractions();
    initWishlistInteractions();
    initNotifications();
    initBackToTop();
    initFormValidation();
    initModalSystem();
    initTooltips();
});

/**
 * LOGO呼吸动画效果
 */
function initLogoAnimation() {
    const logo = document.querySelector('.logo span');
    if (!logo) return;

    // 添加呼吸动画类
    logo.classList.add('logo-pulse');
}

/**
 * 搜索框自动补全下拉动画
 */
function initSearchDropdown() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('.search-results');

    if (!searchInput || !searchResults) return;

    // 模拟搜索结果数据
    const searchData = [
        { name: '原神 - 刻晴星霜华裳', category: '游戏' },
        { name: '原神 - 甘雨晚霞余辉', category: '游戏' },
        { name: '原神 - 凝光天权星袍', category: '游戏' },
        { name: '宝可梦 - 皮卡丘', category: '动漫' },
        { name: '英雄联盟 - 阿狸', category: '游戏' },
        { name: '鬼灭之刃 - 灶门炭治郎', category: '动漫' },
        { name: '鬼灭之刃 - 祢豆子', category: '动漫' },
        { name: '海贼王 - 路飞', category: '动漫' }
    ];

    // 搜索输入事件
    searchInput.addEventListener('input', function () {
        const query = this.value.trim().toLowerCase();

        // 清空搜索结果
        searchResults.innerHTML = '';

        if (query.length < 1) {
            searchResults.classList.remove('active');
            return;
        }

        // 过滤搜索结果
        const filteredResults = searchData.filter(item =>
            item.name.toLowerCase().includes(query) ||
            item.category.toLowerCase().includes(query)
        );

        // 显示搜索结果
        if (filteredResults.length > 0) {
            filteredResults.forEach(item => {
                const resultItem = document.createElement('div');
                resultItem.className = 'search-result-item';

                // 高亮匹配文本
                const highlightedName = item.name.replace(
                    new RegExp(query, 'gi'),
                    match => `<span class="highlight">${match}</span>`
                );

                resultItem.innerHTML = `
                    <div class="search-result-name">${highlightedName}</div>
                    <div class="search-result-category">${item.category}</div>
                `;

                resultItem.addEventListener('click', function () {
                    searchInput.value = item.name;
                    searchResults.classList.remove('active');
                });

                searchResults.appendChild(resultItem);
            });

            searchResults.classList.add('active');
        } else {
            const noResult = document.createElement('div');
            noResult.className = 'search-no-result';
            noResult.textContent = '没有找到相关结果';
            searchResults.appendChild(noResult);
            searchResults.classList.add('active');
        }
    });

    // 点击外部关闭搜索结果
    document.addEventListener('click', function (e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.remove('active');
        }
    });
}

/**
 * 用户下拉菜单
 */
function initUserDropdown() {
    const userDropdowns = document.querySelectorAll('.user-dropdown');
    console.log('找到用户下拉菜单元素:', userDropdowns.length);

    userDropdowns.forEach(userDropdown => {
        const userTrigger = userDropdown.querySelector('.user-dropdown-toggle');
        const userMenu = userDropdown.querySelector('.user-menu');

        if (!userMenu || !userTrigger) {
            console.warn('下拉菜单初始化失败:', {
                trigger: userTrigger ? '找到' : '未找到',
                menu: userMenu ? '找到' : '未找到'
            });
            return;
        }

        console.log('菜单初始化成功', userDropdown);

        // 为触发器添加点击事件
        userTrigger.addEventListener('click', function (e) {
            console.log('用户下拉触发器被点击');
            e.preventDefault();
            e.stopPropagation();

            // 检查当前菜单状态
            const isActive = userMenu.classList.contains('show');
            console.log('当前菜单状态:', isActive ? '显示' : '隐藏');

            // 先关闭所有其他菜单
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                const menu = dropdown.querySelector('.user-menu');
                if (menu && menu !== userMenu) {
                    dropdown.classList.remove('active');
                    menu.classList.remove('show');
                }
            });

            // 切换当前菜单状态
            if (!isActive) {
                userDropdown.classList.add('active');
                userMenu.classList.add('show');
                console.log('显示菜单');
            } else {
                userDropdown.classList.remove('active');
                userMenu.classList.remove('show');
                console.log('隐藏菜单');
            }
        });

        // 点击菜单项后关闭菜单并允许跳转
        userMenu.addEventListener('click', function (e) {
            const menuItem = e.target.closest('.user-menu-item');
            if (menuItem) {
                // 关闭菜单
                userDropdown.classList.remove('active');
                userMenu.classList.remove('show');
                console.log('点击菜单项，关闭菜单');

                // 如果是链接，允许正常跳转
                if (menuItem.tagName === 'A' && menuItem.href) {
                    // 不阻止默认行为，让链接正常跳转
                    return;
                }
            }
        });
    });

    // 点击外部关闭所有菜单
    document.addEventListener('click', function (e) {
        if (!e.target.closest('.user-dropdown')) {
            console.log('点击外部区域，关闭所有菜单');
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });

    // ESC键关闭菜单
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            console.log('按下ESC键，关闭所有菜单');
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.user-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }
    });
}

/**
 * 轮播图3D翻转动画
 */
function initCarousel() {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;

    const carouselItems = carousel.querySelectorAll('.carousel-item');
    const carouselControls = carousel.querySelectorAll('.carousel-control');
    const carouselIndicators = carousel.querySelectorAll('.carousel-indicator');

    let currentIndex = 0;
    let isAnimating = false;

    // 显示指定索引的轮播项
    function showSlide(index) {
        if (isAnimating) return;
        isAnimating = true;

        // 更新索引
        const previousIndex = currentIndex;
        currentIndex = index;

        // 更新指示器
        carouselIndicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === currentIndex);
        });

        // 添加翻转动画类
        carouselItems[previousIndex].classList.add('flip-out');
        carouselItems[currentIndex].classList.add('flip-in');

        // 动画结束后移除类
        setTimeout(() => {
            carouselItems[previousIndex].classList.remove('active', 'flip-out');
            carouselItems[currentIndex].classList.add('active');
            carouselItems[currentIndex].classList.remove('flip-in');
            isAnimating = false;
        }, 600);
    }

    // 下一张幻灯片
    function nextSlide() {
        showSlide((currentIndex + 1) % carouselItems.length);
    }

    // 上一张幻灯片
    function prevSlide() {
        showSlide((currentIndex - 1 + carouselItems.length) % carouselItems.length);
    }

    // 绑定控制按钮事件
    if (carouselControls.length >= 2) {
        carouselControls[0].addEventListener('click', prevSlide);
        carouselControls[1].addEventListener('click', nextSlide);
    }

    // 绑定指示器事件
    carouselIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => showSlide(index));
    });

    // 自动轮播
    let autoplayInterval = setInterval(nextSlide, 5000);

    // 鼠标悬停时暂停自动轮播
    carousel.addEventListener('mouseenter', () => clearInterval(autoplayInterval));
    carousel.addEventListener('mouseleave', () => {
        autoplayInterval = setInterval(nextSlide, 5000);
    });

    // 触摸滑动支持
    let touchStartX = 0;
    let touchEndX = 0;

    carousel.addEventListener('touchstart', e => {
        touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });

    carousel.addEventListener('touchend', e => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, { passive: true });

    function handleSwipe() {
        const threshold = 50;
        if (touchEndX < touchStartX - threshold) {
            nextSlide(); // 左滑，下一张
        } else if (touchEndX > touchStartX + threshold) {
            prevSlide(); // 右滑，上一张
        }
    }
}

/**
 * 热门角色专区悬浮卡片放大效果
 */
function initHoverCards() {
    const characterCards = document.querySelectorAll('.character-card');
    if (characterCards.length === 0) return;

    characterCards.forEach(card => {
        card.addEventListener('mouseenter', function () {
            this.classList.add('hover');
        });

        card.addEventListener('mouseleave', function () {
            this.classList.remove('hover');
        });
    });
}

/**
 * 新品瀑布流展示懒加载动画
 */
function initWaterfallLazyLoad() {
    const newProductsSection = document.querySelector('.new-products');
    if (!newProductsSection) return;

    const productItems = newProductsSection.querySelectorAll('.product-item');

    // 检查元素是否在视口中
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }

    // 加载可见的产品项
    function loadVisibleItems() {
        productItems.forEach(item => {
            if (isElementInViewport(item) && !item.classList.contains('loaded')) {
                // 延迟加载以创建瀑布流效果
                setTimeout(() => {
                    item.classList.add('loaded');
                }, Math.random() * 300);
            }
        });
    }

    // 初始加载
    loadVisibleItems();

    // 滚动时加载
    window.addEventListener('scroll', loadVisibleItems);
}

/**
 * 悬浮分类导航侧边跟随动画
 */
function initStickyNavigation() {
    const stickyNav = document.querySelector('.category-nav');
    if (!stickyNav) return;

    const stickyNavTop = stickyNav.offsetTop;

    function updateStickyNav() {
        if (window.pageYOffset > stickyNavTop) {
            stickyNav.classList.add('sticky');
        } else {
            stickyNav.classList.remove('sticky');
        }
    }

    window.addEventListener('scroll', updateStickyNav);
}

/**
 * 商品详情页360°视图展示区旋转动画
 */
function init360Viewer() {
    const viewer360 = document.querySelector('.product-360-view');
    if (!viewer360) return;

    const viewerContainer = viewer360.querySelector('.view-360-container');
    const viewerImages = viewer360.querySelectorAll('.view-360-image');
    const totalFrames = viewerImages.length;

    let currentFrame = 0;
    let isDragging = false;
    let startX = 0;
    let lastX = 0;

    // 显示指定帧
    function showFrame(frameIndex) {
        viewerImages.forEach((img, index) => {
            img.style.display = index === frameIndex ? 'block' : 'none';
        });
    }

    // 初始显示第一帧
    showFrame(0);

    // 鼠标拖动事件
    viewerContainer.addEventListener('mousedown', e => {
        isDragging = true;
        startX = e.clientX;
        lastX = startX;
        viewerContainer.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', e => {
        if (!isDragging) return;

        const deltaX = e.clientX - lastX;
        lastX = e.clientX;

        // 根据拖动方向和距离计算帧变化
        if (Math.abs(deltaX) > 5) {
            const frameChange = deltaX > 0 ? -1 : 1;
            currentFrame = (currentFrame + frameChange + totalFrames) % totalFrames;
            showFrame(currentFrame);
        }
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
        viewerContainer.style.cursor = 'grab';
    });

    // 触摸事件支持
    viewerContainer.addEventListener('touchstart', e => {
        isDragging = true;
        startX = e.touches[0].clientX;
        lastX = startX;
    }, { passive: true });

    document.addEventListener('touchmove', e => {
        if (!isDragging) return;

        const deltaX = e.touches[0].clientX - lastX;
        lastX = e.touches[0].clientX;

        if (Math.abs(deltaX) > 5) {
            const frameChange = deltaX > 0 ? -1 : 1;
            currentFrame = (currentFrame + frameChange + totalFrames) % totalFrames;
            showFrame(currentFrame);
        }
    }, { passive: true });

    document.addEventListener('touchend', () => {
        isDragging = false;
    }, { passive: true });

    // 自动旋转按钮
    const autoRotateBtn = viewer360.querySelector('.auto-rotate-btn');
    if (autoRotateBtn) {
        let autoRotateInterval = null;

        autoRotateBtn.addEventListener('click', function () {
            if (autoRotateInterval) {
                clearInterval(autoRotateInterval);
                autoRotateInterval = null;
                this.textContent = '自动旋转';
                this.classList.remove('active');
            } else {
                autoRotateInterval = setInterval(() => {
                    currentFrame = (currentFrame + 1) % totalFrames;
                    showFrame(currentFrame);
                }, 100);
                this.textContent = '停止旋转';
                this.classList.add('active');
            }
        });
    }
}

/**
 * 加入购物车按钮点击飞入动画
 */
function initAddToCartAnimation() {
    const addToCartBtns = document.querySelectorAll('.add-to-cart-btn');
    if (addToCartBtns.length === 0) return;

    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();

            // 获取商品图片
            const productCard = this.closest('.product-card') || this.closest('.product-detail');
            if (!productCard) return;

            const productImg = productCard.querySelector('img');
            if (!productImg) return;

            // 获取购物车图标位置
            const cartIcon = document.querySelector('.nav-links .fa-shopping-cart');
            if (!cartIcon) return;

            const cartIconRect = cartIcon.getBoundingClientRect();
            const productImgRect = productImg.getBoundingClientRect();

            // 创建飞入动画元素
            const flyingImg = document.createElement('img');
            flyingImg.src = productImg.src;
            flyingImg.className = 'flying-cart-item';
            flyingImg.style.cssText = `
                position: fixed;
                z-index: 9999;
                width: ${productImgRect.width}px;
                height: ${productImgRect.height}px;
                top: ${productImgRect.top}px;
                left: ${productImgRect.left}px;
                opacity: 0.8;
                pointer-events: none;
            `;

            document.body.appendChild(flyingImg);

            // 执行动画
            setTimeout(() => {
                flyingImg.style.transition = 'all 0.8s cubic-bezier(0.18, 0.89, 0.32, 1.28)';
                flyingImg.style.top = `${cartIconRect.top}px`;
                flyingImg.style.left = `${cartIconRect.left}px`;
                flyingImg.style.width = '20px';
                flyingImg.style.height = '20px';
                flyingImg.style.opacity = '0.2';

                // 更新购物车计数
                const cartCount = document.querySelector('.nav-link-count');
                if (cartCount) {
                    setTimeout(() => {
                        cartCount.textContent = parseInt(cartCount.textContent || 0) + 1;
                        cartCount.classList.add('bump');
                        setTimeout(() => cartCount.classList.remove('bump'), 300);
                    }, 800);
                }

                // 移除飞入元素
                setTimeout(() => {
                    document.body.removeChild(flyingImg);
                }, 800);
            }, 10);
        });
    });
}

/**
 * 购物车商品删除滑动消失动画
 */
function initCartItemRemove() {
    const cartItemRemoveBtns = document.querySelectorAll('.cart-item-remove');
    if (cartItemRemoveBtns.length === 0) return;

    cartItemRemoveBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const cartItem = this.closest('.cart-item');
            if (!cartItem) return;

            // 添加滑出动画类
            cartItem.classList.add('slide-out');

            // 动画结束后移除元素
            setTimeout(() => {
                cartItem.style.height = cartItem.offsetHeight + 'px';
                setTimeout(() => {
                    cartItem.style.height = '0';
                    cartItem.style.opacity = '0';
                    cartItem.style.margin = '0';
                    cartItem.style.padding = '0';

                    // 完全移除元素
                    setTimeout(() => {
                        cartItem.remove();
                        updateCartTotal();
                    }, 300);
                }, 10);
            }, 300);
        });
    });

    // 更新购物车总价
    function updateCartTotal() {
        const cartItems = document.querySelectorAll('.cart-item');
        const summaryTotal = document.querySelector('.summary-total');

        if (!summaryTotal) return;

        // 如果没有商品，显示空购物车
        if (cartItems.length === 0) {
            const cartItems = document.querySelector('.cart-items');
            if (cartItems) {
                cartItems.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart empty-cart-icon"></i>
                        <h3>您的购物车是空的</h3>
                        <p>快去挑选心仪的商品吧！</p>
                        <a href="category.php" class="btn-primary">去购物</a>
                    </div>
                `;
            }

            summaryTotal.textContent = '¥0';
            return;
        }

        // 计算总价
        let total = 0;
        cartItems.forEach(item => {
            const priceText = item.querySelector('.cart-item-price').textContent;
            const price = parseFloat(priceText.replace('¥', ''));
            const quantity = parseInt(item.querySelector('.cart-quantity-input').value);
            total += price * quantity;
        });

        summaryTotal.textContent = `¥${total}`;
    }
}

/**
 * 数量增减控件
 */
function initQuantityControls() {
    const quantityControls = document.querySelectorAll('.cart-quantity-control');
    if (quantityControls.length === 0) return;

    quantityControls.forEach(control => {
        const minusBtn = control.querySelector('.minus');
        const plusBtn = control.querySelector('.plus');
        const input = control.querySelector('.cart-quantity-input');

        if (!minusBtn || !plusBtn || !input) return;

        // 减少数量
        minusBtn.addEventListener('click', function () {
            let value = parseInt(input.value);
            if (value > 1) {
                input.value = value - 1;
                updateItemSubtotal(control);
                updateCartTotal();
            }

            // 添加点击动画
            this.classList.add('clicked');
            setTimeout(() => this.classList.remove('clicked'), 300);
        });

        // 增加数量
        plusBtn.addEventListener('click', function () {
            let value = parseInt(input.value);
            input.value = value + 1;
            updateItemSubtotal(control);
            updateCartTotal();

            // 添加点击动画
            this.classList.add('clicked');
            setTimeout(() => this.classList.remove('clicked'), 300);
        });

        // 手动输入数量
        input.addEventListener('change', function () {
            let value = parseInt(this.value);
            if (isNaN(value) || value < 1) {
                this.value = 1;
            }
            updateItemSubtotal(control);
            updateCartTotal();
        });
    });

    // 更新单个商品小计
    function updateItemSubtotal(control) {
        const cartItem = control.closest('.cart-item');
        if (!cartItem) return;

        const priceElement = cartItem.querySelector('.cart-item-price');
        if (!priceElement) return;

        const priceText = priceElement.textContent;
        const price = parseFloat(priceText.replace('¥', ''));
        const quantity = parseInt(control.querySelector('.cart-quantity-input').value);

        // 如果有小计元素则更新
        const subtotalElement = cartItem.querySelector('.cart-item-subtotal');
        if (subtotalElement) {
            subtotalElement.textContent = `¥${(price * quantity).toFixed(2)}`;
        }
    }

    // 更新购物车总价
    function updateCartTotal() {
        const cartItems = document.querySelectorAll('.cart-item');
        const summaryTotal = document.querySelector('.summary-total');

        if (!summaryTotal) return;

        let total = 0;
        cartItems.forEach(item => {
            const priceText = item.querySelector('.cart-item-price').textContent;
            const price = parseFloat(priceText.replace('¥', ''));
            const quantity = parseInt(item.querySelector('.cart-quantity-input').value);
            total += price * quantity;
        });

        summaryTotal.textContent = `¥${total}`;
    }
}

/**
 * 选项卡切换
 */
function initTabSwitching() {
    // 订单选项卡
    const orderTabs = document.querySelectorAll('.order-tab');
    if (orderTabs.length > 0) {
        orderTabs.forEach(tab => {
            tab.addEventListener('click', function () {
                // 移除所有活动状态
                orderTabs.forEach(t => t.classList.remove('active'));

                // 添加当前活动状态
                this.classList.add('active');

                // 这里可以添加实际的订单过滤逻辑
                const tabType = this.getAttribute('data-tab');
                filterOrders(tabType);
            });
        });
    }

    // 模拟订单过滤
    function filterOrders(type) {
        const orderCards = document.querySelectorAll('.order-card');
        if (orderCards.length === 0) return;

        if (type === 'all') {
            orderCards.forEach(card => card.style.display = 'block');
            return;
        }

        orderCards.forEach(card => {
            const statusElement = card.querySelector('.order-status');
            if (!statusElement) return;

            const hasStatus = statusElement.classList.contains(`status-${type}`);
            card.style.display = hasStatus ? 'block' : 'none';
        });
    }

    // 登录/注册切换
    const switchToRegisterBtn = document.getElementById('switchToRegister');
    if (switchToRegisterBtn) {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const authImage = document.querySelector('.auth-image');

        let isLoginView = true;

        switchToRegisterBtn.addEventListener('click', function () {
            if (isLoginView) {
                // 切换到注册视图
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
                this.textContent = '返回登录';
                authImage.querySelector('.auth-welcome').textContent = '已有账号？';
                authImage.querySelector('.auth-message').textContent = '登录您的账号，享受更多专属服务和优惠。';
            } else {
                // 切换到登录视图
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
                this.textContent = '注册新账号';
                authImage.querySelector('.auth-welcome').textContent = '欢迎来到COSPlay';
                authImage.querySelector('.auth-message').textContent = '专业的角色扮演服装购物平台，为您提供高品质的动漫、游戏角色服装及配饰。';
            }

            isLoginView = !isLoginView;
        });
    }
}

/**
 * 响应式菜单
 */
function initResponsiveMenu() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    if (!menuToggle || !navLinks) return;

    menuToggle.addEventListener('click', function () {
        navLinks.classList.toggle('active');
        this.classList.toggle('active');
    });

    // 在小屏幕上点击链接后关闭菜单
    const navLinkItems = navLinks.querySelectorAll('a');
    navLinkItems.forEach(link => {
        link.addEventListener('click', function () {
            if (window.innerWidth < 768) {
                navLinks.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
}

/**
 * 可访问性增强
 */
function initAccessibility() {
    // 添加键盘导航支持
    const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');

    focusableElements.forEach(element => {
        // 为可聚焦元素添加焦点样式
        element.addEventListener('focus', function () {
            this.classList.add('keyboard-focus');
        });

        element.addEventListener('blur', function () {
            this.classList.remove('keyboard-focus');
        });

        // 为按钮和链接添加键盘事件
        if (element.tagName === 'A' || element.tagName === 'BUTTON') {
            element.addEventListener('keydown', function (e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        }
    });

    // 添加跳转到主要内容的链接（对屏幕阅读器用户有帮助）
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = '跳转到主要内容';
    document.body.insertBefore(skipLink, document.body.firstChild);

    // 为主要内容区域添加ID
    const mainContent = document.querySelector('main') || document.querySelector('.main-content');
    if (mainContent && !mainContent.id) {
        mainContent.id = 'main-content';
    }
}

/**
 * 分类页面筛选功能
 */
function initCategoryFilters() {
    // 筛选侧边栏切换
    const filterToggle = document.querySelector('.filter-toggle');
    const categorySidebar = document.querySelector('.category-sidebar');

    if (filterToggle && categorySidebar) {
        filterToggle.addEventListener('click', function () {
            categorySidebar.classList.toggle('collapsed');
            this.classList.toggle('active');
        });
    }

    // 筛选项交互
    const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function () {
            updateProductFilter();
            updateFilterCount();
        });
    });

    // 价格滑块
    const priceSlider = document.querySelector('.price-slider');
    if (priceSlider) {
        let isDragging = false;
        const priceProgress = priceSlider.querySelector('.price-progress');
        const priceValues = priceSlider.querySelectorAll('.price-values span');

        priceSlider.addEventListener('mousedown', function (e) {
            isDragging = true;
            updatePriceSlider(e);
        });

        document.addEventListener('mousemove', function (e) {
            if (isDragging) {
                updatePriceSlider(e);
            }
        });

        document.addEventListener('mouseup', function () {
            isDragging = false;
        });

        function updatePriceSlider(e) {
            const rect = priceSlider.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));

            if (priceProgress) {
                priceProgress.style.width = percentage + '%';
            }

            // 更新价格显示
            const maxPrice = 2000;
            const currentPrice = Math.round((percentage / 100) * maxPrice);
            if (priceValues.length >= 2) {
                priceValues[1].textContent = `¥${currentPrice}`;
            }

            updateProductFilter();
        }
    }

    // 尺码选择
    const sizeOptions = document.querySelectorAll('.size-option');
    sizeOptions.forEach(option => {
        option.addEventListener('click', function () {
            this.classList.toggle('active');
            updateProductFilter();
        });
    });

    // 评分筛选
    const ratingOptions = document.querySelectorAll('.rating-radio');
    ratingOptions.forEach(radio => {
        radio.addEventListener('change', function () {
            updateProductFilter();
        });
    });

    // 重置筛选
    const resetBtn = document.querySelector('.filter-reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function () {
            // 重置所有筛选项
            filterCheckboxes.forEach(checkbox => checkbox.checked = false);
            sizeOptions.forEach(option => option.classList.remove('active'));
            ratingOptions.forEach(radio => radio.checked = false);

            // 重置价格滑块
            const priceProgress = document.querySelector('.price-progress');
            if (priceProgress) {
                priceProgress.style.width = '100%';
            }

            updateProductFilter();
            updateFilterCount();
        });
    }

    // 排序选择
    const sortSelect = document.querySelector('.sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', function () {
            sortProducts(this.value);
        });
    }

    function updateProductFilter() {
        const productCards = document.querySelectorAll('.product-card');
        let visibleCount = 0;

        productCards.forEach(card => {
            let shouldShow = true;

            // 这里可以添加实际的筛选逻辑
            // 基于选中的筛选条件决定是否显示商品

            if (shouldShow) {
                card.style.display = 'block';
                card.classList.add('filter-show');
                visibleCount++;
            } else {
                card.style.display = 'none';
                card.classList.remove('filter-show');
            }
        });

        // 更新结果计数
        const resultCount = document.querySelector('.category-title span');
        if (resultCount) {
            resultCount.textContent = `(${visibleCount}件商品)`;
        }
    }

    function updateFilterCount() {
        const filterCounts = document.querySelectorAll('.filter-count');
        filterCounts.forEach(count => {
            // 这里可以添加实际的计数逻辑
            const randomCount = Math.floor(Math.random() * 50) + 1;
            count.textContent = randomCount;
        });
    }

    function sortProducts(sortType) {
        const productGrid = document.querySelector('.product-grid-view');
        if (!productGrid) return;

        const products = Array.from(productGrid.querySelectorAll('.product-card'));

        products.sort((a, b) => {
            switch (sortType) {
                case 'price-low':
                    return getProductPrice(a) - getProductPrice(b);
                case 'price-high':
                    return getProductPrice(b) - getProductPrice(a);
                case 'rating':
                    return getProductRating(b) - getProductRating(a);
                case 'newest':
                    return Math.random() - 0.5; // 随机排序模拟最新
                default:
                    return 0;
            }
        });

        // 重新排列DOM元素
        products.forEach(product => productGrid.appendChild(product));
    }

    function getProductPrice(productCard) {
        const priceElement = productCard.querySelector('.product-price');
        if (!priceElement) return 0;
        return parseFloat(priceElement.textContent.replace('¥', ''));
    }

    function getProductRating(productCard) {
        const ratingStars = productCard.querySelectorAll('.product-rating .fas.fa-star');
        return ratingStars.length;
    }
}

/**
 * 产品交互功能
 */
function initProductInteractions() {
    // 产品卡片悬停效果
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function () {
            this.classList.add('hover');
        });

        card.addEventListener('mouseleave', function () {
            this.classList.remove('hover');
        });
    });

    // 快速查看按钮
    const quickViewBtns = document.querySelectorAll('.quick-view-btn');
    quickViewBtns.forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const productCard = this.closest('.product-card');
            if (productCard) {
                showQuickViewModal(productCard);
            }
        });
    });

    // 心愿单按钮
    const wishlistBtns = document.querySelectorAll('.product-wishlist-btn');
    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            this.classList.toggle('active');
            const icon = this.querySelector('i');

            if (this.classList.contains('active')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('已添加到心愿单', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('已从心愿单移除', 'info');
            }
        });
    });

    // 产品图片懒加载
    const productImages = document.querySelectorAll('.product-img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    productImages.forEach(img => imageObserver.observe(img));

    function showQuickViewModal(productCard) {
        const productTitle = productCard.querySelector('.product-title').textContent;
        const productPrice = productCard.querySelector('.product-price').textContent;
        const productImg = productCard.querySelector('.product-img').src;

        const modal = createModal('quick-view', `
            <div class="quick-view-content">
                <div class="quick-view-image">
                    <img src="${productImg}" alt="${productTitle}">
                </div>
                <div class="quick-view-info">
                    <h3>${productTitle}</h3>
                    <p class="quick-view-price">${productPrice}</p>
                    <div class="quick-view-actions">
                        <button class="btn-primary add-to-cart-btn">加入购物车</button>
                        <button class="btn-secondary">查看详情</button>
                    </div>
                </div>
            </div>
        `);

        showModal(modal);
    }
}

/**
 * 购物车交互功能
 */
function initCartInteractions() {
    // 全选/取消全选
    const selectAllCheckbox = document.querySelector('#select-all-items');
    const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox input[type="checkbox"]');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function () {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateCartSummary();
        });
    }

    // 单个商品选择
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function () {
            updateSelectAllState();
            updateCartSummary();
        });
    });

    // 清空购物车
    const clearCartBtn = document.querySelector('.clear-cart-btn');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function () {
            showConfirmModal('确定要清空购物车吗？', '此操作不可撤销', () => {
                const cartItems = document.querySelectorAll('.cart-item');
                cartItems.forEach(item => {
                    item.style.animation = 'slideOutRight 0.5s ease-in-out';
                    setTimeout(() => item.remove(), 500);
                });

                setTimeout(() => {
                    showEmptyCart();
                    showNotification('购物车已清空', 'info');
                }, 600);
            });
        });
    }

    // 优惠券展开/折叠
    const couponHeader = document.querySelector('.coupon-header');
    if (couponHeader) {
        couponHeader.addEventListener('click', function () {
            const couponBody = document.querySelector('.coupon-body');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            this.setAttribute('aria-expanded', !isExpanded);
            couponBody.style.display = isExpanded ? 'none' : 'block';

            const toggle = this.querySelector('.coupon-toggle');
            if (toggle) {
                toggle.style.transform = isExpanded ? 'rotate(0deg)' : 'rotate(180deg)';
            }
        });
    }

    // 优惠券使用
    const couponUseBtns = document.querySelectorAll('.coupon-use');
    couponUseBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const couponItem = this.closest('.coupon-item');
            const couponName = couponItem.querySelector('.coupon-name').textContent;

            this.textContent = '已使用';
            this.disabled = true;
            this.style.backgroundColor = '#10B981';

            showNotification(`已使用优惠券：${couponName}`, 'success');
            updateCartSummary();
        });
    });

    // 继续购物按钮
    const continueShoppingBtn = document.querySelector('.continue-shopping-btn');
    if (continueShoppingBtn) {
        continueShoppingBtn.addEventListener('click', function () {
            window.location.href = 'category.php';
        });
    }

    function updateSelectAllState() {
        if (!selectAllCheckbox) return;

        const checkedItems = document.querySelectorAll('.cart-item-checkbox input[type="checkbox"]:checked');
        const totalItems = itemCheckboxes.length;

        selectAllCheckbox.checked = checkedItems.length === totalItems;
        selectAllCheckbox.indeterminate = checkedItems.length > 0 && checkedItems.length < totalItems;
    }

    function updateCartSummary() {
        const checkedItems = document.querySelectorAll('.cart-item-checkbox input[type="checkbox"]:checked');
        const summaryCount = document.querySelector('.summary-count');
        const summaryTotal = document.querySelector('.summary-total');

        if (summaryCount) {
            summaryCount.textContent = `已选择 ${checkedItems.length} 件商品`;
        }

        // 计算选中商品总价
        let total = 0;
        checkedItems.forEach(checkbox => {
            const cartItem = checkbox.closest('.cart-item');
            const priceElement = cartItem.querySelector('.subtotal-price');
            if (priceElement) {
                const price = parseFloat(priceElement.textContent.replace('¥', ''));
                total += price;
            }
        });

        if (summaryTotal) {
            summaryTotal.textContent = `¥${total.toLocaleString()}`;
        }
    }

    function showEmptyCart() {
        const cartItems = document.querySelector('.cart-items');
        if (cartItems) {
            cartItems.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart empty-cart-icon"></i>
                    <h3>您的购物车是空的</h3>
                    <p>快去挑选心仪的商品吧！</p>
                    <a href="category.php" class="btn-primary">去购物</a>
                </div>
            `;
        }
    }
}

/**
 * 结算页面交互功能
 */
function initCheckoutInteractions() {
    // 地址选择
    const addressOptions = document.querySelectorAll('.address-option');
    addressOptions.forEach(option => {
        const radio = option.querySelector('input[type="radio"]');
        const label = option.querySelector('label');

        if (label) {
            label.addEventListener('click', function () {
                addressOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                radio.checked = true;
            });
        }
    });

    // 添加新地址
    const addAddressBtn = document.querySelector('.add-address');
    if (addAddressBtn) {
        addAddressBtn.addEventListener('click', function () {
            showAddAddressModal();
        });
    }

    // 地址编辑/删除
    const addressActions = document.querySelectorAll('.address-action');
    addressActions.forEach(action => {
        action.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            if (this.classList.contains('edit')) {
                const addressOption = this.closest('.address-option');
                showEditAddressModal(addressOption);
            } else if (this.classList.contains('delete')) {
                const addressOption = this.closest('.address-option');
                showConfirmModal('确定要删除这个地址吗？', '', () => {
                    addressOption.style.animation = 'slideOutRight 0.5s ease-in-out';
                    setTimeout(() => {
                        addressOption.remove();
                        showNotification('地址已删除', 'success');
                    }, 500);
                });
            }
        });
    });

    // 支付方式选择
    const paymentMethods = document.querySelectorAll('.payment-method');
    paymentMethods.forEach(method => {
        const radio = method.querySelector('input[type="radio"]');
        const label = method.querySelector('label');

        if (label) {
            label.addEventListener('click', function () {
                paymentMethods.forEach(m => m.classList.remove('selected'));
                method.classList.add('selected');
                radio.checked = true;
            });
        }
    });

    // 配送方式选择
    const shippingMethods = document.querySelectorAll('.shipping-method');
    shippingMethods.forEach(method => {
        const radio = method.querySelector('input[type="radio"]');
        const label = method.querySelector('label');

        if (label) {
            label.addEventListener('click', function () {
                shippingMethods.forEach(m => m.classList.remove('selected'));
                method.classList.add('selected');
                radio.checked = true;
                updateShippingCost();
            });
        }
    });

    // 提交订单
    const placeOrderBtn = document.querySelector('.place-order-btn');
    if (placeOrderBtn) {
        placeOrderBtn.addEventListener('click', function () {
            if (validateCheckoutForm()) {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

                // 模拟订单处理
                setTimeout(() => {
                    showNotification('订单提交成功！', 'success');
                    // 这里可以跳转到订单确认页面
                    window.location.href = 'order.php';
                }, 2000);
            }
        });
    }

    function updateShippingCost() {
        const selectedShipping = document.querySelector('.shipping-method.selected');
        if (!selectedShipping) return;

        const shippingPrice = selectedShipping.querySelector('.shipping-price').textContent;
        const shippingCostElement = document.querySelector('.summary-row:nth-child(2) .summary-value');

        if (shippingCostElement) {
            shippingCostElement.textContent = shippingPrice;
        }

        // 重新计算总价
        updateCheckoutTotal();
    }

    function updateCheckoutTotal() {
        const subtotal = parseFloat(document.querySelector('.summary-row:first-child .summary-value').textContent.replace('¥', ''));
        const shipping = parseFloat(document.querySelector('.summary-row:nth-child(2) .summary-value').textContent.replace('¥', '') || 0);
        const discount = parseFloat(document.querySelector('.summary-row.discount .summary-value').textContent.replace('-¥', '') || 0);

        const total = subtotal + shipping - discount;
        const totalElement = document.querySelector('.checkout-total');

        if (totalElement) {
            totalElement.textContent = `¥${total.toLocaleString()}`;
        }
    }

    function validateCheckoutForm() {
        const selectedAddress = document.querySelector('.address-option.selected');
        const selectedPayment = document.querySelector('.payment-method.selected');
        const selectedShipping = document.querySelector('.shipping-method.selected');

        if (!selectedAddress) {
            showNotification('请选择收货地址', 'error');
            return false;
        }

        if (!selectedPayment) {
            showNotification('请选择支付方式', 'error');
            return false;
        }

        if (!selectedShipping) {
            showNotification('请选择配送方式', 'error');
            return false;
        }

        return true;
    }

    function showAddAddressModal() {
        const modal = createModal('add-address', `
            <h3>添加新地址</h3>
            <form class="address-form">
                <div class="form-group">
                    <label for="recipient-name">收货人姓名</label>
                    <input type="text" id="recipient-name" required>
                </div>
                <div class="form-group">
                    <label for="recipient-phone">联系电话</label>
                    <input type="tel" id="recipient-phone" required>
                </div>
                <div class="form-group">
                    <label for="recipient-address">详细地址</label>
                    <textarea id="recipient-address" required></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn-primary">保存地址</button>
                </div>
            </form>
        `);

        showModal(modal);
    }

    function showEditAddressModal(addressOption) {
        const name = addressOption.querySelector('.address-name').textContent;
        const phone = addressOption.querySelector('.address-phone').textContent.replace(/.*(\d{3}\*{4}\d{4}).*/, '$1');
        const address = addressOption.querySelector('.address-location').textContent;

        const modal = createModal('edit-address', `
            <h3>编辑地址</h3>
            <form class="address-form">
                <div class="form-group">
                    <label for="edit-recipient-name">收货人姓名</label>
                    <input type="text" id="edit-recipient-name" value="${name}" required>
                </div>
                <div class="form-group">
                    <label for="edit-recipient-phone">联系电话</label>
                    <input type="tel" id="edit-recipient-phone" value="${phone}" required>
                </div>
                <div class="form-group">
                    <label for="edit-recipient-address">详细地址</label>
                    <textarea id="edit-recipient-address" required>${address}</textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn-primary">保存修改</button>
                </div>
            </form>
        `);

        showModal(modal);
    }
}

/**
 * 用户中心交互功能
 */
function initUserCenterInteractions() {
    // 头像编辑
    const avatarEditBtn = document.querySelector('.avatar-edit-btn');
    if (avatarEditBtn) {
        avatarEditBtn.addEventListener('click', function () {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function (e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        const avatar = document.querySelector('.user-avatar img');
                        if (avatar) {
                            avatar.src = e.target.result;
                            showNotification('头像更新成功', 'success');
                        }
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        });
    }

    // 统计卡片动画
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // 收藏商品快速查看
    const quickViewBtns = document.querySelectorAll('.quick-view');
    quickViewBtns.forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            const favoriteItem = this.closest('.favorite-item');
            const productTitle = favoriteItem.querySelector('.favorite-title').textContent;
            const productPrice = favoriteItem.querySelector('.favorite-price').textContent;
            const productImg = favoriteItem.querySelector('.favorite-img').src;

            showQuickViewModal({
                querySelector: () => ({
                    textContent: productTitle
                })
            });
        });
    });

    // 订单状态更新动画
    const orderItems = document.querySelectorAll('.order-item');
    orderItems.forEach(item => {
        const status = item.querySelector('.order-status');
        if (status) {
            // 根据状态添加不同的动画效果
            const statusClass = status.className;
            if (statusClass.includes('processing')) {
                status.classList.add('pulse');
            } else if (statusClass.includes('shipped')) {
                status.classList.add('bounce');
            }
        }
    });
}

/**
 * 订单管理功能
 */
function initOrderManagement() {
    // 订单选项卡切换
    const orderTabs = document.querySelectorAll('.order-tab');
    const orderContents = document.querySelectorAll('.order-content');

    orderTabs.forEach(tab => {
        tab.addEventListener('click', function () {
            const tabType = this.getAttribute('data-tab');

            // 更新选项卡状态
            orderTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 筛选订单
            filterOrdersByStatus(tabType);
        });
    });

    // 订单搜索
    const orderSearchInput = document.querySelector('.order-search-input');
    if (orderSearchInput) {
        orderSearchInput.addEventListener('input', function () {
            const searchTerm = this.value.toLowerCase();
            const orderCards = document.querySelectorAll('.order-card');

            orderCards.forEach(card => {
                const orderNumber = card.querySelector('.order-number').textContent.toLowerCase();
                const productName = card.querySelector('.order-product-name').textContent.toLowerCase();

                if (orderNumber.includes(searchTerm) || productName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // 订单操作按钮
    const orderActionBtns = document.querySelectorAll('.order-action-btn');
    orderActionBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const action = this.getAttribute('data-action');
            const orderCard = this.closest('.order-card');
            const orderNumber = orderCard.querySelector('.order-number').textContent;

            switch (action) {
                case 'pay':
                    handleOrderPayment(orderNumber);
                    break;
                case 'cancel':
                    handleOrderCancel(orderNumber, orderCard);
                    break;
                case 'confirm':
                    handleOrderConfirm(orderNumber, orderCard);
                    break;
                case 'review':
                    handleOrderReview(orderNumber);
                    break;
                case 'track':
                    handleOrderTrack(orderNumber);
                    break;
                case 'refund':
                    handleOrderRefund(orderNumber);
                    break;
            }
        });
    });

    function filterOrdersByStatus(status) {
        const orderCards = document.querySelectorAll('.order-card');

        orderCards.forEach(card => {
            const orderStatus = card.querySelector('.order-status');

            if (status === 'all') {
                card.style.display = 'block';
            } else {
                const hasStatus = orderStatus.classList.contains(`status-${status}`);
                card.style.display = hasStatus ? 'block' : 'none';
            }
        });
    }

    function handleOrderPayment(orderNumber) {
        showConfirmModal('确认支付订单？', `订单号：${orderNumber}`, () => {
            showNotification('正在跳转到支付页面...', 'info');
            // 这里可以跳转到支付页面
            setTimeout(() => {
                showNotification('支付成功！', 'success');
            }, 2000);
        });
    }

    function handleOrderCancel(orderNumber, orderCard) {
        showConfirmModal('确定要取消订单吗？', '取消后无法恢复', () => {
            const statusElement = orderCard.querySelector('.order-status');
            statusElement.textContent = '已取消';
            statusElement.className = 'order-status status-cancelled';

            // 更新操作按钮
            const actionBtns = orderCard.querySelector('.order-actions');
            actionBtns.innerHTML = '<span class="order-cancelled-text">订单已取消</span>';

            showNotification('订单已取消', 'success');
        });
    }

    function handleOrderConfirm(orderNumber, orderCard) {
        showConfirmModal('确认收货？', '确认后订单将完成', () => {
            const statusElement = orderCard.querySelector('.order-status');
            statusElement.textContent = '已完成';
            statusElement.className = 'order-status status-completed';

            // 更新操作按钮
            const actionBtns = orderCard.querySelector('.order-actions');
            actionBtns.innerHTML = `
                <button class="order-action-btn" data-action="review">评价</button>
                <button class="order-action-btn" data-action="refund">申请退款</button>
            `;

            showNotification('确认收货成功', 'success');
        });
    }

    function handleOrderReview(orderNumber) {
        const modal = createModal('order-review', `
            <h3>订单评价</h3>
            <div class="review-form">
                <div class="rating-section">
                    <label>商品评分：</label>
                    <div class="star-rating">
                        <i class="far fa-star" data-rating="1"></i>
                        <i class="far fa-star" data-rating="2"></i>
                        <i class="far fa-star" data-rating="3"></i>
                        <i class="far fa-star" data-rating="4"></i>
                        <i class="far fa-star" data-rating="5"></i>
                    </div>
                </div>
                <div class="comment-section">
                    <label for="review-comment">评价内容：</label>
                    <textarea id="review-comment" placeholder="分享您的购买体验..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn-primary" onclick="submitReview()">提交评价</button>
                </div>
            </div>
        `);

        showModal(modal);

        // 星级评分交互
        const stars = modal.querySelectorAll('.star-rating i');
        stars.forEach((star, index) => {
            star.addEventListener('click', function () {
                const rating = parseInt(this.getAttribute('data-rating'));
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.remove('far');
                        s.classList.add('fas');
                    } else {
                        s.classList.remove('fas');
                        s.classList.add('far');
                    }
                });
            });
        });
    }

    function handleOrderTrack(orderNumber) {
        const modal = createModal('order-track', `
            <h3>物流跟踪</h3>
            <div class="tracking-info">
                <div class="tracking-number">订单号：${orderNumber}</div>
                <div class="tracking-timeline">
                    <div class="tracking-item completed">
                        <div class="tracking-icon"><i class="fas fa-check"></i></div>
                        <div class="tracking-content">
                            <div class="tracking-title">订单已确认</div>
                            <div class="tracking-time">2024-01-27 10:30</div>
                        </div>
                    </div>
                    <div class="tracking-item completed">
                        <div class="tracking-icon"><i class="fas fa-box"></i></div>
                        <div class="tracking-content">
                            <div class="tracking-title">商品已发货</div>
                            <div class="tracking-time">2024-01-27 14:20</div>
                        </div>
                    </div>
                    <div class="tracking-item active">
                        <div class="tracking-icon"><i class="fas fa-truck"></i></div>
                        <div class="tracking-content">
                            <div class="tracking-title">运输中</div>
                            <div class="tracking-time">2024-01-28 09:15</div>
                        </div>
                    </div>
                    <div class="tracking-item">
                        <div class="tracking-icon"><i class="fas fa-home"></i></div>
                        <div class="tracking-content">
                            <div class="tracking-title">待收货</div>
                            <div class="tracking-time">预计今日送达</div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        showModal(modal);
    }

    function handleOrderRefund(orderNumber) {
        const modal = createModal('order-refund', `
            <h3>申请退款</h3>
            <div class="refund-form">
                <div class="form-group">
                    <label>退款原因：</label>
                    <select class="refund-reason">
                        <option value="">请选择退款原因</option>
                        <option value="quality">商品质量问题</option>
                        <option value="size">尺寸不合适</option>
                        <option value="description">与描述不符</option>
                        <option value="damage">商品损坏</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>详细说明：</label>
                    <textarea class="refund-description" placeholder="请详细描述退款原因..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn-primary" onclick="submitRefund()">提交申请</button>
                </div>
            </div>
        `);

        showModal(modal);
    }
}

/**
 * 地址管理功能
 */
function initAddressManagement() {
    // 添加新地址按钮
    const addAddressBtns = document.querySelectorAll('.add-address-btn');
    addAddressBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            showAddressModal();
        });
    });

    // 地址卡片操作
    const addressCards = document.querySelectorAll('.address-card');
    addressCards.forEach(card => {
        // 设为默认地址
        const setDefaultBtn = card.querySelector('.set-default-btn');
        if (setDefaultBtn) {
            setDefaultBtn.addEventListener('click', function () {
                // 移除其他地址的默认状态
                addressCards.forEach(c => c.classList.remove('default'));
                // 设置当前地址为默认
                card.classList.add('default');
                showNotification('默认地址设置成功', 'success');
            });
        }

        // 编辑地址
        const editBtn = card.querySelector('.edit-address-btn');
        if (editBtn) {
            editBtn.addEventListener('click', function () {
                showAddressModal(card);
            });
        }

        // 删除地址
        const deleteBtn = card.querySelector('.delete-address-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function () {
                showConfirmModal('确定要删除这个地址吗？', '', () => {
                    card.style.animation = 'slideOutRight 0.5s ease-in-out';
                    setTimeout(() => {
                        card.remove();
                        showNotification('地址已删除', 'success');
                    }, 500);
                });
            });
        }
    });

    function showAddressModal(existingCard = null) {
        const isEdit = !!existingCard;
        const title = isEdit ? '编辑地址' : '添加新地址';

        let formData = {
            name: '',
            phone: '',
            province: '',
            city: '',
            district: '',
            address: '',
            isDefault: false
        };

        if (isEdit) {
            // 从现有卡片获取数据
            formData.name = existingCard.querySelector('.address-name').textContent;
            formData.phone = existingCard.querySelector('.address-phone').textContent;
            formData.address = existingCard.querySelector('.address-detail').textContent;
            formData.isDefault = existingCard.classList.contains('default');
        }

        const modal = createModal('address-modal', `
            <h3>${title}</h3>
            <form class="address-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-name">收货人姓名</label>
                        <input type="text" id="address-name" value="${formData.name}" required>
                    </div>
                    <div class="form-group">
                        <label for="address-phone">联系电话</label>
                        <input type="tel" id="address-phone" value="${formData.phone}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-province">省份</label>
                        <select id="address-province" required>
                            <option value="">请选择省份</option>
                            <option value="北京">北京</option>
                            <option value="上海">上海</option>
                            <option value="广东">广东</option>
                            <option value="浙江">浙江</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="address-city">城市</label>
                        <select id="address-city" required>
                            <option value="">请选择城市</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="address-district">区县</label>
                        <select id="address-district" required>
                            <option value="">请选择区县</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="address-detail">详细地址</label>
                    <textarea id="address-detail" placeholder="请输入详细地址" required>${formData.address}</textarea>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="address-default" ${formData.isDefault ? 'checked' : ''}>
                        设为默认地址
                    </label>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn-primary">${isEdit ? '保存修改' : '添加地址'}</button>
                </div>
            </form>
        `);

        showModal(modal);

        // 表单提交处理
        const form = modal.querySelector('.address-form');
        form.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(form);
            const addressData = {
                name: document.getElementById('address-name').value,
                phone: document.getElementById('address-phone').value,
                province: document.getElementById('address-province').value,
                city: document.getElementById('address-city').value,
                district: document.getElementById('address-district').value,
                address: document.getElementById('address-detail').value,
                isDefault: document.getElementById('address-default').checked
            };

            if (isEdit) {
                updateAddressCard(existingCard, addressData);
                showNotification('地址修改成功', 'success');
            } else {
                createAddressCard(addressData);
                showNotification('地址添加成功', 'success');
            }

            closeModal();
        });
    }

    function createAddressCard(data) {
        const addressList = document.querySelector('.address-list');
        const newCard = document.createElement('div');
        newCard.className = `address-card ${data.isDefault ? 'default' : ''}`;
        newCard.innerHTML = `
            <div class="address-header">
                <span class="address-name">${data.name}</span>
                <span class="address-phone">${data.phone}</span>
                ${data.isDefault ? '<span class="default-tag">默认</span>' : ''}
            </div>
            <div class="address-detail">${data.province} ${data.city} ${data.district} ${data.address}</div>
            <div class="address-actions">
                <button class="set-default-btn">设为默认</button>
                <button class="edit-address-btn">编辑</button>
                <button class="delete-address-btn">删除</button>
            </div>
        `;

        addressList.appendChild(newCard);

        // 重新绑定事件
        initAddressManagement();
    }

    function updateAddressCard(card, data) {
        card.querySelector('.address-name').textContent = data.name;
        card.querySelector('.address-phone').textContent = data.phone;
        card.querySelector('.address-detail').textContent = `${data.province} ${data.city} ${data.district} ${data.address}`;

        if (data.isDefault) {
            card.classList.add('default');
            // 移除其他地址的默认状态
            document.querySelectorAll('.address-card').forEach(c => {
                if (c !== card) c.classList.remove('default');
            });
        }
    }
}

/**
 * 登录注册交互功能
 */
function initLoginInteractions() {
    // 表单切换
    const switchBtns = document.querySelectorAll('.auth-switch-btn');
    switchBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const targetForm = this.getAttribute('data-target');
            const forms = document.querySelectorAll('.auth-form');

            forms.forEach(form => {
                form.classList.remove('active');
                if (form.id === targetForm) {
                    form.classList.add('active');
                }
            });
        });
    });

    // 密码显示/隐藏
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function () {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // 登录表单提交
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const password = this.querySelector('input[type="password"]').value;

            if (validateLoginForm(email, password)) {
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

                // 模拟登录过程
                setTimeout(() => {
                    showNotification('登录成功！', 'success');
                    // 跳转到用户中心或之前的页面
                    window.location.href = 'user.php';
                }, 2000);
            }
        });
    }

    // 注册表单提交
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(this);
            const email = formData.get('email');
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');

            if (validateRegisterForm(email, password, confirmPassword)) {
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';

                // 模拟注册过程
                setTimeout(() => {
                    showNotification('注册成功！请登录', 'success');
                    // 切换到登录表单
                    document.querySelector('[data-target="loginForm"]').click();
                }, 2000);
            }
        });
    }

    // 社交登录
    const socialLoginBtns = document.querySelectorAll('.social-login-btn');
    socialLoginBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            const platform = this.getAttribute('data-platform');
            showNotification(`正在跳转到${platform}登录...`, 'info');

            // 这里可以添加实际的社交登录逻辑
            setTimeout(() => {
                showNotification(`${platform}登录成功！`, 'success');
            }, 2000);
        });
    });

    function validateLoginForm(email, password) {
        if (!email) {
            showNotification('请输入邮箱地址', 'error');
            return false;
        }

        if (!isValidEmail(email)) {
            showNotification('请输入有效的邮箱地址', 'error');
            return false;
        }

        if (!password) {
            showNotification('请输入密码', 'error');
            return false;
        }

        return true;
    }

    function validateRegisterForm(email, password, confirmPassword) {
        if (!email) {
            showNotification('请输入邮箱地址', 'error');
            return false;
        }

        if (!isValidEmail(email)) {
            showNotification('请输入有效的邮箱地址', 'error');
            return false;
        }

        if (!password) {
            showNotification('请输入密码', 'error');
            return false;
        }

        if (password.length < 6) {
            showNotification('密码长度至少6位', 'error');
            return false;
        }

        if (password !== confirmPassword) {
            showNotification('两次输入的密码不一致', 'error');
            return false;
        }

        return true;
    }
}

/**
 * 心愿单功能
 */
function initWishlistInteractions() {
    // 心愿单按钮点击
    const wishlistBtns = document.querySelectorAll('.wishlist-btn, .product-wishlist-btn');
    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            this.classList.toggle('active');
            const icon = this.querySelector('i');

            if (this.classList.contains('active')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('已添加到心愿单', 'success');
                updateWishlistCount(1);
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('已从心愿单移除', 'info');
                updateWishlistCount(-1);
            }
        });
    });

    function updateWishlistCount(change) {
        const wishlistCount = document.querySelector('.wishlist-count');
        if (wishlistCount) {
            const currentCount = parseInt(wishlistCount.textContent) || 0;
            const newCount = Math.max(0, currentCount + change);
            wishlistCount.textContent = newCount;
        }
    }
}

/**
 * 通知系统
 */
function initNotifications() {
    // 创建通知容器
    if (!document.querySelector('.notification-container')) {
        const container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
}

function showNotification(message, type = 'info', duration = 3000) {
    const container = document.querySelector('.notification-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    notification.innerHTML = `
        <i class="${icons[type] || icons.info}"></i>
        <span class="notification-message">${message}</span>
        <button class="notification-close" aria-label="关闭通知">
            <i class="fas fa-times"></i>
        </button>
    `;

    container.appendChild(notification);

    // 显示动画
    setTimeout(() => notification.classList.add('show'), 10);

    // 关闭按钮
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => hideNotification(notification));

    // 自动关闭
    if (duration > 0) {
        setTimeout(() => hideNotification(notification), duration);
    }

    return notification;
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * 返回顶部功能
 */
function initBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    if (!backToTopBtn) {
        // 创建返回顶部按钮
        const btn = document.createElement('button');
        btn.id = 'back-to-top';
        btn.className = 'back-to-top';
        btn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        btn.setAttribute('aria-label', '返回顶部');
        document.body.appendChild(btn);
    }

    const btn = document.getElementById('back-to-top');

    // 滚动显示/隐藏
    window.addEventListener('scroll', function () {
        if (window.pageYOffset > 300) {
            btn.classList.add('show');
        } else {
            btn.classList.remove('show');
        }
    });

    // 点击返回顶部
    btn.addEventListener('click', function () {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * 表单验证
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');

    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            input.addEventListener('blur', function () {
                validateField(this);
            });

            input.addEventListener('input', function () {
                clearFieldError(this);
            });
        });

        form.addEventListener('submit', function (e) {
            let isValid = true;

            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                showNotification('请检查表单中的错误', 'error');
            }
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');

    clearFieldError(field);

    if (required && !value) {
        showFieldError(field, '此字段为必填项');
        return false;
    }

    if (value) {
        switch (type) {
            case 'email':
                if (!isValidEmail(value)) {
                    showFieldError(field, '请输入有效的邮箱地址');
                    return false;
                }
                break;
            case 'tel':
                if (!isValidPhone(value)) {
                    showFieldError(field, '请输入有效的手机号码');
                    return false;
                }
                break;
            case 'password':
                if (value.length < 6) {
                    showFieldError(field, '密码长度至少6位');
                    return false;
                }
                break;
        }
    }

    return true;
}

function showFieldError(field, message) {
    field.classList.add('error');

    let errorElement = field.parentNode.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        field.parentNode.appendChild(errorElement);
    }

    errorElement.textContent = message;
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * 模态框系统
 */
function initModalSystem() {
    // 点击遮罩关闭模态框
    document.addEventListener('click', function (e) {
        if (e.target.classList.contains('modal-overlay')) {
            closeModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function createModal(id, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content" role="dialog" aria-labelledby="modal-title" aria-modal="true">
            <button class="modal-close" aria-label="关闭">
                <i class="fas fa-times"></i>
            </button>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 关闭按钮事件
    const closeBtn = modal.querySelector('.modal-close');
    closeBtn.addEventListener('click', () => closeModal());

    return modal;
}

function showModal(modal) {
    document.body.appendChild(modal);
    document.body.classList.add('modal-open');

    // 显示动画
    setTimeout(() => modal.classList.add('show'), 10);

    // 焦点管理
    const firstFocusable = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
        firstFocusable.focus();
    }
}

function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
            document.body.classList.remove('modal-open');
        }, 300);
    }
}

function showConfirmModal(title, message, onConfirm) {
    const modal = createModal('confirm', `
        <h3>${title}</h3>
        ${message ? `<p>${message}</p>` : ''}
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
            <button type="button" class="btn-primary confirm-btn">确定</button>
        </div>
    `);

    const confirmBtn = modal.querySelector('.confirm-btn');
    confirmBtn.addEventListener('click', function () {
        onConfirm();
        closeModal();
    });

    showModal(modal);
}

/**
 * 工具提示
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function () {
            showTooltip(this);
        });

        element.addEventListener('mouseleave', function () {
            hideTooltip();
        });
    });
}

function showTooltip(element) {
    const text = element.getAttribute('data-tooltip');
    const position = element.getAttribute('data-tooltip-position') || 'top';

    const tooltip = document.createElement('div');
    tooltip.className = `tooltip tooltip-${position}`;
    tooltip.textContent = text;

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    let top, left;

    switch (position) {
        case 'top':
            top = rect.top - tooltipRect.height - 10;
            left = rect.left + (rect.width - tooltipRect.width) / 2;
            break;
        case 'bottom':
            top = rect.bottom + 10;
            left = rect.left + (rect.width - tooltipRect.width) / 2;
            break;
        case 'left':
            top = rect.top + (rect.height - tooltipRect.height) / 2;
            left = rect.left - tooltipRect.width - 10;
            break;
        case 'right':
            top = rect.top + (rect.height - tooltipRect.height) / 2;
            left = rect.right + 10;
            break;
    }

    tooltip.style.top = `${top}px`;
    tooltip.style.left = `${left}px`;
    tooltip.classList.add('show');
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

/**
 * 工具函数
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 全局函数，供HTML中的onclick使用
window.closeModal = closeModal;
window.showNotification = showNotification;
window.submitReview = function () {
    showNotification('评价提交成功', 'success');
    closeModal();
};
window.submitRefund = function () {
    showNotification('退款申请已提交', 'success');
    closeModal();
};