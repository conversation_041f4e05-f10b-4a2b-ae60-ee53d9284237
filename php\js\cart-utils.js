// 通用购物车操作函数
window.CartUtils = {
    // 添加商品到购物车
    addToCart: function (productData) {
        try {
            // 生成唯一ID
            const itemId = `${productData.product_id}_${productData.size || 'default'}_${productData.color || 'default'}`;

            // 构建商品对象
            const item = {
                id: itemId,
                product_id: productData.product_id,
                name: productData.name,
                image: productData.image || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=200&fit=crop',
                price: parseFloat(productData.price) || 0,
                sale_price: productData.sale_price ? parseFloat(productData.sale_price) : null,
                quantity: parseInt(productData.quantity) || 1,
                size: productData.size || '',
                color: productData.color || '',
                sku: productData.sku || '',
                package: productData.package || ''
            };

            // 添加到购物车
            const success = window.cartStorage.addItem(item);

            if (success) {
                // 同步到服务器
                window.cartStorage.syncToServer();

                // 更新购物车图标数量
                this.updateCartIcon();

                // 显示成功提示
                this.showMessage('商品已添加到购物车！', 'success');

                return true;
            } else {
                this.showMessage('添加失败，请重试', 'error');
                return false;
            }
        } catch (error) {
            console.error('添加到购物车失败:', error);
            this.showMessage('添加失败，请重试', 'error');
            return false;
        }
    },

    // 更新购物车图标数量
    updateCartIcon: function () {
        const totalItems = window.cartStorage.getTotalItems();

        // 更新导航栏购物车数量徽章
        const navCartBadge = document.querySelector('.nav-link-count');
        if (navCartBadge) {
            navCartBadge.textContent = totalItems;
            navCartBadge.style.display = totalItems > 0 ? 'inline' : 'none';
        }

        // 更新其他购物车数量显示
        const cartBadges = document.querySelectorAll('.cart-badge, .cart-count');
        cartBadges.forEach(badge => {
            if (badge) {
                badge.textContent = totalItems;
                badge.style.display = totalItems > 0 ? 'inline' : 'none';
            }
        });

        // 更新购物车图标状态
        const cartIcons = document.querySelectorAll('.cart-icon, .nav-link-icon');
        cartIcons.forEach(icon => {
            if (totalItems > 0) {
                icon.classList.add('has-items');
            } else {
                icon.classList.remove('has-items');
            }
        });

        // 如果没有徽章元素，创建一个
        const cartLink = document.querySelector('a[href="cart.php"]');
        if (cartLink && !navCartBadge && totalItems > 0) {
            const badge = document.createElement('span');
            badge.className = 'nav-link-count';
            badge.textContent = totalItems;
            cartLink.appendChild(badge);
        }
    },

    // 显示消息提示
    showMessage: function (message, type = 'success') {
        // 移除现有的消息
        const existingMessage = document.querySelector('.cart-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息
        const messageDiv = document.createElement('div');
        messageDiv.className = `cart-message cart-message-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;
        messageDiv.textContent = message;

        // 添加CSS动画
        if (!document.querySelector('#cart-message-styles')) {
            const style = document.createElement('style');
            style.id = 'cart-message-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
                .add-to-cart, .add-to-cart-btn {
                    position: relative;
                    overflow: hidden;
                    transition: all 0.3s ease;
                }
                .add-to-cart.adding, .add-to-cart-btn.adding {
                    transform: scale(0.95);
                    opacity: 0.8;
                }
                .add-to-cart.added, .add-to-cart-btn.added {
                    background: #28a745 !important;
                    transform: scale(1.05);
                }
                .add-to-cart.added::after, .add-to-cart-btn.added::after {
                    content: '✓';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 1.2em;
                    color: white;
                    animation: checkmark 0.5s ease;
                }
                @keyframes checkmark {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0); }
                    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
                    100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                }
                .cart-icon.has-items {
                    animation: cartBounce 0.5s ease;
                }
                @keyframes cartBounce {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动隐藏
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 300);
        }, 3000);
    },

    // 初始化购物车功能
    init: function () {
        // 更新购物车图标
        this.updateCartIcon();

        // 绑定所有"加入购物车"按钮
        this.bindAddToCartButtons();

        // 监听存储变化
        window.addEventListener('storage', () => {
            this.updateCartIcon();
        });
    },

    // 绑定加入购物车按钮
    bindAddToCartButtons: function () {
        // 绑定所有加入购物车按钮
        document.querySelectorAll('.add-to-cart, .add-to-cart-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // 获取商品数据
                const productData = this.getProductDataFromButton(button);

                if (productData) {
                    // 添加按钮动效
                    button.classList.add('adding');

                    // 添加到购物车
                    const success = this.addToCart(productData);

                    if (success) {
                        button.classList.add('added');
                        setTimeout(() => {
                            button.classList.remove('adding', 'added');
                        }, 1000);
                    } else {
                        button.classList.remove('adding');
                    }
                } else {
                    this.showMessage('商品信息获取失败', 'error');
                }
            });
        });
    },

    // 从按钮获取商品数据
    getProductDataFromButton: function (button) {
        try {
            // 获取商品ID
            const productId = button.dataset.productId || button.getAttribute('data-product-id');

            if (!productId) {
                console.error('未找到商品ID');
                return null;
            }

            // 查找商品容器
            const productCard = button.closest('.product-card, .product-item, .product-info, .product-details');

            if (!productCard) {
                console.error('未找到商品容器');
                return null;
            }

            // 提取商品信息
            const nameElement = productCard.querySelector('.product-name, .item-name, h1, h2, h3');
            const priceElement = productCard.querySelector('.product-price, .current-price, .price');
            const salePriceElement = productCard.querySelector('.sale-price, .discounted-price');
            const imageElement = productCard.querySelector('img');

            // 获取选中的尺码和颜色（如果有）
            const selectedSize = document.querySelector('.size-option.active')?.dataset.size ||
                document.querySelector('input[name="size"]:checked')?.value || '';
            const selectedColor = document.querySelector('.color-option.active')?.dataset.color ||
                document.querySelector('input[name="color"]:checked')?.value || '';

            // 获取数量
            const qtyInput = document.querySelector('.qty-input, input[name="quantity"]');
            const quantity = qtyInput ? parseInt(qtyInput.value) || 1 : 1;

            // 构建商品数据
            const productData = {
                product_id: parseInt(productId),
                name: nameElement ? nameElement.textContent.trim() : `商品 ${productId}`,
                price: this.extractPrice(priceElement?.textContent),
                sale_price: salePriceElement ? this.extractPrice(salePriceElement.textContent) : null,
                image: imageElement ? imageElement.src : '',
                size: selectedSize,
                color: selectedColor,
                quantity: quantity,
                sku: `PROD-${productId}`,
                package: document.querySelector('input[name="package"]:checked')?.value || ''
            };

            return productData;
        } catch (error) {
            console.error('获取商品数据失败:', error);
            return null;
        }
    },

    // 提取价格数字
    extractPrice: function (priceText) {
        if (!priceText) return 0;
        const match = priceText.match(/[\d,]+\.?\d*/);
        return match ? parseFloat(match[0].replace(/,/g, '')) : 0;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    if (window.CartUtils) {
        window.CartUtils.init();
    }
});
