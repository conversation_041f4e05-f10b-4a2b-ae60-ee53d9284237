<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品卡片样式测试 - COSPlay购物网站</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/layout-styles.css">
    <link rel="stylesheet" href="css/navbar-unified.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 卡片文字修复样式 -->
    <style>
        /* 确保所有文字都可见 */
        body {
            color: #333 !important;
            background-color: #f8f9fa !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #212529 !important;
        }

        p {
            color: #495057 !important;
        }

        /* 商品卡片样式修复 */
        .product-card {
            background-color: #ffffff !important;
            color: #212529 !important;
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: block !important;
            position: relative !important;
        }

        .product-card:hover {
            color: #212529 !important;
            text-decoration: none !important;
            transform: translateY(-8px) !important;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
            border-color: #007bff !important;
        }

        .product-info {
            color: #212529 !important;
            background-color: #ffffff !important;
            padding: 20px !important;
        }

        .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin-bottom: 8px !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
        }

        .product-origin {
            color: #6c757d !important;
            font-size: 0.9rem !important;
            margin-bottom: 12px !important;
        }

        .product-price {
            color: #007bff !important;
            font-weight: 700 !important;
            font-size: 1.2rem !important;
        }

        .product-price-original {
            color: #6c757d !important;
            text-decoration: line-through !important;
            font-size: 1rem !important;
            margin-left: 8px !important;
        }

        .product-badge {
            position: absolute !important;
            top: 12px !important;
            left: 12px !important;
            background-color: #007bff !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 6px 12px !important;
            border-radius: 20px !important;
            z-index: 2 !important;
        }

        .product-badge.new {
            background-color: #28a745 !important;
            color: #ffffff !important;
        }

        .product-badge.hot {
            background-color: #dc3545 !important;
            color: #ffffff !important;
        }

        .product-badge.sale {
            background-color: #ffc107 !important;
            color: #212529 !important;
            top: 12px !important;
            left: auto !important;
            right: 12px !important;
        }

        .add-to-cart {
            background-color: #007bff !important;
            color: #ffffff !important;
            border: none !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
        }

        .add-to-cart:hover {
            background-color: #0056b3 !important;
            color: #ffffff !important;
            transform: scale(1.1) !important;
        }

        .add-to-cart i {
            color: #ffffff !important;
            font-size: 14px !important;
        }

        .product-image-container {
            position: relative !important;
            width: 100% !important;
            height: 280px !important;
            overflow: hidden !important;
            background: #f8f9fa !important;
        }

        .product-image {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: transform 0.3s ease !important;
        }

        .product-card:hover .product-image {
            transform: scale(1.05) !important;
        }

        .product-price-row {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-top: 12px !important;
        }

        /* 测试页面样式 */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .test-section {
            margin-bottom: 40px;
        }

        .test-title {
            color: #212529 !important;
            font-size: 1.8rem !important;
            font-weight: 700 !important;
            margin-bottom: 20px !important;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 24px;
            margin-top: 30px;
        }

        .info-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .info-box h3 {
            color: #1976d2 !important;
            margin-bottom: 10px !important;
        }

        .info-box p {
            color: #424242 !important;
            margin: 5px 0 !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 16px;
            }

            .product-image-container {
                height: 240px !important;
            }

            .product-info {
                padding: 16px !important;
            }

            .product-name {
                font-size: 1rem !important;
            }

            .product-price {
                font-size: 1.1rem !important;
            }
        }

        @media (max-width: 480px) {
            .product-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .product-image-container {
                height: 200px !important;
            }

            .product-name {
                font-size: 0.95rem !important;
            }

            .product-origin {
                font-size: 0.8rem !important;
            }

            .product-price {
                font-size: 1rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">商品卡片样式测试</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p><strong>目标：</strong>确保所有商品卡片的文字都清晰可见</p>
            <p><strong>检查项目：</strong>商品名称、分类、价格、徽章、按钮等文字元素</p>
            <p><strong>响应式：</strong>在不同屏幕尺寸下测试文字显示效果</p>
        </div>

        <div class="test-section">
            <h2 class="test-title">商品卡片展示</h2>
            <div class="product-grid">
                <!-- 热门商品卡片 -->
                <a href="#" class="product-card">
                    <div class="product-image-container">
                        <div class="product-badge hot">热门</div>
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                             alt="原神 - 刻晴星霜华裳" class="product-image">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                        <p class="product-origin">原神</p>
                        <div class="product-price-row">
                            <span class="product-price">¥499.00</span>
                            <span class="product-price-original">¥599.00</span>
                            <button class="add-to-cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </a>

                <!-- 新品卡片 -->
                <a href="#" class="product-card">
                    <div class="product-image-container">
                        <div class="product-badge new">新品</div>
                        <div class="product-badge sale">特价</div>
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                             alt="英雄联盟 - 阿狸" class="product-image">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">英雄联盟 - 阿狸九尾妖狐皮肤</h3>
                        <p class="product-origin">英雄联盟</p>
                        <div class="product-price-row">
                            <span class="product-price">¥649.00</span>
                            <button class="add-to-cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </a>

                <!-- 普通商品卡片 -->
                <a href="#" class="product-card">
                    <div class="product-image-container">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                             alt="鬼灭之刃 - 炭治郎" class="product-image">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">鬼灭之刃 - 炭治郎水之呼吸套装</h3>
                        <p class="product-origin">鬼灭之刃</p>
                        <div class="product-price-row">
                            <span class="product-price">¥599.00</span>
                            <button class="add-to-cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </a>

                <!-- 特价商品卡片 -->
                <a href="#" class="product-card">
                    <div class="product-image-container">
                        <div class="product-badge sale">特价</div>
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                             alt="赛博朋克2077" class="product-image">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">赛博朋克2077 - V女主角套装</h3>
                        <p class="product-origin">赛博朋克</p>
                        <div class="product-price-row">
                            <span class="product-price">¥599.00</span>
                            <span class="product-price-original">¥699.00</span>
                            <button class="add-to-cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="info-box">
            <h3>✅ 检查清单</h3>
            <p>□ 商品名称清晰可见</p>
            <p>□ 商品分类文字可读</p>
            <p>□ 价格信息突出显示</p>
            <p>□ 徽章文字清晰</p>
            <p>□ 按钮图标可见</p>
            <p>□ 悬停效果正常</p>
            <p>□ 响应式布局正确</p>
        </div>
    </div>

    <script>
        // 测试交互效果
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('添加到购物车按钮被点击');
                
                // 简单的视觉反馈
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 检查文字可见性
        function checkTextVisibility() {
            const elements = document.querySelectorAll('.product-name, .product-origin, .product-price');
            elements.forEach(el => {
                const styles = window.getComputedStyle(el);
                const color = styles.color;
                const backgroundColor = styles.backgroundColor;
                console.log(`元素: ${el.className}, 文字颜色: ${color}, 背景色: ${backgroundColor}`);
            });
        }

        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', checkTextVisibility);
    </script>
</body>
</html>
