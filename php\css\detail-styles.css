/* 商品详情页面样式 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 商品详情页面容器 */
.product-detail-page {
    padding: 40px 0;
    min-height: 100vh;
}

/* 商品详情主要布局 */
.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

/* 商品图片区域 */
.product-images {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: #f8f9fa;
    aspect-ratio: 4/5;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-zoom {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.image-zoom:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.image-zoom i {
    color: #666;
    font-size: 16px;
}

/* 缩略图 */
.thumbnail-images {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 5px 0;
}

.thumbnail {
    width: 80px;
    height: 100px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.thumbnail:hover {
    border-color: #007bff;
    transform: scale(1.05);
}

.thumbnail.active {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* 商品信息区域 */
.product-info {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* 商品头部 */
.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.product-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.3;
    flex: 1;
}

.product-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.sale {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.badge.hot {
    background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
}

/* 商品元信息 */
.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.product-origin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.product-sku {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 评分区域 */
.product-rating {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.stars {
    display: flex;
    gap: 3px;
}

.stars i {
    color: #fbbf24;
    font-size: 1.1rem;
}

.rating-text {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.review-count {
    color: #6c757d;
    font-size: 0.95rem;
}

/* 价格区域 */
.product-price {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.current-price {
    font-size: 2.2rem;
    font-weight: 800;
    color: #007bff;
    text-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.original-price {
    font-size: 1.3rem;
    color: #6c757d;
    text-decoration: line-through;
}

.discount {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #ffffff;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* 商品描述 */
.product-description {
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.product-description p {
    color: #495057;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* 商品选项 */
.product-options {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* 尺码选择 */
.size-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.size-option {
    width: 50px;
    height: 50px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    color: #495057;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.size-option:hover {
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
}

.size-option.active {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.size-guide {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 8px;
    align-self: flex-start;
}

.size-guide:hover {
    text-decoration: underline;
}

/* 颜色选择 */
.color-options {
    display: flex;
    gap: 10px;
}

.color-option {
    width: 40px;
    height: 40px;
    border: 3px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.color-option.active {
    border-color: #2c3e50;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #2c3e50;
}

/* 套装选择 */
.package-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.package-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
}

.package-option:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.package-option.selected {
    border-color: #007bff;
    background: #f8f9ff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
}

.package-option input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: #007bff;
}

.package-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    gap: 15px;
}

.package-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.package-price {
    font-weight: 700;
    color: #007bff;
    font-size: 1.2rem;
}

.package-desc {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 购买区域 */
.purchase-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 25px 0;
    border-top: 1px solid #e9ecef;
}

.quantity-selector {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quantity-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.qty-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover:not(:disabled) {
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
}

.qty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.qty-input {
    width: 80px;
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    background: #ffffff;
    transition: border-color 0.3s ease;
}

.qty-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.stock-info {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 购买按钮 */
.purchase-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.add-to-cart-btn,
.buy-now-btn {
    flex: 1;
    height: 50px;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-to-cart-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.add-to-cart-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.add-to-cart-btn.added {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    transform: scale(0.95);
}

.buy-now-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.buy-now-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.wishlist-btn {
    width: 50px;
    height: 50px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #ffffff;
    color: #6c757d;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wishlist-btn:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
    transform: translateY(-2px);
}

.wishlist-btn.active {
    background: #ff6b6b;
    border-color: #ff6b6b;
    color: #ffffff;
}

/* 服务保障 */
.service-guarantees {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 25px 0;
    border-top: 1px solid #e9ecef;
}

.guarantee-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.guarantee-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.guarantee-item i {
    color: #007bff;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.guarantee-item span {
    color: #495057;
    font-weight: 500;
    font-size: 0.95rem;
}

/* 商品标签页 */
.product-tabs {
    background: #ffffff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.tab-btn.active {
    color: #007bff;
    background: #ffffff;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.tab-content {
    position: relative;
    min-height: 400px;
}

.tab-pane {
    display: none;
    padding: 40px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-pane.active {
    display: block;
    opacity: 1;
}

.tab-pane h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    display: inline-block;
}

/* 规格表格 */
.specs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.specs-table tr {
    border-bottom: 1px solid #e9ecef;
}

.specs-table td {
    padding: 15px 20px;
    font-size: 1rem;
}

.specs-table td:first-child {
    font-weight: 600;
    color: #2c3e50;
    background: #f8f9fa;
    width: 30%;
}

.specs-table td:last-child {
    color: #495057;
}

/* 评价区域 */
.rating-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.9rem;
}

.rating-bar span:first-child {
    width: 40px;
    color: #6c757d;
    font-weight: 500;
}

.rating-bar .bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.rating-bar .fill {
    height: 100%;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.rating-bar span:last-child {
    width: 40px;
    text-align: right;
    color: #6c757d;
    font-weight: 500;
}

/* 常见问题 */
.qa-item {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #007bff;
}

.qa-item h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.qa-item p {
    color: #495057;
    line-height: 1.6;
}

/* 图片模态弹窗 */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.image-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.image-modal.active .modal-content {
    transform: scale(1);
}

.modal-content img {
    width: 100%;
    height: auto;
    display: block;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: #ffffff;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Toast 提示 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: #ffffff;
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast-message.active {
    transform: translateX(0);
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .product-detail {
        gap: 40px;
        padding: 30px;
    }

    .product-title {
        font-size: 1.8rem;
    }

    .current-price {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .product-detail {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 20px;
    }

    .product-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .product-title {
        font-size: 1.6rem;
    }

    .current-price {
        font-size: 1.8rem;
    }

    .purchase-buttons {
        flex-direction: column;
    }

    .add-to-cart-btn,
    .buy-now-btn {
        width: 100%;
    }

    .service-guarantees {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1 1 50%;
        min-width: 120px;
    }

    .tab-pane {
        padding: 20px;
    }

    .thumbnail-images {
        justify-content: center;
    }

    .size-options {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .product-detail-page {
        padding: 20px 0;
    }

    .product-detail {
        padding: 15px;
        border-radius: 15px;
    }

    .product-title {
        font-size: 1.4rem;
        line-height: 1.4;
    }

    .current-price {
        font-size: 1.6rem;
    }

    .original-price {
        font-size: 1.1rem;
    }

    .product-badges {
        flex-wrap: wrap;
    }

    .badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .size-option {
        width: 45px;
        height: 45px;
        font-size: 0.9rem;
    }

    .color-option {
        width: 35px;
        height: 35px;
    }

    .package-option {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .package-info {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .quantity-controls {
        justify-content: center;
    }

    .qty-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .qty-input {
        width: 60px;
        height: 35px;
        font-size: 1rem;
    }

    .add-to-cart-btn,
    .buy-now-btn {
        height: 45px;
        font-size: 1rem;
    }

    .wishlist-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .tab-btn {
        padding: 15px 10px;
        font-size: 1rem;
        flex: 1 1 100%;
    }

    .tab-pane {
        padding: 15px;
    }

    .tab-pane h3 {
        font-size: 1.3rem;
    }

    .specs-table td {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .guarantee-item {
        padding: 12px;
    }

    .guarantee-item i {
        font-size: 1.1rem;
    }

    .guarantee-item span {
        font-size: 0.9rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-detail {
    animation: fadeInUp 0.6s ease-out;
}

.product-tabs {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* 滚动条样式 */
.thumbnail-images::-webkit-scrollbar {
    height: 6px;
}

.thumbnail-images::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.thumbnail-images::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 3px;
}

.thumbnail-images::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}