/**
 * COSPlay购物网站 - 增强样式文件
 * 包含高级样式、动画效果、特殊组件等
 */

/* ===== Flash消息 ===== */
.flash-messages {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1050;
    max-width: 400px;
}

.flash-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    animation: slideInRight 0.3s ease;
}

.flash-success {
    background-color: var(--success-color);
    color: var(--white);
}

.flash-error {
    background-color: var(--danger-color);
    color: var(--white);
}

.flash-info {
    background-color: var(--info-color);
    color: var(--white);
}

.flash-warning {
    background-color: var(--warning-color);
    color: var(--gray-800);
}

.flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    margin-left: auto;
}

.flash-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* ===== 分类导航 ===== */
.category-nav {
    background-color: var(--white);
    border-bottom: var(--border-width) solid var(--border-color);
    padding: var(--spacing-lg) 0;
    box-shadow: var(--box-shadow-sm);
}

.category-list {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    min-width: 100px;
}

.category-item:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
    transform: translateY(-2px);
}

.category-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
}

.category-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

/* ===== 页面区块 ===== */
.section {
    padding: var(--spacing-xxl) 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin: 0;
}

.view-all {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
}

.view-all:hover {
    color: var(--primary-dark);
    transform: translateX(4px);
}

/* ===== 空状态 ===== */
.no-products {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    text-align: center;
    color: var(--gray-500);
}

.no-products i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-products p {
    font-size: var(--font-size-lg);
    margin: 0;
}

/* ===== 加载状态 ===== */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== 模态框 ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1060;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: var(--border-width) solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--gray-800);
    background-color: var(--gray-100);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: var(--border-width) solid var(--border-color);
}

/* ===== 工具提示 ===== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-text {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--gray-800);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1070;
}

.tooltip-text::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-800);
}

.tooltip:hover .tooltip-text {
    opacity: 1;
    visibility: visible;
}

/* ===== 徽章 ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: var(--font-weight-semibold);
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--gray-800);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.badge-secondary {
    background-color: var(--gray-500);
    color: var(--white);
}

/* ===== 动画效果 ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translateY(0);
    }

    40%,
    43% {
        transform: translateY(-10px);
    }

    70% {
        transform: translateY(-5px);
    }

    90% {
        transform: translateY(-2px);
    }
}

/* ===== 实用工具类 ===== */
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.d-grid {
    display: grid !important;
}

.justify-content-center {
    justify-content: center !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.text-center {
    text-align: center !important;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.m-0 {
    margin: 0 !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.ml-0 {
    margin-left: 0 !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.p-0 {
    padding: 0 !important;
}

.pt-0 {
    padding-top: 0 !important;
}

.pb-0 {
    padding-bottom: 0 !important;
}

.pl-0 {
    padding-left: 0 !important;
}

.pr-0 {
    padding-right: 0 !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}

.rounded-lg {
    border-radius: var(--border-radius-lg) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

/* ===== 响应式工具类 ===== */
@media (max-width: 768px) {
    .d-md-none {
        display: none !important;
    }

    .d-md-block {
        display: block !important;
    }

    .category-list {
        gap: var(--spacing-lg);
    }

    .category-item {
        min-width: 80px;
        padding: var(--spacing-sm);
    }

    .category-icon {
        font-size: 1.5rem;
    }

    .section {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .section-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 480px) {
    .d-sm-none {
        display: none !important;
    }

    .d-sm-block {
        display: block !important;
    }

    .flash-messages {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }

    .modal-content {
        width: 95%;
    }

    .category-list {
        gap: var(--spacing-md);
    }

    .category-item {
        min-width: 70px;
        padding: var(--spacing-xs);
    }

    .section-title {
        font-size: 1.5rem;
    }
}