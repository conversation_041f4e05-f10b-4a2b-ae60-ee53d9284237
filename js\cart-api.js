/**
 * 购物车API集成模块
 * 提供与后端API的交互功能
 */

class CartAPI {
    constructor() {
        this.apiBase = 'api/cart.php';
        this.csrfToken = this.getCSRFToken();
        this.isOnline = navigator.onLine;
        this.pendingRequests = [];
        
        // 监听网络状态
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncPendingRequests();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    /**
     * 获取CSRF令牌
     */
    getCSRFToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    /**
     * 通用API请求方法
     */
    async request(action, data = {}, method = 'POST') {
        const url = `${this.apiBase}?action=${action}`;
        
        try {
            const options = {
                method: method,
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            if (method === 'POST') {
                const formData = new FormData();
                formData.append('csrf_token', this.csrfToken);
                
                Object.keys(data).forEach(key => {
                    if (data[key] !== null && data[key] !== undefined) {
                        formData.append(key, data[key]);
                    }
                });
                
                options.body = formData;
            }

            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '操作失败');
            }

            return result;
            
        } catch (error) {
            console.error(`API请求失败 [${action}]:`, error);
            
            // 如果是网络错误且离线，添加到待处理队列
            if (!this.isOnline && method === 'POST') {
                this.pendingRequests.push({ action, data, method });
                throw new Error('当前离线，操作已保存，将在网络恢复后同步');
            }
            
            throw error;
        }
    }

    /**
     * 添加商品到购物车
     */
    async addToCart(productId, quantity = 1, attributes = {}) {
        return await this.request('add', {
            product_id: productId,
            quantity: quantity,
            attributes: JSON.stringify(attributes)
        });
    }

    /**
     * 更新购物车商品数量
     */
    async updateCartItem(cartId, quantity) {
        return await this.request('update', {
            cart_id: cartId,
            quantity: quantity
        });
    }

    /**
     * 删除购物车商品
     */
    async removeCartItem(cartId) {
        return await this.request('remove', {
            cart_id: cartId
        });
    }

    /**
     * 获取购物车内容
     */
    async getCart() {
        return await this.request('get', {}, 'GET');
    }

    /**
     * 清空购物车
     */
    async clearCart() {
        return await this.request('clear');
    }

    /**
     * 同步待处理的请求
     */
    async syncPendingRequests() {
        if (this.pendingRequests.length === 0) return;

        const requests = [...this.pendingRequests];
        this.pendingRequests = [];

        for (const req of requests) {
            try {
                await this.request(req.action, req.data, req.method);
            } catch (error) {
                console.error('同步请求失败:', error);
                // 重新添加到队列
                this.pendingRequests.push(req);
            }
        }
    }

    /**
     * 批量操作
     */
    async batchUpdate(operations) {
        const results = [];
        
        for (const op of operations) {
            try {
                const result = await this.request(op.action, op.data);
                results.push({ success: true, result });
            } catch (error) {
                results.push({ success: false, error: error.message });
            }
        }
        
        return results;
    }
}

/**
 * 增强的购物车管理器
 * 集成API功能和离线支持
 */
class EnhancedCartManager extends CartManager {
    constructor() {
        super();
        this.api = new CartAPI();
        this.syncInProgress = false;
        this.lastSyncTime = 0;
        
        // 定期同步
        setInterval(() => this.syncWithServer(), 30000); // 30秒同步一次
    }

    /**
     * 添加商品到购物车（增强版）
     */
    async addToCart(productData, button = null) {
        try {
            // 先更新本地状态
            super.addToCart(productData, button);
            
            // 尝试同步到服务器
            if (navigator.onLine) {
                await this.api.addToCart(productData.id, productData.quantity, productData.specs);
                this.showNotification(`${productData.name} 已添加到购物车`, 'success');
            } else {
                this.showNotification(`${productData.name} 已添加到购物车（离线模式）`, 'warning');
            }
            
        } catch (error) {
            console.error('添加到购物车失败:', error);
            this.showNotification(error.message || '添加失败，请重试', 'error');
        }
    }

    /**
     * 更新商品数量（增强版）
     */
    async updateQuantity(productId, quantity, specs = {}) {
        try {
            // 先更新本地状态
            super.updateQuantity(productId, quantity, specs);
            
            // 尝试同步到服务器
            if (navigator.onLine) {
                // 这里需要cart_id，实际实现中需要维护本地ID到服务器ID的映射
                // await this.api.updateCartItem(cartId, quantity);
            }
            
        } catch (error) {
            console.error('更新数量失败:', error);
            this.showNotification('更新失败，请重试', 'error');
        }
    }

    /**
     * 与服务器同步
     */
    async syncWithServer() {
        if (this.syncInProgress || !navigator.onLine) return;
        
        try {
            this.syncInProgress = true;
            
            // 获取服务器购物车状态
            const serverCart = await this.api.getCart();
            
            // 这里可以实现更复杂的同步逻辑
            // 比如合并本地和服务器的购物车数据
            
            this.lastSyncTime = Date.now();
            
        } catch (error) {
            console.error('同步失败:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * 显示增强的通知
     */
    showNotification(message, type = 'info') {
        // 移除现有通知
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(n => n.remove());

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${iconMap[type] || 'info-circle'}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" aria-label="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // 绑定关闭事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });

        // 自动移除
        const autoHideDelay = type === 'error' ? 8000 : 4000;
        setTimeout(() => {
            if (notification.parentNode) {
                this.hideNotification(notification);
            }
        }, autoHideDelay);
    }

    /**
     * 隐藏通知
     */
    hideNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }

    /**
     * 获取同步状态
     */
    getSyncStatus() {
        return {
            isOnline: navigator.onLine,
            lastSyncTime: this.lastSyncTime,
            syncInProgress: this.syncInProgress,
            pendingRequests: this.api.pendingRequests.length
        };
    }
}

// 替换原有的购物车管理器
document.addEventListener('DOMContentLoaded', () => {
    if (typeof cartManager !== 'undefined') {
        // 如果已经有实例，先清理
        cartManager = null;
    }
    
    cartManager = new EnhancedCartManager();
    window.cartManager = cartManager; // 全局访问
    
    // 添加调试信息
    if (window.location.search.includes('debug=1')) {
        window.cartAPI = cartManager.api;
        console.log('购物车调试模式已启用');
    }
});
