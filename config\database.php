<?php
/**
 * 数据库配置和通用函数
 * COSPlay购物网站
 */

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'cosplay_shop');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// 网站配置
define('SITE_URL', 'http://localhost/cosplay');
define('SITE_NAME', 'COSPlay购物网站');

/**
 * 获取数据库连接
 */
function getDatabase() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // 如果数据库连接失败，使用模拟数据
            error_log("数据库连接失败: " . $e->getMessage());
            $pdo = false;
        }
    }
    
    return $pdo;
}

/**
 * 检查用户是否已登录
 */
function isLoggedIn() {
    return isset($_SESSION['user']) && !empty($_SESSION['user']);
}

/**
 * 获取当前登录用户信息
 */
function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}

/**
 * 重定向函数
 */
function redirect($url, $permanent = false) {
    if (headers_sent()) {
        // 如果头部已发送，使用JavaScript重定向
        echo "<script>window.location.href = '$url';</script>";
        echo "<noscript><meta http-equiv='refresh' content='0;url=$url'></noscript>";
    } else {
        // 使用HTTP头重定向
        $status = $permanent ? 301 : 302;
        http_response_code($status);
        header("Location: $url");
    }
    exit;
}

/**
 * 安全地输出HTML
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 生成CSRF令牌
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 添加Flash消息
 */
function addFlashMessage($type, $message) {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    if (!isset($_SESSION['flash_messages'][$type])) {
        $_SESSION['flash_messages'][$type] = [];
    }
    $_SESSION['flash_messages'][$type][] = $message;
}

/**
 * 获取并清除Flash消息
 */
function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * 模拟用户数据（当数据库不可用时使用）
 */
function getMockUsers() {
    return [
        '<EMAIL>' => [
            'id' => 1,
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'role' => 'admin',
            'avatar' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
            'nickname' => '管理员',
            'phone' => '13800138000',
            'created_at' => '2024-01-01'
        ],
        '<EMAIL>' => [
            'id' => 2,
            'username' => 'user',
            'email' => '<EMAIL>',
            'password' => 'user123',
            'role' => 'user',
            'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
            'nickname' => '普通用户',
            'phone' => '13900139000',
            'created_at' => '2024-01-15'
        ],
        '<EMAIL>' => [
            'id' => 3,
            'username' => 'vip',
            'email' => '<EMAIL>',
            'password' => 'vip123',
            'role' => 'vip',
            'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
            'nickname' => 'VIP用户',
            'phone' => '13700137000',
            'created_at' => '2024-01-10'
        ]
    ];
}

/**
 * 模拟订单数据
 */
function getMockOrders($userId = null) {
    $orders = [
        [
            'id' => 'COS2024011501',
            'user_id' => 2,
            'status' => 'delivered',
            'total_amount' => 514.00,
            'shipping_fee' => 15.00,
            'created_at' => '2024-01-15 14:30:00',
            'items' => [
                [
                    'product_id' => 201,
                    'name' => '刻晴 - 星霜华裳',
                    'origin' => '原神',
                    'image' => 'images/product-1.jpg',
                    'price' => 499.00,
                    'quantity' => 1,
                    'specs' => ['尺码: M', '颜色: 紫色']
                ]
            ]
        ],
        [
            'id' => 'COS2024011802',
            'user_id' => 2,
            'status' => 'shipped',
            'total_amount' => 613.00,
            'shipping_fee' => 15.00,
            'created_at' => '2024-01-18 10:15:00',
            'items' => [
                [
                    'product_id' => 202,
                    'name' => '宝可梦 - 皮卡丘',
                    'origin' => '宝可梦',
                    'image' => 'images/product-2.jpg',
                    'price' => 299.00,
                    'quantity' => 2,
                    'specs' => ['尺码: L', '颜色: 黄色']
                ]
            ]
        ],
        [
            'id' => 'COS2024012003',
            'user_id' => 2,
            'status' => 'processing',
            'total_amount' => 614.00,
            'shipping_fee' => 15.00,
            'created_at' => '2024-01-20 16:45:00',
            'items' => [
                [
                    'product_id' => 203,
                    'name' => '鬼灭之刃 - 炭治郎',
                    'origin' => '鬼灭之刃',
                    'image' => 'images/product-3.jpg',
                    'price' => 599.00,
                    'quantity' => 1,
                    'specs' => ['尺码: L', '颜色: 黑绿']
                ]
            ]
        ],
        [
            'id' => 'COS2024012204',
            'user_id' => 2,
            'status' => 'pending',
            'total_amount' => 474.00,
            'shipping_fee' => 15.00,
            'created_at' => '2024-01-22 09:30:00',
            'items' => [
                [
                    'product_id' => 204,
                    'name' => '海贼王 - 路飞',
                    'origin' => '海贼王',
                    'image' => 'images/product-4.jpg',
                    'price' => 459.00,
                    'quantity' => 1,
                    'specs' => ['尺码: M', '颜色: 红色']
                ]
            ]
        ]
    ];
    
    if ($userId !== null) {
        return array_filter($orders, function($order) use ($userId) {
            return $order['user_id'] == $userId;
        });
    }
    
    return $orders;
}

/**
 * 获取订单状态的中文名称
 */
function getOrderStatusName($status) {
    $statusNames = [
        'pending' => '待付款',
        'paid' => '已付款',
        'processing' => '处理中',
        'shipped' => '已发货',
        'delivered' => '已送达',
        'cancelled' => '已取消',
        'refunded' => '已退款'
    ];
    
    return $statusNames[$status] ?? '未知状态';
}

/**
 * 格式化价格
 */
function formatPrice($price) {
    return '¥' . number_format($price, 2);
}

/**
 * 格式化日期
 */
function formatDate($date, $format = 'Y-m-d H:i') {
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    return $date->format($format);
}

// 初始化数据库连接（可选）
$db = getDatabase();
?>
