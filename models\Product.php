<?php
/**
 * 商品模型类
 * 处理商品相关的数据库操作
 */

class Product {
    private $db;
    private $cache;

    public function __construct() {
        $this->db = getDB();
        $this->cache = getCache();
    }
    
    /**
     * 获取所有商品
     */
    public function getAllProducts($limit = null, $offset = 0, $filters = []) {
        $sql = "SELECT p.*, c.name as category_name, 
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active'";
        
        $params = [];
        
        // 应用筛选条件
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.tags LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['min_price'])) {
            $sql .= " AND (COALESCE(p.sale_price, p.price) >= :min_price)";
            $params['min_price'] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND (COALESCE(p.sale_price, p.price) <= :max_price)";
            $params['max_price'] = $filters['max_price'];
        }
        
        // 排序
        $order_by = $filters['order_by'] ?? 'created_at';
        $order_dir = $filters['order_dir'] ?? 'DESC';
        $sql .= " ORDER BY p.{$order_by} {$order_dir}";
        
        // 分页
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            $params['limit'] = $limit;
            $params['offset'] = $offset;
        }
        
        $stmt = $this->db->prepare($sql);
        
        // 绑定参数
        foreach ($params as $key => $value) {
            if (in_array($key, ['limit', 'offset'])) {
                $stmt->bindValue(":$key", (int)$value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue(":$key", $value);
            }
        }
        
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 根据ID获取商品详情
     */
    public function getProductById($id) {
        // 尝试从缓存获取
        $cache_key = "product_detail_$id";
        $cached_product = $this->cache->get($cache_key);

        if ($cached_product !== null) {
            return $cached_product;
        }

        try {
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.id = :id AND p.status = 'active'";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();

            $product = $stmt->fetch();

            if ($product) {
                // 获取商品图片
                $product['images'] = $this->getProductImages($id);

                // 获取商品属性
                $product['attributes'] = $this->getProductAttributes($id);

                // 获取商品评价统计
                $product['review_stats'] = $this->getProductReviewStats($id);

                // 缓存结果
                $this->cache->set($cache_key, $product, 1800); // 30分钟缓存
            }

            return $product;

        } catch (PDOException $e) {
            logError("获取商品详情失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据slug获取商品详情
     */
    public function getProductBySlug($slug) {
        $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.slug = :slug AND p.status = 'active'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':slug', $slug);
        $stmt->execute();
        
        $product = $stmt->fetch();
        
        if ($product) {
            // 获取商品图片
            $product['images'] = $this->getProductImages($product['id']);
            
            // 获取商品属性
            $product['attributes'] = $this->getProductAttributes($product['id']);
            
            // 获取商品评价统计
            $product['review_stats'] = $this->getProductReviewStats($product['id']);
        }
        
        return $product;
    }
    
    /**
     * 获取商品图片
     */
    public function getProductImages($product_id) {
        $sql = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY sort_order ASC, is_primary DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 获取商品属性
     */
    public function getProductAttributes($product_id) {
        $sql = "SELECT * FROM product_attributes WHERE product_id = :product_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 获取商品评价统计
     */
    public function getProductReviewStats($product_id) {
        $sql = "SELECT 
                    COUNT(*) as total_reviews,
                    AVG(rating) as average_rating,
                    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
                FROM product_reviews 
                WHERE product_id = :product_id AND status = 'approved'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * 获取特色商品
     */
    public function getFeaturedProducts($limit = 8) {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active' AND p.featured = 1 
                ORDER BY p.created_at DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 获取最新商品
     */
    public function getLatestProducts($limit = 8) {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active' 
                ORDER BY p.created_at DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 获取相关商品
     */
    public function getRelatedProducts($product_id, $category_id, $limit = 4) {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active' AND p.category_id = :category_id AND p.id != :product_id
                ORDER BY RAND() 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 搜索商品
     */
    public function searchProducts($query, $limit = 20, $offset = 0) {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                       MATCH(p.name, p.description, p.tags) AGAINST(:query IN NATURAL LANGUAGE MODE) as relevance
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active' 
                AND (p.name LIKE :like_query OR p.description LIKE :like_query OR p.tags LIKE :like_query
                     OR MATCH(p.name, p.description, p.tags) AGAINST(:query IN NATURAL LANGUAGE MODE))
                ORDER BY relevance DESC, p.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $like_query = '%' . $query . '%';
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':query', $query);
        $stmt->bindParam(':like_query', $like_query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * 获取商品总数
     */
    public function getProductCount($filters = []) {
        $sql = "SELECT COUNT(*) FROM products p WHERE p.status = 'active'";
        $params = [];
        
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE :search OR p.description LIKE :search OR p.tags LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(":$key", $value);
        }
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    /**
     * 更新商品库存
     */
    public function updateStock($product_id, $quantity) {
        $sql = "UPDATE products SET stock_quantity = stock_quantity - :quantity WHERE id = :product_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    /**
     * 检查商品库存
     */
    public function checkStock($product_id, $quantity) {
        $sql = "SELECT stock_quantity FROM products WHERE id = :product_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $stmt->execute();
        $stock = $stmt->fetchColumn();
        
        return $stock >= $quantity;
    }
}
?>
