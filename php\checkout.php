<?php
/**
 * COSPlay购物网站 - 订单结算页面
 * 处理订单确认、地址选择和支付
 */

// 引入配置文件
require_once 'config/database.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// 获取当前用户信息
$current_user = getCurrentUser();

// 页面信息设置
$page_title = '订单结算 - COSPlay购物网站';
$page_description = '确认订单信息，选择收货地址和支付方式';

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.checkout-title {
    color: #212529 !important;
}

.section-title {
    color: #212529 !important;
}

.step-text {
    color: #495057 !important;
}

.step.active .step-text {
    color: #007bff !important;
}

.recipient-name {
    color: #212529 !important;
}

.recipient-phone {
    color: #6c757d !important;
}

.address-detail {
    color: #495057 !important;
}

.item-name {
    color: #212529 !important;
}

.item-origin {
    color: #6c757d !important;
}

.item-price, .item-total {
    color: #007bff !important;
}

.shipping-name, .payment-name {
    color: #212529 !important;
}

.shipping-time {
    color: #6c757d !important;
}

.shipping-price {
    color: #007bff !important;
}

.summary-label {
    color: #495057 !important;
}

.summary-value {
    color: #212529 !important;
}

.summary-row.total .summary-value {
    color: #007bff !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.add-address-btn, .apply-coupon-btn, .submit-order-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.back-to-cart-btn {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.edit-address {
    color: #007bff !important;
    background-color: transparent !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保表单文字可见 */
.coupon-code, .note-textarea {
    color: #495057 !important;
    background-color: #ffffff !important;
}

/* 确保卡片背景和文字可见 */
.checkout-section, .order-summary {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.address-item, .shipping-option, .payment-option, .coupon-item {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.address-item:hover, .shipping-option:hover, .payment-option:hover, .coupon-item:hover {
    background-color: #e9ecef !important;
}

/* 确保优惠券信息可见 */
.coupon-name {
    color: #212529 !important;
}

.coupon-condition {
    color: #6c757d !important;
}

.coupon-amount {
    color: #007bff !important;
}

/* 确保安全提示可见 */
.security-notice {
    color: #6c757d !important;
}
</style>

<style>
/* 修复图片路径和一些特定样式 */
.item-image img {
    background: var(--gray-100);
}

/* 确保radio按钮可见 */
input[type="radio"] {
    width: auto !important;
    margin-right: var(--spacing-sm);
}

/* 修复grid布局在旧浏览器中的兼容性 */
.order-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-md);
}

.order-item .item-image {
    flex: 0 0 80px;
}

.order-item .item-info {
    flex: 1;
    min-width: 200px;
}

.order-item .item-price,
.order-item .item-quantity,
.order-item .item-total {
    flex: 0 0 auto;
    min-width: 60px;
}

/* 支付图标颜色 */
.fab.fa-alipay {
    color: #1677ff;
}

.fab.fa-weixin {
    color: #07c160;
}

.fas.fa-credit-card {
    color: #ff6b35;
}

.fas.fa-money-bill-wave {
    color: #28a745;
}
</style>

<div class="checkout-page">
    <div class="container">
        <div class="checkout-header">
            <h1 class="checkout-title">
                <i class="fas fa-credit-card"></i>
                订单结算
            </h1>
            <div class="checkout-steps">
                <div class="step completed">
                    <span class="step-number">1</span>
                    <span class="step-text">购物车</span>
                </div>
                <div class="step active">
                    <span class="step-number">2</span>
                    <span class="step-text">确认订单</span>
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    <span class="step-text">支付</span>
                </div>
                <div class="step">
                    <span class="step-number">4</span>
                    <span class="step-text">完成</span>
                </div>
            </div>
        </div>

        <form class="checkout-form" method="POST" action="order.php">
            <div class="checkout-content">
                <!-- 收货地址 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            收货地址
                        </h3>
                        <button type="button" class="add-address-btn">
                            <i class="fas fa-plus"></i>
                            添加新地址
                        </button>
                    </div>

                    <div class="address-list">
                        <label class="address-item">
                            <input type="radio" name="address_id" value="1" checked>
                            <div class="address-content">
                                <div class="address-header">
                                    <span class="recipient-name">张三</span>
                                    <span class="recipient-phone">138****8888</span>
                                    <span class="default-badge">默认</span>
                                </div>
                                <div class="address-detail">
                                    北京市朝阳区某某街道某某小区1号楼101室
                                </div>
                            </div>
                            <div class="address-actions">
                                <button type="button" class="edit-address">编辑</button>
                            </div>
                        </label>

                        <label class="address-item">
                            <input type="radio" name="address_id" value="2">
                            <div class="address-content">
                                <div class="address-header">
                                    <span class="recipient-name">李四</span>
                                    <span class="recipient-phone">139****9999</span>
                                </div>
                                <div class="address-detail">
                                    上海市浦东新区某某路某某号某某大厦2001室
                                </div>
                            </div>
                            <div class="address-actions">
                                <button type="button" class="edit-address">编辑</button>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- 商品清单 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-shopping-bag"></i>
                            商品清单
                        </h3>
                    </div>

                    <div class="order-items">
                        <div class="order-item">
                            <div class="item-image">
                                <img src="images/product-1.jpg" alt="刻晴 - 星霜华裳">
                            </div>
                            <div class="item-info">
                                <h4 class="item-name">刻晴 - 星霜华裳</h4>
                                <p class="item-origin">原神</p>
                                <div class="item-specs">
                                    <span class="spec">尺码: M</span>
                                    <span class="spec">颜色: 紫色</span>
                                </div>
                            </div>
                            <div class="item-price">¥499</div>
                            <div class="item-quantity">×1</div>
                            <div class="item-total">¥499</div>
                        </div>

                        <div class="order-item">
                            <div class="item-image">
                                <img src="images/product-2.jpg" alt="宝可梦 - 皮卡丘">
                            </div>
                            <div class="item-info">
                                <h4 class="item-name">宝可梦 - 皮卡丘</h4>
                                <p class="item-origin">宝可梦</p>
                                <div class="item-specs">
                                    <span class="spec">尺码: L</span>
                                    <span class="spec">颜色: 黄色</span>
                                </div>
                            </div>
                            <div class="item-price">¥299</div>
                            <div class="item-quantity">×2</div>
                            <div class="item-total">¥598</div>
                        </div>
                    </div>
                </div>

                <!-- 配送方式 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-truck"></i>
                            配送方式
                        </h3>
                    </div>

                    <div class="shipping-options">
                        <label class="shipping-option">
                            <input type="radio" name="shipping_method" value="standard" checked>
                            <div class="shipping-info">
                                <span class="shipping-name">标准配送</span>
                                <span class="shipping-time">3-5个工作日</span>
                            </div>
                            <div class="shipping-price">¥15</div>
                        </label>

                        <label class="shipping-option">
                            <input type="radio" name="shipping_method" value="express">
                            <div class="shipping-info">
                                <span class="shipping-name">快速配送</span>
                                <span class="shipping-time">1-2个工作日</span>
                            </div>
                            <div class="shipping-price">¥25</div>
                        </label>

                        <label class="shipping-option">
                            <input type="radio" name="shipping_method" value="same_day">
                            <div class="shipping-info">
                                <span class="shipping-name">当日达</span>
                                <span class="shipping-time">当日送达（限部分地区）</span>
                            </div>
                            <div class="shipping-price">¥35</div>
                        </label>
                    </div>
                </div>

                <!-- 支付方式 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-credit-card"></i>
                            支付方式
                        </h3>
                    </div>

                    <div class="payment-options">
                        <label class="payment-option">
                            <input type="radio" name="payment_method" value="alipay" checked>
                            <div class="payment-info">
                                <i class="fab fa-alipay payment-icon"></i>
                                <span class="payment-name">支付宝</span>
                            </div>
                        </label>

                        <label class="payment-option">
                            <input type="radio" name="payment_method" value="wechat">
                            <div class="payment-info">
                                <i class="fab fa-weixin payment-icon"></i>
                                <span class="payment-name">微信支付</span>
                            </div>
                        </label>

                        <label class="payment-option">
                            <input type="radio" name="payment_method" value="unionpay">
                            <div class="payment-info">
                                <i class="fas fa-credit-card payment-icon"></i>
                                <span class="payment-name">银联支付</span>
                            </div>
                        </label>

                        <label class="payment-option">
                            <input type="radio" name="payment_method" value="cod">
                            <div class="payment-info">
                                <i class="fas fa-money-bill-wave payment-icon"></i>
                                <span class="payment-name">货到付款</span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- 优惠券 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-ticket-alt"></i>
                            优惠券
                        </h3>
                    </div>

                    <div class="coupon-section">
                        <div class="coupon-input">
                            <input type="text" placeholder="请输入优惠券代码" class="coupon-code">
                            <button type="button" class="apply-coupon-btn">使用</button>
                        </div>

                        <div class="available-coupons">
                            <h4>可用优惠券</h4>
                            <div class="coupon-list">
                                <label class="coupon-item">
                                    <input type="radio" name="coupon_id" value="1">
                                    <div class="coupon-info">
                                        <div class="coupon-amount">¥50</div>
                                        <div class="coupon-details">
                                            <span class="coupon-name">新用户专享券</span>
                                            <span class="coupon-condition">满500可用</span>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单备注 -->
                <div class="checkout-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-comment"></i>
                            订单备注
                        </h3>
                    </div>

                    <div class="order-note">
                        <textarea name="order_note" placeholder="如有特殊要求，请在此留言..." class="note-textarea"></textarea>
                    </div>
                </div>
            </div>

            <!-- 订单摘要 -->
            <div class="checkout-sidebar">
                <div class="order-summary">
                    <h3 class="summary-title">订单摘要</h3>

                    <div class="summary-items">
                        <div class="summary-row">
                            <span class="summary-label">商品小计</span>
                            <span class="summary-value">¥1,097</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">运费</span>
                            <span class="summary-value">¥15</span>
                        </div>
                        <div class="summary-row discount">
                            <span class="summary-label">优惠券</span>
                            <span class="summary-value">-¥50</span>
                        </div>
                        <div class="summary-divider"></div>
                        <div class="summary-row total">
                            <span class="summary-label">应付总额</span>
                            <span class="summary-value">¥1,062</span>
                        </div>
                    </div>

                    <div class="checkout-actions">
                        <button type="button" class="back-to-cart-btn">
                            <i class="fas fa-arrow-left"></i>
                            返回购物车
                        </button>
                        <button type="submit" class="submit-order-btn">
                            <i class="fas fa-lock"></i>
                            提交订单
                        </button>
                    </div>

                    <div class="security-notice">
                        <i class="fas fa-shield-alt"></i>
                        <span>您的支付信息将被安全加密</span>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 地址选择
    document.querySelectorAll('input[name="address_id"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // 更新选中的地址
            console.log('选择地址:', this.value);
        });
    });

    // 配送方式选择
    document.querySelectorAll('input[name="shipping_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updateShippingCost();
        });
    });

    // 支付方式选择
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            console.log('选择支付方式:', this.value);
        });
    });

    // 优惠券使用
    document.querySelector('.apply-coupon-btn').addEventListener('click', function() {
        const couponCode = document.querySelector('.coupon-code').value;
        if (couponCode.trim()) {
            // 这里应该验证优惠券
            console.log('使用优惠券:', couponCode);
        }
    });

    // 更新运费
    function updateShippingCost() {
        const selectedShipping = document.querySelector('input[name="shipping_method"]:checked');
        const shippingCost = selectedShipping.closest('.shipping-option').querySelector('.shipping-price').textContent;
        
        // 更新订单摘要中的运费
        document.querySelector('.summary-row:nth-child(2) .summary-value').textContent = shippingCost;
        
        // 重新计算总价
        updateTotal();
    }

    // 更新总价
    function updateTotal() {
        // 这里应该重新计算总价
        console.log('更新总价');
    }

    // 返回购物车
    document.querySelector('.back-to-cart-btn').addEventListener('click', function() {
        window.location.href = 'cart.php';
    });

    // 提交订单
    document.querySelector('.checkout-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 验证表单
        const addressSelected = document.querySelector('input[name="address_id"]:checked');
        const paymentSelected = document.querySelector('input[name="payment_method"]:checked');
        
        if (!addressSelected) {
            alert('请选择收货地址');
            return;
        }
        
        if (!paymentSelected) {
            alert('请选择支付方式');
            return;
        }
        
        // 提交订单
        alert('订单提交成功！即将跳转到支付页面...');
        // 这里应该提交到后端处理
    });
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
