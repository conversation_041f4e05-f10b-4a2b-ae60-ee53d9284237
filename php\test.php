<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS测试页面</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/enhanced-styles.css">
    <link rel="stylesheet" href="../css/card-styles.css">
    <link rel="stylesheet" href="css/layout-styles.css">
    <link rel="stylesheet" href="css/detail-styles.css">
    <link rel="stylesheet" href="css/category-fix.css">
    <link rel="stylesheet" href="css/category-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

    <!-- 调试样式 -->
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #000;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }

        /* 确保文字可见 */
        body {
            color: #333 !important;
            background: #f5f5f5 !important;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #222 !important;
        }

        p {
            color: #555 !important;
        }

        .text-primary {
            color: #007bff !important;
        }

        .text-muted {
            color: #6c757d !important;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        CSS文件加载状态检查
    </div>

    <div class="container" style="padding: 20px;">
        <h1>CSS测试页面</h1>
        
        <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2>文字颜色测试</h2>
            <p>这是普通段落文字，应该是深灰色。</p>
            <p class="text-primary">这是主色调文字，应该是蓝色。</p>
            <p class="text-muted">这是静音文字，应该是浅灰色。</p>
            
            <h3>按钮测试</h3>
            <button class="btn btn-primary">主要按钮</button>
            <button class="btn btn-secondary">次要按钮</button>
            <button class="btn btn-success">成功按钮</button>
            
            <h3>导航栏测试</h3>
            <nav class="navbar">
                <div class="container">
                    <div class="navbar-container">
                        <a href="#" class="logo">COS<span>Play</span></a>
                        <div class="nav-links">
                            <a href="#" class="nav-link">
                                <i class="fas fa-home nav-link-icon"></i>
                                <span class="nav-link-text">首页</span>
                            </a>
                            <a href="#" class="nav-link">
                                <i class="fas fa-shopping-cart nav-link-icon"></i>
                                <span class="nav-link-count">3</span>
                                <span class="nav-link-text">购物车</span>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
            
            <h3>商品卡片测试</h3>
            <div class="product-card" style="max-width: 300px; margin: 20px 0;">
                <div class="product-image-container">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="测试商品" class="product-image">
                    <div class="product-badge">热门</div>
                </div>
                <div class="product-info">
                    <h3 class="product-name">测试商品名称</h3>
                    <p class="product-origin">测试分类</p>
                    <div class="product-price-row">
                        <span class="product-price">¥299.00</span>
                        <button class="add-to-cart">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h2>CSS变量测试</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <div style="background: var(--primary-color); color: var(--white); padding: 10px; border-radius: 4px;">
                    主色调背景 + 白色文字
                </div>
                <div style="background: var(--white); color: var(--gray-800); padding: 10px; border-radius: 4px; border: 1px solid var(--border-color);">
                    白色背景 + 深灰色文字
                </div>
                <div style="background: var(--gray-100); color: var(--gray-700); padding: 10px; border-radius: 4px;">
                    浅灰背景 + 灰色文字
                </div>
                <div style="background: var(--success-color); color: var(--white); padding: 10px; border-radius: 4px;">
                    成功色背景 + 白色文字
                </div>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2>表单测试</h2>
            <form>
                <div class="form-group">
                    <label class="form-label">测试输入框</label>
                    <input type="text" class="form-control" placeholder="请输入内容">
                </div>
                <div class="form-group">
                    <label class="form-label">测试文本域</label>
                    <textarea class="form-control" rows="3" placeholder="请输入多行内容"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">提交</button>
            </form>
        </div>
    </div>
</body>
</html>
