<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COSPlay购物网站 - 首页 (修复测试)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式 */
        body {
            color: #333 !important;
            background-color: #f8f9fa !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #212529 !important;
        }

        p {
            color: #495057 !important;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            box-sizing: border-box;
        }

        .section-title {
            color: #212529 !important;
            font-weight: 700 !important;
            font-size: 1.8rem !important;
            margin: 0 0 24px 0 !important;
        }

        /* 修复商品卡片样式 */
        .product-card {
            background: #ffffff !important;
            border: 1px solid #e9ecef !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            overflow: hidden !important;
            position: relative !important;
        }

        .product-card:hover {
            text-decoration: none !important;
            transform: none !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        .product-info {
            background: #ffffff !important;
            padding: 20px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: space-between !important;
            min-height: 140px !important;
            box-sizing: border-box !important;
            position: relative !important;
            z-index: 10 !important;
        }

        /* 修复商品信息区域文字颜色 */
        .product-info * {
            color: inherit !important;
        }

        /* 确保 h3 标题可见 - 修复为深色 */
        .product-info h1, .product-info h2, .product-info h3, .product-info h4, .product-info h5, .product-info h6 {
            color: #212529 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-info p {
            color: #6c757d !important;
        }

        .product-info span {
            color: inherit !important;
        }

        .product-info div {
            color: inherit !important;
        }

        .product-name {
            color: #212529 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin: 0 0 8px 0 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            position: relative !important;
            z-index: 15 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            filter: none !important;
            transform: none !important;
        }

        /* 强制 h3 标签为深色可见 */
        .product-info h3.product-name {
            color: #212529 !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-origin {
            color: #6c757d !important;
            font-size: 0.9rem !important;
            margin: 0 0 12px 0 !important;
            font-weight: 500 !important;
            line-height: 1.3 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            position: relative !important;
            z-index: 12 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 强制 p 标签为灰色可见 */
        .product-info p.product-origin {
            color: #6c757d !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-price-row {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-top: auto !important;
            padding-top: 8px !important;
            flex-shrink: 0 !important;
            gap: 8px !important;
            position: relative !important;
            z-index: 20 !important;
            transform: none !important;
            backface-visibility: hidden !important;
            -webkit-backface-visibility: hidden !important;
        }

        .product-price {
            color: #212529 !important;
            font-weight: 800 !important;
            font-size: 1.25rem !important;
            line-height: 1.2 !important;
            margin: 0 !important;
            letter-spacing: 0 !important;
            position: relative !important;
            z-index: 25 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
            transform: none !important;
            backface-visibility: hidden !important;
            -webkit-backface-visibility: hidden !important;
            will-change: auto !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 强制 span 标签为深色可见 */
        .product-info span.product-price {
            color: #212529 !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .product-price-original {
            color: #8e9ba8 !important;
            text-decoration: line-through !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            margin: 0 0 0 8px !important;
            line-height: 1.2 !important;
            position: relative !important;
            z-index: 22 !important;
            -webkit-font-smoothing: auto !important;
            -moz-osx-font-smoothing: auto !important;
            text-rendering: optimizeSpeed !important;
        }

        .product-price-container {
            display: flex !important;
            align-items: baseline !important;
            gap: 6px !important;
            flex: 1 !important;
            min-width: 0 !important;
            position: relative !important;
            z-index: 20 !important;
            transform: none !important;
            backface-visibility: hidden !important;
            -webkit-backface-visibility: hidden !important;
        }

        .product-badge {
            background-color: #007bff !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 6px 12px !important;
            border-radius: 20px !important;
            position: absolute !important;
            top: 12px !important;
            left: 12px !important;
            z-index: 2 !important;
        }

        .product-badge.new {
            background-color: #28a745 !important;
        }

        .product-badge.hot {
            background-color: #dc3545 !important;
        }

        .product-badge.sale {
            background-color: #ffc107 !important;
            color: #212529 !important;
            top: 12px !important;
            left: auto !important;
            right: 12px !important;
        }

        .add-to-cart {
            background-color: #007bff !important;
            color: #ffffff !important;
            border: none !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            flex-shrink: 0 !important;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
            position: relative !important;
            z-index: 30 !important;
        }

        .add-to-cart:hover {
            background-color: #0056b3 !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
        }

        .add-to-cart i {
            color: #ffffff !important;
            font-size: 14px !important;
            line-height: 1 !important;
            position: relative !important;
            z-index: 35 !important;
        }

        /* 商品网格布局 */
        .product-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
            gap: 24px !important;
            margin-top: 30px !important;
        }

        .product-image-container {
            position: relative !important;
            width: 100% !important;
            height: 280px !important;
            overflow: hidden !important;
            background: #f8f9fa !important;
            z-index: 1 !important;
        }

        .product-image {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: transform 0.3s ease !important;
            position: relative !important;
            z-index: 2 !important;
        }

        .product-card:hover .product-image {
            transform: none !important;
        }

        /* 防止整个商品卡片模糊 - 移除所有3D变换 */
        .product-card {
            transform: none !important;
            -webkit-transform: none !important;
            backface-visibility: visible !important;
            -webkit-backface-visibility: visible !important;
            perspective: none !important;
            -webkit-perspective: none !important;
        }

        .product-card:hover {
            transform: none !important;
            -webkit-transform: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="section-title">热门角色专区 (修复后测试)</h2>
        
        <div class="product-grid">
            <!-- 测试商品卡片 1 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge">热门</div>
                    <div class="product-badge sale">特价</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="原神 - 刻晴星霜华裳" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">原神 - 刻晴星霜华裳</h3>
                    <p class="product-origin">原神</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥499.00</span>
                            <span class="product-price-original">¥599.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>

            <!-- 测试商品卡片 2 -->
            <a href="#" class="product-card">
                <div class="product-image-container">
                    <div class="product-badge new">新品</div>
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=500&fit=crop" 
                         alt="英雄联盟 - 阿狸" class="product-image">
                </div>
                <div class="product-info">
                    <h3 class="product-name">英雄联盟 - 阿狸</h3>
                    <p class="product-origin">英雄联盟</p>
                    <div class="product-price-row">
                        <div class="product-price-container">
                            <span class="product-price">¥649.00</span>
                        </div>
                        <button class="add-to-cart" title="添加到购物车">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <script>
        // 检查修复后的 h3 标题显示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 修复后的 H3 标题检查 ===');
            
            const h3Elements = document.querySelectorAll('h3.product-name');
            console.log('找到的 h3 标题数量:', h3Elements.length);
            
            h3Elements.forEach((h3, index) => {
                const styles = window.getComputedStyle(h3);
                console.log(`H3 标题 ${index + 1}:`, {
                    text: h3.textContent,
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity,
                    color: styles.color,
                    fontSize: styles.fontSize,
                    fontWeight: styles.fontWeight,
                    zIndex: styles.zIndex
                });
                
                // 检查是否有任何隐藏的样式
                if (styles.display === 'none' || styles.visibility === 'hidden' || styles.opacity === '0') {
                    console.warn(`警告: H3 标题 ${index + 1} 可能不可见!`);
                } else {
                    console.log(`✓ H3 标题 ${index + 1} 显示正常`);
                }
            });
        });
    </script>
</body>
</html>
