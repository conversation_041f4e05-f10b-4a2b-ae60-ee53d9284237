<?php
/**
 * COSPlay购物网站 - 用户登录页面
 * 提供用户登录和注册功能
 */

// 启动输出缓冲
ob_start();

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 模拟用户数据库
$users = [
    '<EMAIL>' => [
        'id' => 1,
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'role' => 'admin',
        'avatar' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        'nickname' => '管理员',
        'phone' => '13800138000',
        'created_at' => '2024-01-01'
    ],
    '<EMAIL>' => [
        'id' => 2,
        'username' => 'user',
        'email' => '<EMAIL>',
        'password' => 'user123',
        'role' => 'user',
        'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        'nickname' => '普通用户',
        'phone' => '13900139000',
        'created_at' => '2024-01-15'
    ],
    '<EMAIL>' => [
        'id' => 3,
        'username' => 'vip',
        'email' => '<EMAIL>',
        'password' => 'vip123',
        'role' => 'vip',
        'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        'nickname' => 'VIP用户',
        'phone' => '13700137000',
        'created_at' => '2024-01-10'
    ]
];

// 处理注销请求
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_destroy();
    session_start();
    $_SESSION['flash_messages']['success'][] = '已成功退出登录！';

    // 清理输出缓冲并重定向
    ob_end_clean();
    header('Location: index.php');
    exit;
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'login') {
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';

        // 验证用户
        if (isset($users[$email]) && $users[$email]['password'] === $password) {
            // 登录成功
            $_SESSION['user'] = $users[$email];
            $_SESSION['user_id'] = $users[$email]['id'];
            $_SESSION['login_time'] = time();
            $_SESSION['flash_messages']['success'][] = '登录成功！欢迎回来，' . $users[$email]['nickname'];

            // 重定向到首页或指定页面
            $redirect = $_GET['redirect'] ?? 'index.php';

            // 清理输出缓冲并重定向
            ob_end_clean();
            header('Location: ' . $redirect);
            exit;
        } else {
            // 登录失败
            $_SESSION['flash_messages']['error'][] = '邮箱或密码错误！';
        }
    } elseif ($action === 'register') {
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        // 简单验证
        if (empty($username) || empty($email) || empty($password)) {
            $_SESSION['flash_messages']['error'][] = '请填写所有必填字段！';
        } elseif ($password !== $confirm_password) {
            $_SESSION['flash_messages']['error'][] = '两次输入的密码不一致！';
        } elseif (isset($users[$email])) {
            $_SESSION['flash_messages']['error'][] = '该邮箱已被注册！';
        } else {
            // 模拟注册成功
            $_SESSION['flash_messages']['success'][] = '注册成功！请使用邮箱和密码登录。';
        }
    }
}

// 页面信息设置
$page_title = '用户登录 - COSPlay购物网站';
$page_description = '登录您的COSPlay账户，享受个性化购物体验';

// 引入头部模板
require_once __DIR__ . '/../includes/header.php';
?>

<!-- 页面专用样式 -->
<link rel="stylesheet" href="css/layout-styles.css">

<!-- 紧急修复：确保文字可见 -->
<style>
/* 紧急修复：确保所有文字都可见 */
body {
    color: #333 !important;
    background-color: #f8f9fa !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p {
    color: #495057 !important;
}

.auth-title {
    color: #212529 !important;
}

.auth-subtitle {
    color: #6c757d !important;
}

.form-label {
    color: #212529 !important;
}

.form-input {
    color: #495057 !important;
    background-color: #ffffff !important;
}

.checkbox-text {
    color: #495057 !important;
}

.demo-role {
    color: #212529 !important;
}

.demo-desc {
    color: #6c757d !important;
}

.feature-text h4 {
    color: #212529 !important;
}

.feature-text p {
    color: #495057 !important;
}

.logo-text {
    color: #007bff !important;
}

.logo-tagline {
    color: #6c757d !important;
}

.nav-link-text {
    color: #495057 !important;
}

.logo {
    color: #007bff !important;
}

/* 确保按钮文字可见 */
.auth-submit-btn {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.auth-tab {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

.auth-tab.active {
    color: #007bff !important;
    background-color: #ffffff !important;
}

/* 确保导航栏文字可见 */
.navbar {
    background-color: #ffffff !important;
}

.nav-link {
    color: #495057 !important;
}

.nav-link:hover {
    color: #007bff !important;
}

/* 确保表单面板背景和文字可见 */
.auth-form-panel {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.auth-info-panel {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* 确保演示卡片文字可见 */
.demo-card {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    border: 1px solid #dee2e6 !important;
}

.demo-card:hover {
    background-color: #e9ecef !important;
}

/* 确保链接文字可见 */
.terms-link, .privacy-link, .forgot-password {
    color: #007bff !important;
}

.terms-link:hover, .privacy-link:hover, .forgot-password:hover {
    color: #0056b3 !important;
}
</style>

<div class="auth-page">
    <!-- 背景装饰 -->
    <div class="auth-background">
        <div class="bg-shape shape-1"></div>
        <div class="bg-shape shape-2"></div>
        <div class="bg-shape shape-3"></div>
        <div class="floating-icons">
            <i class="fas fa-mask floating-icon"></i>
            <i class="fas fa-star floating-icon"></i>
            <i class="fas fa-heart floating-icon"></i>
            <i class="fas fa-magic floating-icon"></i>
            <i class="fas fa-crown floating-icon"></i>
        </div>
    </div>

    <div class="container">
        <div class="auth-layout">
            <!-- 左侧信息面板 -->
            <div class="auth-info-panel">
                <div class="info-content">
                    <div class="brand-section">
                        <div class="brand-logo">
                            <span class="logo-text">COS<span class="logo-highlight">Play</span></span>
                            <div class="logo-tagline">角色扮演的世界</div>
                        </div>
                    </div>

                    <div class="features-list">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="feature-text">
                                <h4>精品服装</h4>
                                <p>高品质的角色扮演服装和配饰</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-text">
                                <h4>社区交流</h4>
                                <p>与同好分享经验和作品</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="feature-text">
                                <h4>会员特权</h4>
                                <p>专属折扣和优先服务</p>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial">
                        <div class="testimonial-content">
                            <p>"在这里找到了完美的角色服装，质量超出预期！"</p>
                            <div class="testimonial-author">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="用户头像">
                                <span>小雪 - VIP会员</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧表单面板 -->
            <div class="auth-form-panel">
                <div class="auth-container">
                    <div class="auth-form-content">
                        <div class="auth-header">
                            <h1 class="auth-title">欢迎回来</h1>
                            <p class="auth-subtitle">登录您的账户，继续您的COSplay之旅</p>
                        </div>

                        <div class="auth-tabs">
                            <button class="auth-tab active" data-tab="login">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>登录</span>
                            </button>
                            <button class="auth-tab" data-tab="register">
                                <i class="fas fa-user-plus"></i>
                                <span>注册</span>
                            </button>
                        </div>

                    <!-- 表单容器 -->
                    <div class="auth-form-container">
                        <!-- 登录表单 -->
                        <form class="auth-form active" id="login-form" method="POST">
                            <input type="hidden" name="action" value="login">

                            <div class="form-group">
                                <label for="login-email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    邮箱地址
                                </label>
                                <div class="input-wrapper">
                                    <input type="email" id="login-email" name="email" class="form-input"
                                           placeholder="请输入您的邮箱地址" required>
                                    <div class="input-focus-border"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="login-password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    密码
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" id="login-password" name="password" class="form-input"
                                           placeholder="请输入您的密码" required>
                                    <button type="button" class="password-toggle" data-target="login-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="input-focus-border"></div>
                                </div>
                            </div>

                            <div class="form-options">
                                <label class="checkbox-wrapper">
                                    <input type="checkbox" name="remember" class="checkbox-input">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">记住我</span>
                                </label>
                                <a href="#" class="forgot-password">
                                    <i class="fas fa-question-circle"></i>
                                    忘记密码？
                                </a>
                            </div>

                            <button type="submit" class="auth-submit-btn">
                                <span class="btn-text">
                                    <i class="fas fa-sign-in-alt"></i>
                                    立即登录
                                </span>
                                <div class="btn-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    登录中...
                                </div>
                            </button>

                            <div class="demo-accounts">
                                <div class="demo-header">
                                    <i class="fas fa-rocket"></i>
                                    <span>快速体验</span>
                                </div>
                                <p class="demo-text">选择演示账户快速登录</p>
                                <div class="demo-grid">
                                    <button type="button" class="demo-card admin" onclick="quickLogin('<EMAIL>', 'admin123')">
                                        <div class="demo-icon">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <div class="demo-info">
                                            <span class="demo-role">管理员</span>
                                            <span class="demo-desc">完整权限</span>
                                        </div>
                                    </button>

                                    <button type="button" class="demo-card vip" onclick="quickLogin('<EMAIL>', 'vip123')">
                                        <div class="demo-icon">
                                            <i class="fas fa-crown"></i>
                                        </div>
                                        <div class="demo-info">
                                            <span class="demo-role">VIP用户</span>
                                            <span class="demo-desc">专属特权</span>
                                        </div>
                                    </button>

                                    <button type="button" class="demo-card user" onclick="quickLogin('<EMAIL>', 'user123')">
                                        <div class="demo-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="demo-info">
                                            <span class="demo-role">普通用户</span>
                                            <span class="demo-desc">基础功能</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- 注册表单 -->
                        <form class="auth-form" id="register-form" method="POST">
                            <input type="hidden" name="action" value="register">

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="register-username" class="form-label">
                                        <i class="fas fa-user"></i>
                                        用户名
                                    </label>
                                    <div class="input-wrapper">
                                        <input type="text" id="register-username" name="username" class="form-input"
                                               placeholder="请输入用户名" required>
                                        <div class="input-focus-border"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    邮箱地址
                                </label>
                                <div class="input-wrapper">
                                    <input type="email" id="register-email" name="email" class="form-input"
                                           placeholder="请输入邮箱地址" required>
                                    <div class="input-focus-border"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    密码
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" id="register-password" name="password" class="form-input"
                                           placeholder="请输入密码（至少6位）" required>
                                    <button type="button" class="password-toggle" data-target="register-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="input-focus-border"></div>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-indicator">
                                        <div class="strength-bar">
                                            <div class="strength-fill"></div>
                                        </div>
                                        <span class="strength-text">密码强度：弱</span>
                                    </div>
                                    <div class="password-tips">
                                        <div class="tip-item" data-rule="length">
                                            <i class="fas fa-times"></i>
                                            <span>至少6个字符</span>
                                        </div>
                                        <div class="tip-item" data-rule="letter">
                                            <i class="fas fa-times"></i>
                                            <span>包含字母</span>
                                        </div>
                                        <div class="tip-item" data-rule="number">
                                            <i class="fas fa-times"></i>
                                            <span>包含数字</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-confirm-password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    确认密码
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" id="register-confirm-password" name="confirm_password"
                                           class="form-input" placeholder="请再次输入密码" required>
                                    <button type="button" class="password-toggle" data-target="register-confirm-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="input-focus-border"></div>
                                </div>
                                <div class="password-match">
                                    <span class="match-text"></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-wrapper agreement">
                                    <input type="checkbox" name="agree_terms" class="checkbox-input" required>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">
                                        我已阅读并同意
                                        <a href="#" class="terms-link">《用户协议》</a>
                                        和
                                        <a href="#" class="privacy-link">《隐私政策》</a>
                                    </span>
                                </label>
                            </div>

                            <button type="submit" class="auth-submit-btn">
                                <span class="btn-text">
                                    <i class="fas fa-user-plus"></i>
                                    立即注册
                                </span>
                                <div class="btn-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    注册中...
                                </div>
                            </button>

                            <div class="register-note">
                                <p>
                                    <i class="fas fa-info-circle"></i>
                                    注册即可享受会员专属折扣和优质服务
                                </p>
                            </div>
                        </form>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 优化的登录注册样式 -->
<style>
/* 页面基础样式 */
.auth-page {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 背景装饰 */
.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.2);
    font-size: 2rem;
    animation: floatIcon 8s ease-in-out infinite;
}

.floating-icon:nth-child(1) { top: 15%; left: 15%; animation-delay: 0s; }
.floating-icon:nth-child(2) { top: 25%; right: 20%; animation-delay: 1.6s; }
.floating-icon:nth-child(3) { bottom: 30%; left: 25%; animation-delay: 3.2s; }
.floating-icon:nth-child(4) { bottom: 20%; right: 30%; animation-delay: 4.8s; }
.floating-icon:nth-child(5) { top: 50%; left: 50%; animation-delay: 6.4s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.2; }
    50% { transform: translateY(-15px) scale(1.1); opacity: 0.4; }
}

/* 主布局 */
.auth-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
    position: relative;
    z-index: 2;
}

/* 左侧信息面板 */
.auth-info-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.info-content {
    max-width: 400px;
    color: white;
}

.brand-section {
    text-align: center;
    margin-bottom: 50px;
}

.brand-logo .logo-text {
    font-size: 3rem;
    font-weight: 700;
    color: white;
}

.logo-highlight {
    color: #ffd700;
}

.logo-tagline {
    font-size: 1.1rem;
    margin-top: 10px;
    opacity: 0.9;
}

.features-list {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(10px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.2rem;
}

.feature-text h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.feature-text p {
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

.testimonial {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
    backdrop-filter: blur(5px);
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 15px;
    font-size: 1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 10px;
}

.testimonial-author img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 右侧表单面板 */
.auth-form-panel {
    background: white;
    display: flex;
    align-items: stretch;
    justify-content: center;
    padding: 0;
    min-height: 100vh;
}

.auth-container {
    width: 100%;
    max-width: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 60px 40px;
    min-height: 100vh;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.auth-subtitle {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* 表单内容容器 */
.auth-form-content {
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

/* 标签页 */
.auth-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 30px;
}

.auth-tab {
    flex: 1;
    background: none;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-tab.active {
    background: white;
    color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.auth-tab:hover:not(.active) {
    color: #007bff;
}

/* 表单样式 */
.auth-form-container {
    position: relative;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-wrapper {
    position: relative;
}

.form-input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-input:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #007bff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-input:focus + .input-focus-border {
    width: 100%;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    background: #f0f0f0;
    color: #007bff;
}

/* 复选框样式 */
.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-input:checked + .checkbox-custom {
    background: #007bff;
    border-color: #007bff;
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    color: #666;
}

.terms-link, .privacy-link {
    color: #007bff;
    text-decoration: none;
}

.terms-link:hover, .privacy-link:hover {
    text-decoration: underline;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.forgot-password {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* 提交按钮 */
.auth-submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.auth-submit-btn:active {
    transform: translateY(0);
}

.btn-loading {
    display: none;
}

.auth-submit-btn.loading .btn-text {
    display: none;
}

.auth-submit-btn.loading .btn-loading {
    display: block;
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 10px;
}

.strength-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    background: #dc3545;
    transition: all 0.3s ease;
}

.strength-text {
    font-size: 12px;
    color: #666;
    min-width: 80px;
}

.password-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #dc3545;
    transition: color 0.3s ease;
}

.tip-item.valid {
    color: #28a745;
}

.tip-item i {
    font-size: 10px;
}

.tip-item.valid i::before {
    content: '\f00c';
}

/* 密码匹配指示器 */
.password-match {
    margin-top: 8px;
    font-size: 12px;
    min-height: 16px;
}

.match-text.success {
    color: #28a745;
}

.match-text.error {
    color: #dc3545;
}

/* 演示账户样式 */
.demo-accounts {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 16px;
    border: 1px solid #dee2e6;
}

.demo-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #495057;
}

.demo-text {
    text-align: center;
    margin: 0 0 20px 0;
    font-size: 14px;
    color: #6c757d;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
}

.demo-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.demo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.demo-card.admin {
    border-color: #dc3545;
}

.demo-card.admin:hover {
    background: #dc3545;
    color: white;
}

.demo-card.vip {
    border-color: #ffc107;
}

.demo-card.vip:hover {
    background: #ffc107;
    color: #212529;
}

.demo-card.user {
    border-color: #007bff;
}

.demo-card.user:hover {
    background: #007bff;
    color: white;
}

.demo-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.demo-card.admin .demo-icon {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.demo-card.vip .demo-icon {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.demo-card.user .demo-icon {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.demo-card:hover .demo-icon {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
}

.demo-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.demo-role {
    font-weight: 600;
    font-size: 12px;
}

.demo-desc {
    font-size: 10px;
    opacity: 0.7;
}

/* 注册说明 */
.register-note {
    margin-top: 20px;
    padding: 15px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 8px;
    text-align: center;
}

.register-note p {
    margin: 0;
    font-size: 14px;
    color: #0056b3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .auth-layout {
        grid-template-columns: 1fr;
    }

    .auth-info-panel {
        display: none;
    }

    .auth-form-panel {
        padding: 0;
    }

    .auth-container {
        padding: 40px 20px;
    }

    .auth-form-content {
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .auth-page {
        padding: 0;
    }

    .auth-container {
        padding: 30px 20px;
        min-height: 100vh;
    }

    .auth-form-content {
        max-width: 100%;
    }

    .demo-grid {
        grid-template-columns: 1fr;
    }

    .password-tips {
        grid-template-columns: 1fr;
    }

    .form-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 20px 15px;
    }

    .auth-title {
        font-size: 1.8rem;
    }

    .form-input {
        padding: 12px 16px;
        font-size: 14px;
    }

    .auth-submit-btn {
        padding: 12px 16px;
        font-size: 14px;
    }
}
</style>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查URL hash，如果是#register则自动切换到注册标签页
    function checkUrlHash() {
        if (window.location.hash === '#register') {
            // 切换到注册标签页
            const registerTab = document.querySelector('.auth-tab[data-tab="register"]');
            const loginTab = document.querySelector('.auth-tab[data-tab="login"]');
            const registerForm = document.getElementById('register-form');
            const loginForm = document.getElementById('login-form');

            if (registerTab && loginTab && registerForm && loginForm) {
                // 切换标签状态
                loginTab.classList.remove('active');
                registerTab.classList.add('active');

                // 切换表单
                loginForm.classList.remove('active');
                registerForm.classList.add('active');

                // 更新标题
                const title = document.querySelector('.auth-title');
                const subtitle = document.querySelector('.auth-subtitle');
                if (title && subtitle) {
                    title.textContent = '创建账户';
                    subtitle.textContent = '注册新账户，开始您的COSplay之旅';
                }
            }
        }
    }

    // 页面加载时检查hash
    checkUrlHash();

    // 监听hash变化
    window.addEventListener('hashchange', checkUrlHash);

    // 标签页切换
    document.querySelectorAll('.auth-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const tabType = this.dataset.tab;

            // 切换标签状态
            document.querySelector('.auth-tab.active').classList.remove('active');
            this.classList.add('active');

            // 切换表单
            document.querySelector('.auth-form.active').classList.remove('active');
            document.getElementById(tabType + '-form').classList.add('active');

            // 更新页面标题
            const title = tabType === 'login' ? '欢迎回来' : '创建账户';
            const subtitle = tabType === 'login' ? '登录您的账户，继续您的COSplay之旅' : '注册新账户，开始您的COSplay之旅';

            document.querySelector('.auth-title').textContent = title;
            document.querySelector('.auth-subtitle').textContent = subtitle;

            // 更新URL hash
            if (tabType === 'register') {
                window.history.replaceState(null, null, '#register');
            } else {
                window.history.replaceState(null, null, window.location.pathname);
            }
        });
    });

    // 密码显示/隐藏
    document.querySelectorAll('.password-toggle').forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const input = document.getElementById(targetId);
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // 增强的密码强度检测
    const passwordInput = document.getElementById('register-password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');
            const tipItems = document.querySelectorAll('.tip-item');

            let strength = 0;
            let strengthLabel = '弱';

            // 检查各项规则
            const rules = {
                length: password.length >= 6,
                letter: /[a-zA-Z]/.test(password),
                number: /[0-9]/.test(password)
            };

            // 更新提示项状态
            tipItems.forEach(item => {
                const rule = item.dataset.rule;
                const icon = item.querySelector('i');

                if (rules[rule]) {
                    item.classList.add('valid');
                    icon.className = 'fas fa-check';
                    strength++;
                } else {
                    item.classList.remove('valid');
                    icon.className = 'fas fa-times';
                }
            });

            // 额外强度检查
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;

            const percentage = Math.min((strength / 5) * 100, 100);
            strengthBar.style.width = percentage + '%';

            if (strength <= 2) {
                strengthLabel = '弱';
                strengthBar.style.backgroundColor = '#dc3545';
            } else if (strength <= 3) {
                strengthLabel = '中';
                strengthBar.style.backgroundColor = '#ffc107';
            } else {
                strengthLabel = '强';
                strengthBar.style.backgroundColor = '#28a745';
            }

            strengthText.textContent = '密码强度：' + strengthLabel;
        });
    }

    // 确认密码验证
    const confirmPasswordInput = document.getElementById('register-confirm-password');
    if (confirmPasswordInput && passwordInput) {
        function validatePasswordMatch() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const matchText = document.querySelector('.match-text');

            if (confirmPassword === '') {
                matchText.textContent = '';
                matchText.className = 'match-text';
                confirmPasswordInput.setCustomValidity('');
            } else if (password === confirmPassword) {
                matchText.textContent = '✓ 密码匹配';
                matchText.className = 'match-text success';
                confirmPasswordInput.setCustomValidity('');
            } else {
                matchText.textContent = '✗ 密码不匹配';
                matchText.className = 'match-text error';
                confirmPasswordInput.setCustomValidity('密码不匹配');
            }
        }

        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
        passwordInput.addEventListener('input', validatePasswordMatch);
    }

    // 表单提交增强
    const forms = document.querySelectorAll('.auth-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.auth-submit-btn');

            // 显示加载状态
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            // 如果是注册表单，进行额外验证
            if (this.id === 'register-form') {
                const password = this.querySelector('#register-password').value;
                const confirmPassword = this.querySelector('#register-confirm-password').value;
                const agreeTerms = this.querySelector('input[name="agree_terms"]').checked;

                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('两次输入的密码不一致！');
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    return;
                }

                if (!agreeTerms) {
                    e.preventDefault();
                    alert('请先同意用户协议和隐私政策！');
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    return;
                }

                if (password.length < 6) {
                    e.preventDefault();
                    alert('密码长度至少6位！');
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    return;
                }
            }
        });
    });

    // 快速登录功能
    window.quickLogin = function(email, password) {
        document.getElementById('login-email').value = email;
        document.getElementById('login-password').value = password;

        // 可选：自动提交表单
        if (confirm('确定要使用此账户登录吗？')) {
            document.getElementById('login-form').submit();
        }
    };
});
</script>

<?php
// 引入尾部模板
require_once __DIR__ . '/../includes/footer.php';
?>
